#!/usr/bin/env python3
"""
Quick fix to remove auto_roling_enabled: False from server config
"""

from pymongo import MongoClient

# Connect to MongoDB
client = MongoClient("mongodb://localhost:27017/")
db = client["ryzuo"]

# Your server ID
server_id = "1393129436974551050"

print("Fixing auto-roling configuration...")

# Remove the blocking field
collection = db['ryzuo-server-configs']
result = collection.update_one(
    {"server_id": server_id},
    {"$unset": {"auto_roling_enabled": ""}}
)

print(f"Removed auto_roling_enabled field: modified_count={result.modified_count}")

# Check the result
config = collection.find_one({"server_id": server_id})
print(f"Server config after fix: {config}")

# Check auto-roling settings
auto_collection = db['ryzuo-auto-roling']
auto_settings = auto_collection.find_one({"server_id": server_id})
print(f"Auto-roling settings: {auto_settings}")

print("Fix completed! Auto-roling should now work.")

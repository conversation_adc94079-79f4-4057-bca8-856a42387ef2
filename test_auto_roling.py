#!/usr/bin/env python3
"""
Test script for the freemium auto-roling system
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'website'))

from database import DatabaseManager
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_auto_roling_database():
    """Test auto-roling database operations"""
    
    # Initialize database (you'll need to provide your MongoDB URL)
    mongo_url = "mongodb://localhost:27017"  # Update this with your actual MongoDB URL
    db = DatabaseManager(mongo_url)
    
    test_server_id = 123456789  # Test server ID
    test_user_id = 987654321   # Test user ID
    
    print("Testing Auto-Roling Database Operations")
    print("=" * 50)
    
    # Test 1: Basic auto-role settings
    print("\n1. Testing basic auto-role settings...")
    success = db.set_auto_roling_settings(test_server_id, 123456, True)
    print(f"Set basic auto-role: {success}")
    
    settings = db.get_auto_roling_settings(test_server_id)
    print(f"Retrieved settings: {settings}")
    
    # Test 2: Multiple join roles (premium)
    print("\n2. Testing multiple join roles...")
    additional_roles = [111111, 222222, 333333]
    success = db.save_multiple_join_roles(test_server_id, additional_roles)
    print(f"Saved multiple roles: {success}")
    
    settings = db.get_auto_roling_settings(test_server_id)
    print(f"Settings with additional roles: {settings}")
    
    # Test 3: Offline protection (premium)
    print("\n3. Testing offline protection...")
    success = db.save_offline_protection_settings(test_server_id, True)
    print(f"Enabled offline protection: {success}")
    
    settings = db.get_auto_roling_settings(test_server_id)
    print(f"Settings with offline protection: {settings}")
    
    # Test 4: Premium enforcement - disable premium features
    print("\n4. Testing premium enforcement...")
    success = db.disable_offline_protection(test_server_id)
    print(f"Disabled offline protection: {success}")
    
    success = db.limit_join_roles_to_free_tier(test_server_id)
    print(f"Limited to free tier: {success}")
    
    settings = db.get_auto_roling_settings(test_server_id)
    print(f"Settings after premium removal: {settings}")
    
    # Test 5: User subscription check
    print("\n5. Testing user subscription...")
    is_subscribed = db.is_user_subscribed(test_user_id)
    print(f"User {test_user_id} is subscribed: {is_subscribed}")
    
    print("\n" + "=" * 50)
    print("Database tests completed!")

if __name__ == "__main__":
    try:
        test_auto_roling_database()
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()

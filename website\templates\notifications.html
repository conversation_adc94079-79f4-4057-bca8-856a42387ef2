{% extends "base.html" %}

{% block title %}Notifications{% endblock %}

{% block styles %}
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

    :root {
        --primary-color: #5865f2;
        --secondary-color: #7289da;
        --accent-color: #00d4ff;
        --bg-primary: #0a0a0f;
        --bg-secondary: #101218;
        --bg-tertiary: #1a1d29;
        --text-primary: #ffffff;
        --text-secondary: #b9bbbe;
        --text-muted: #72767d;
        --border-color: rgba(255, 255, 255, 0.1);
        --glow-primary: rgba(88, 101, 242, 0.5);
        --glow-accent: rgba(0, 212, 255, 0.3);
        --border-radius: 12px;
        --border-radius-sm: 8px;
        --box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background: var(--bg-primary);
        color: var(--text-primary);
        line-height: 1.6;
    }

    .notifications-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 2rem 1rem;
    }

    .notifications-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--border-color);
    }

    .notifications-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .clear-all-btn {
        background: linear-gradient(135deg, #dc2626, #b91c1c);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius-sm);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .clear-all-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
    }

    .notification-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .notification-item {
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        position: relative;
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .notification-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .notification-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--box-shadow);
        border-color: rgba(88, 101, 242, 0.3);
    }

    .notification-item:hover::before {
        opacity: 1;
    }

    .notification-item.unread {
        background: rgba(88, 101, 242, 0.05);
        border-left: 4px solid var(--primary-color);
    }

    .notification-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 1rem;
    }

    .notification-main {
        flex: 1;
        min-width: 0;
    }

    .notification-header-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .notification-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        flex-shrink: 0;
    }

    .notification-icon.info {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    }

    .notification-icon.success {
        background: linear-gradient(135deg, #10b981, #059669);
    }

    .notification-icon.warning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    .notification-icon.error {
        background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .notification-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
        flex: 1;
    }

    .notification-message {
        color: var(--text-secondary);
        line-height: 1.5;
        margin-bottom: 0.75rem;
    }

    .notification-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid var(--border-color);
    }

    .notification-time {
        color: var(--text-muted);
        font-size: 0.875rem;
        font-weight: 500;
    }

    .notification-actions {
        position: absolute;
        top: 1rem;
        right: 1rem;
        display: flex;
        gap: 0.5rem;
    }

    .notification-delete {
        background: rgba(220, 38, 38, 0.1);
        border: 1px solid rgba(220, 38, 38, 0.3);
        color: #ef4444;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.875rem;
    }

    .notification-delete:hover {
        background: rgba(220, 38, 38, 0.2);
        border-color: rgba(220, 38, 38, 0.5);
        transform: scale(1.1);
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: var(--bg-secondary);
        border-radius: var(--border-radius);
        border: 1px solid var(--border-color);
    }

    .empty-state i {
        font-size: 4rem;
        color: var(--text-muted);
        margin-bottom: 1.5rem;
        opacity: 0.5;
    }

    .empty-state h3 {
        color: var(--text-primary);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        color: var(--text-muted);
        margin: 0;
    }

    .loading-state {
        text-align: center;
        padding: 3rem;
    }

    .loading-state i {
        font-size: 2rem;
        color: var(--primary-color);
        margin-bottom: 1rem;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
        .notifications-container {
            padding: 1rem;
        }

        .notifications-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .notifications-title {
            font-size: 1.5rem;
            text-align: center;
        }

        .notification-item {
            padding: 1rem;
        }

        .notification-actions {
            position: static;
            margin-top: 1rem;
            justify-content: flex-end;
        }

        .notification-content {
            flex-direction: column;
            gap: 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="notifications-container">
    <div class="notifications-header">
        <h1 class="notifications-title">Notifications</h1>
        <div style="display: flex; gap: 0.5rem;">
            <button class="test-notification-btn" onclick="createTestNotification()" style="background: linear-gradient(135deg, #10b981, #059669); border: none; color: white; padding: 0.75rem 1.5rem; border-radius: 8px; font-weight: 600; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-plus"></i>
                Test Notification
            </button>
            <button class="clear-all-btn" onclick="clearAllNotifications()">
                <i class="fas fa-trash-alt"></i>
                Clear All
            </button>
        </div>
    </div>

    <div id="notificationsList" class="notification-list">
        <!-- Notifications will be loaded dynamically -->
        <div class="loading-state">
            <i class="fas fa-spinner"></i>
            <p>Loading notifications...</p>
        </div>
    </div>
</div>



<script>
document.addEventListener('DOMContentLoaded', function() {
    loadNotifications();
    updateNavbarBadge();
});

async function updateNavbarBadge() {
    try {
        const response = await fetch('/api/notifications/count', {
            headers: {
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        if (!response.ok) throw new Error('Network response was not ok');

        const data = await response.json();
        const badge = document.getElementById('notificationBadge');

        if (badge) {
            if (data.count > 0) {
                badge.textContent = data.count;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }
    } catch (error) {
        console.error('Error updating navbar badge:', error);
    }
}

async function createTestNotification() {
    try {
        const response = await fetch('/api/test-notification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        if (!response.ok) throw new Error('Network response was not ok');

        const data = await response.json();
        if (data.success) {
            // Reload notifications and update badge
            loadNotifications();
            updateNavbarBadge();
            alert('Test notification created successfully!');
        } else {
            alert('Failed to create test notification: ' + (data.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error creating test notification:', error);
        alert('Error creating test notification. Check console for details.');
    }
}

async function loadNotifications() {
    try {
        const response = await fetch('/api/notifications', {
            headers: {
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        if (!response.ok) throw new Error('Network response was not ok');

        const notifications = await response.json();
        const notificationsList = document.getElementById('notificationsList');

        if (notifications.length === 0) {
            notificationsList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-bell-slash"></i>
                    <h3>No notifications</h3>
                    <p>You're all caught up! No new notifications to show.</p>
                </div>`;
            return;
        }

        // Automatically mark all unread notifications as read when page loads
        const unreadNotifications = notifications.filter(notif => !notif.read);
        if (unreadNotifications.length > 0) {
            await markAllNotificationsAsRead(unreadNotifications);
        }

        notificationsList.innerHTML = notifications.map(notif => `
            <div class="notification-item" data-id="${notif._id}">
                <div class="notification-content">
                    <div class="notification-main">
                        <div class="notification-header-content">
                            <div class="notification-icon ${getNotificationTypeClass(notif.type)}">
                                <i class="fas fa-${getNotificationIcon(notif.type)}"></i>
                            </div>
                            <h3 class="notification-title">${escapeHtml(notif.title)}</h3>
                        </div>
                        <p class="notification-message">${escapeHtml(notif.message)}</p>
                        <div class="notification-footer">
                            <span class="notification-time">${formatDate(notif.created_at)}</span>
                        </div>
                    </div>
                </div>
                <div class="notification-actions">
                    <button class="notification-delete" onclick="deleteNotification('${notif._id}')" title="Delete notification">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `).join('');

        // Update navbar badge after marking as read
        updateNavbarBadge();
    } catch (error) {
        console.error('Error loading notifications:', error);
        const notificationsList = document.getElementById('notificationsList');
        notificationsList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Error loading notifications</h3>
                <p>Please refresh the page to try again.</p>
            </div>`;
    }
}

async function markAllNotificationsAsRead(notifications) {
    try {
        const response = await fetch('/api/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        if (!response.ok) throw new Error('Network response was not ok');
    } catch (error) {
        console.error('Error marking notifications as read:', error);
    }
}

async function deleteNotification(id) {
    try {
        const notificationElement = document.querySelector(`[data-id="${id}"]`);
        if (notificationElement) {
            notificationElement.style.opacity = '0.5';
            notificationElement.style.pointerEvents = 'none';
        }

        const response = await fetch(`/api/notifications/delete/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        if (!response.ok) throw new Error('Network response was not ok');

        if (notificationElement) {
            notificationElement.style.transform = 'translateX(100%)';
            setTimeout(() => {
                loadNotifications();
                updateNavbarBadge();
            }, 300);
        }
    } catch (error) {
        console.error('Error deleting notification:', error);
        // Restore the element if there was an error
        const notificationElement = document.querySelector(`[data-id="${id}"]`);
        if (notificationElement) {
            notificationElement.style.opacity = '1';
            notificationElement.style.pointerEvents = 'auto';
        }
    }
}

async function clearAllNotifications() {
    if (!confirm('Are you sure you want to clear all notifications? This action cannot be undone.')) return;

    try {
        const response = await fetch('/api/notifications/clear', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        if (!response.ok) throw new Error('Network response was not ok');
        loadNotifications();
        updateNavbarBadge();
    } catch (error) {
        console.error('Error clearing notifications:', error);
    }
}

// Helper functions
function escapeHtml(unsafe) {
    if (!unsafe) return '';
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

function formatDate(dateStr) {
    if (!dateStr) return 'Unknown';

    const date = new Date(dateStr);
    const now = new Date();
    const diff = now - date;

    // If less than 1 minute ago
    if (diff < 60 * 1000) return 'Just now';

    // If less than 1 hour ago
    if (diff < 60 * 60 * 1000) {
        const minutes = Math.floor(diff / (60 * 1000));
        return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
    }

    // If less than 24 hours ago
    if (diff < 24 * 60 * 60 * 1000) {
        const hours = Math.floor(diff / (60 * 60 * 1000));
        return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    }

    // If less than 7 days ago
    if (diff < 7 * 24 * 60 * 60 * 1000) {
        const days = Math.floor(diff / (24 * 60 * 60 * 1000));
        return `${days} day${days !== 1 ? 's' : ''} ago`;
    }

    // Otherwise show formatted date
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getNotificationTypeClass(type) {
    const typeMap = {
        'error': 'error',
        'warning': 'warning',
        'success': 'success',
        'info': 'info'
    };
    return typeMap[type] || 'info';
}

function getNotificationIcon(type) {
    const iconMap = {
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'success': 'check-circle',
        'info': 'info-circle'
    };
    return iconMap[type] || 'info-circle';
}
</script>
{% endblock %}

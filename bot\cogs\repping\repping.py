"""
Repping System Cog for Discord Bot
Handles status monitoring and role assignment based on trigger words
"""

import discord
from discord.ext import commands, tasks
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Set

logger = logging.getLogger(__name__)

class ReppingSystem(commands.Cog):
    """Cog for handling the repping system functionality"""
    
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
        
        # Store users who currently have the role to track changes per server
        self.server_users_with_role: Dict[int, Set[int]] = {}
        
    async def cog_load(self):
        """Called when the cog is loaded"""
        logger.info("Loading Repping System cog...")
        
        # Initialize role tracking for all servers
        await self.initialize_role_tracking()
        
        # Start the status checking task
        if not self.check_users_status.is_running():
            self.check_users_status.start()
            
        logger.info("Repping System cog loaded successfully")
        
    async def cog_unload(self):
        """Called when the cog is unloaded"""
        logger.info("Unloading Repping System cog...")
        
        # Stop the status checking task
        if self.check_users_status.is_running():
            self.check_users_status.cancel()
            
        logger.info("Repping System cog unloaded")
        
    async def initialize_role_tracking(self):
        """Initialize role tracking for all servers"""
        try:
            for guild in self.bot.guilds:
                if self.db.is_server_licensed(guild.id):
                    config = self.db.get_server_config(guild.id)
                    if config:
                        # Support both old and new field names for backward compatibility
                        role_id = config.get('repping_role_id') or config.get('role_id')
                        if role_id:
                            role = guild.get_role(role_id)
                            if role:
                                self.server_users_with_role[guild.id] = set(member.id for member in role.members)
                                logger.info(f"Initialized repping role tracking for {guild.name}: {len(role.members)} members")
        except Exception as e:
            logger.error(f"Error initializing role tracking: {e}")

    @tasks.loop(seconds=5)
    async def check_users_status(self):
        """Check all users every 5 seconds for the trigger word in their status across all licensed servers
        
        This function constantly monitors user statuses while respecting Discord rate limits:
        - Checks every 5 seconds for maximum responsiveness
        - Batches role operations to avoid rate limits
        - Uses small delays between operations
        - Skips offline users for efficiency
        """
        
        # Ensure we have the necessary intents to check user statuses
        if not self.bot.intents.presences:
            logger.warning("Presence intent not enabled - cannot check user statuses for repping system")
            return

        logger.debug("Starting repping check cycle...")

        # Batch role assignments and removals to avoid rate limits
        role_assignments = []
        role_removals = []

        try:
            for guild in self.bot.guilds:
                # Check if repping is a premium feature
                if self.db.is_premium_feature("repping"):
                    # Skip if server owner doesn't have subscription
                    if guild.owner_id and not self.db.is_server_premium_for_user(guild.id, guild.owner_id):
                        logger.debug(f"Skipping {guild.name} - repping is premium and no subscription")
                        continue

                config = self.db.get_server_config(guild.id)
                if not config:
                    logger.debug(f"Skipping {guild.name} - no configuration")
                    continue

                # Check if repping system is enabled
                if not self.db.is_system_enabled(guild.id, "repping"):
                    logger.debug(f"Skipping {guild.name} - repping system is disabled")
                    continue

                # Check if repping is explicitly disabled (for premium enforcement)
                if config.get('repping_enabled') is False:
                    logger.debug(f"Skipping {guild.name} - repping explicitly disabled")
                    continue

                # Support both old and new field names for backward compatibility
                role_id = config.get('repping_role_id') or config.get('role_id')
                channel_id = config.get('repping_channel_id') or config.get('channel_id')
                log_channel_id = config.get('log_channel_id')
                ignored_users = config.get('ignored_users', [])
                trigger_word = config.get('trigger_word')

                if not all([role_id, channel_id, trigger_word]):
                    logger.debug(f"Skipping {guild.name} - incomplete configuration")
                    continue

                role = guild.get_role(role_id)
                channel = guild.get_channel(channel_id)
                log_channel = guild.get_channel(log_channel_id) if log_channel_id else None

                if not role or not channel:
                    logger.debug(f"Skipping {guild.name} - role or channel not found")
                    continue
                
                # Initialize server tracking if not exists
                if guild.id not in self.server_users_with_role:
                    self.server_users_with_role[guild.id] = set()

                # Get users who actually have the role in Discord (live check)
                # Only filter by ignored users list, not by bot status
                actual_role_holders = set(member.id for member in role.members if member.id not in ignored_users)

                # Use the actual role holders for checking who currently has the role
                current_users_with_role = actual_role_holders

                # Check bot permissions for managing roles
                bot_member = guild.me
                can_manage_roles = guild.me.guild_permissions.manage_roles

                # If bot can't manage roles, skip this server entirely
                if not can_manage_roles:
                    if guild.id in self.server_users_with_role:
                        # Clear tracking since we can't manage roles
                        self.server_users_with_role[guild.id] = set()
                        logger.warning(f"Bot lacks 'Manage Roles' permission in {guild.name}, skipping role management")
                    continue

                # Check if bot's highest role is above the target role
                if role and bot_member.top_role <= role and bot_member != guild.owner:
                    if guild.id in self.server_users_with_role:
                        # Clear tracking since we can't manage this role
                        self.server_users_with_role[guild.id] = set()
                        logger.warning(f"Bot's highest role is not above role {role.name} in {guild.name}")
                    continue

                # Check all members in the guild (optimized for performance)
                for member in guild.members:
                    # Skip only ignored users (not bots automatically)
                    if member.id in ignored_users:
                        continue

                    # Skip offline users unless they have the role (for removal)
                    if member.status == discord.Status.offline:
                        if member.id in current_users_with_role:
                            role_removals.append((member, role, log_channel, guild.id))
                        continue  # Skip status checking for offline users

                    # Extract status text from custom status
                    status_text = ""
                    for activity in member.activities:
                        if activity.type == discord.ActivityType.custom:
                            if hasattr(activity, 'name') and activity.name:
                                status_text = activity.name
                                break
                            elif hasattr(activity, 'state') and activity.state:
                                status_text = activity.state
                                break

                    # Debug logging for status detection
                    if member.id in current_users_with_role:
                        logger.debug(f"Checking {member.display_name} (has role): status='{status_text}', trigger='{trigger_word}'")

                    # Check if status contains the trigger word (case insensitive, substring match)
                    if status_text:
                        status_lower = status_text.lower()
                        trigger_lower = trigger_word.lower()
                        has_trigger = trigger_lower in status_lower
                        user_has_role = member.id in current_users_with_role

                        # Queue role assignment if trigger found and user doesn't have role
                        if has_trigger and not user_has_role:
                            # Check if we can actually assign the role to this member
                            if member.top_role < bot_member.top_role or member == guild.owner:
                                role_assignments.append((member, role, channel, guild.id, trigger_word, status_text))
                                logger.info(f"Queued role assignment for {member.display_name} - trigger '{trigger_word}' found in status: '{status_text}'")
                            else:
                                logger.warning(f"Cannot assign role to {member.display_name} - their highest role is above mine")
                        # Queue role removal if trigger not found and user has role
                        elif not has_trigger and user_has_role:
                            # Check if we can actually remove the role from this member
                            if member.top_role < bot_member.top_role or member == guild.owner:
                                role_removals.append((member, role, log_channel, guild.id))
                                logger.info(f"Queued role removal for {member.display_name} - trigger '{trigger_word}' NOT found in status: '{status_text}'")
                            else:
                                logger.warning(f"Cannot remove role from {member.display_name} - their highest role is above mine")

                    # If no status text but user has role, queue for removal
                    elif member.id in current_users_with_role:
                        # Check if we can actually remove the role from this member
                        if member.top_role < bot_member.top_role or member == guild.owner:
                            role_removals.append((member, role, log_channel, guild.id))
                            logger.info(f"Queued role removal for {member.display_name} - no custom status found")
                        else:
                            logger.warning(f"Cannot remove role from {member.display_name} - their highest role is above mine")

            # Process batched role assignments with enhanced rate limiting
            if role_assignments:
                logger.info(f"Processing {len(role_assignments)} role assignments...")
                for i, (member, role, channel, guild_id, trigger_word, status_text) in enumerate(role_assignments):
                    try:
                        await self.handle_role_assignment(member, role, channel, guild_id, trigger_word)
                        logger.info(f"Assigned role to {member.display_name} - trigger '{trigger_word}' in status: '{status_text}'")
                        # Enhanced rate limiting: 50ms between assignments (20 per second max)
                        if i < len(role_assignments) - 1:  # Don't delay after the last one
                            await asyncio.sleep(0.05)
                    except discord.HTTPException as e:
                        if e.status == 429:  # Rate limited
                            logger.warning(f"Rate limited during role assignment, waiting longer...")
                            await asyncio.sleep(1.0)  # Wait 1 second if rate limited
                        else:
                            logger.error(f"HTTP error assigning role to {member.display_name}: {e}")
                    except Exception as e:
                        logger.error(f"Failed to assign role to {member.display_name}: {e}")

            # Process batched role removals with enhanced rate limiting
            if role_removals:
                logger.info(f"Processing {len(role_removals)} role removals...")
                for i, (member, role, log_channel, guild_id) in enumerate(role_removals):
                    try:
                        await self.handle_role_removal(member, role, log_channel, guild_id)
                        logger.info(f"Removed role from {member.display_name}")
                        # Enhanced rate limiting: 50ms between removals (20 per second max)
                        if i < len(role_removals) - 1:  # Don't delay after the last one
                            await asyncio.sleep(0.05)
                    except discord.HTTPException as e:
                        if e.status == 429:  # Rate limited
                            logger.warning(f"Rate limited during role removal, waiting longer...")
                            await asyncio.sleep(1.0)  # Wait 1 second if rate limited
                        else:
                            logger.error(f"HTTP error removing role from {member.display_name}: {e}")
                    except Exception as e:
                        logger.error(f"Failed to remove role from {member.display_name}: {e}")

            logger.debug(f"Repping check cycle completed. Assignments: {len(role_assignments)}, Removals: {len(role_removals)}")

        except Exception as e:
            logger.error(f"Error in check_users_status: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")

    async def handle_role_assignment(self, member, role, channel, guild_id, trigger_word):
        """Handle giving the role to a user with license validation and permission checks"""
        try:
            # Check if server owner still has a valid subscription
            guild = self.bot.get_guild(guild_id)
            if guild and guild.owner_id and not self.db.is_server_premium_for_user(guild_id, guild.owner_id):
                logger.info(f"Skipping role assignment for {member.display_name} - server {guild_id} subscription is no longer valid")
                # Clean up tracking
                if guild_id in self.server_users_with_role and member.id in self.server_users_with_role[guild_id]:
                    self.server_users_with_role[guild_id].remove(member.id)
                return

            # Check if user already has the role
            if role in member.roles:
                logger.debug(f"User {member.display_name} already has role {role.name}")
                return

            # Check if we can actually assign the role
            bot_member = member.guild.me
            if not bot_member.guild_permissions.manage_roles:
                logger.warning(f"Cannot assign role in {member.guild.name} - missing 'Manage Roles' permission")
                return

            if bot_member.top_role <= role and bot_member != member.guild.owner:
                logger.warning(f"Cannot assign role {role.name} in {member.guild.name} - role is above my highest role")
                return

            if member.top_role >= bot_member.top_role and member != member.guild.owner:
                logger.debug(f"Skipping role assignment for {member.display_name} - their highest role is above bot's")
                return

            # Add the role with error handling
            try:
                await member.add_roles(role, reason=f"Trigger word '{trigger_word}' detected in status")

                # Send notification
                embed = discord.Embed(
                    title="✅ Role Assigned",
                    description=f"You've been given the {role.mention} role because your status contained the trigger word `{trigger_word}`.",
                    color=discord.Color.green()
                )

                # Try to send notification, but don't fail if we can't
                # Don't use delete_after as it can interfere with sticky messages
                try:
                    await channel.send(f"{member.mention}", embed=embed)
                except Exception as e:
                    logger.warning(f"Could not send role assignment notification: {e}")

                # Update tracking
                if guild_id in self.server_users_with_role:
                    self.server_users_with_role[guild_id].add(member.id)
                else:
                    self.server_users_with_role[guild_id] = {member.id}

                logger.info(f"Assigned role {role.name} to {member.display_name} in guild {member.guild.name}")

                # Log to database for dashboard
                try:
                    self.db.log_bot_activity(
                        guild_id,
                        member.id,
                        f"{member.name}#{member.discriminator}",
                        f"Role assigned: {role.name}",
                        f"Trigger word '{trigger_word}' found in status",
                        "repping",
                        channel.id if channel else None
                    )

                    # Also log to Discord if enabled
                    logging_cog = self.bot.get_cog('LoggingSystem')
                    if logging_cog:
                        await logging_cog.log_bot_activity_to_channel(
                            guild_id, "ryzuo_logs", member.id, f"{member.name}#{member.discriminator}",
                            f"Role assigned: {role.name}",
                            f"Trigger word '{trigger_word}' found in status",
                            channel.id if channel else None
                        )
                except Exception as e:
                    logger.error(f"Failed to log role assignment: {e}")

            except discord.Forbidden:
                logger.error(f"Missing permissions to assign role {role.name} in {member.guild.name}")
                # Clean up tracking since we can't assign the role
                if guild_id in self.server_users_with_role and member.id in self.server_users_with_role[guild_id]:
                    self.server_users_with_role[guild_id].remove(member.id)

            except discord.HTTPException as e:
                if e.status == 429:  # Rate limited
                    logger.warning(f"Rate limited when assigning role to {member.display_name}, will retry next cycle")
                    # Don't update tracking if rate limited, so it will retry
                    raise  # Re-raise to be handled by the outer exception handler
                else:
                    logger.error(f"HTTP error assigning role to {member.display_name}: {e}")
                    # Clean up tracking on HTTP error
                    if guild_id in self.server_users_with_role and member.id in self.server_users_with_role[guild_id]:
                        self.server_users_with_role[guild_id].remove(member.id)

        except Exception as e:
            logger.error(f"Unexpected error in handle_role_assignment for {member.display_name}: {e}", exc_info=True)
            # Clean up tracking on error
            if guild_id in self.server_users_with_role and member.id in self.server_users_with_role[guild_id]:
                self.server_users_with_role[guild_id].remove(member.id)

    async def handle_role_removal(self, member, role, log_channel, guild_id):
        """Handle removing the role from a user with license validation and permission checks"""
        try:
            # Skip if server no longer has a valid license
            if not self.db.is_server_licensed(guild_id):
                logger.info(f"Skipping role removal for {member.display_name} - server {guild_id} license is no longer valid")
                # Clean up tracking
                if guild_id in self.server_users_with_role and member.id in self.server_users_with_role[guild_id]:
                    self.server_users_with_role[guild_id].remove(member.id)
                return

            # Check if user has the role
            if role not in member.roles:
                # Clean up tracking if user doesn't have role but is in our tracking
                if guild_id in self.server_users_with_role and member.id in self.server_users_with_role[guild_id]:
                    self.server_users_with_role[guild_id].remove(member.id)
                return

            # Check if we can actually remove the role
            bot_member = member.guild.me
            if not bot_member.guild_permissions.manage_roles:
                logger.warning(f"Cannot remove role in {member.guild.name} - missing 'Manage Roles' permission")
                # Clean up tracking since we can't manage roles
                if guild_id in self.server_users_with_role and member.id in self.server_users_with_role[guild_id]:
                    self.server_users_with_role[guild_id].remove(member.id)
                return

            if bot_member.top_role <= role and bot_member != member.guild.owner:
                logger.warning(f"Cannot remove role {role.name} in {member.guild.name} - role is above my highest role")
                # Clean up tracking since we can't manage this role
                if guild_id in self.server_users_with_role and member.id in self.server_users_with_role[guild_id]:
                    self.server_users_with_role[guild_id].remove(member.id)
                return

            if member.top_role >= bot_member.top_role and member != member.guild.owner:
                logger.warning(f"Cannot remove role from {member.display_name} - their highest role is above mine")
                # Clean up tracking since we can't modify this user
                if guild_id in self.server_users_with_role and member.id in self.server_users_with_role[guild_id]:
                    self.server_users_with_role[guild_id].remove(member.id)
                return

            # Remove the role with error handling
            try:
                await member.remove_roles(role, reason="Trigger word no longer in status, user went offline, or license was removed")

                # Update tracking
                if guild_id in self.server_users_with_role and member.id in self.server_users_with_role[guild_id]:
                    self.server_users_with_role[guild_id].remove(member.id)

                logger.info(f"Removed role {role.name} from {member.display_name} in guild {member.guild.name}")

                # Log to database for dashboard
                try:
                    self.db.log_bot_activity(
                        guild_id,
                        member.id,
                        f"{member.name}#{member.discriminator}",
                        f"Role removed: {role.name}",
                        "Trigger word no longer in status or user went offline",
                        "repping",
                        log_channel.id if log_channel else None
                    )

                    # Also log to Discord if enabled
                    logging_cog = self.bot.get_cog('LoggingSystem')
                    if logging_cog:
                        await logging_cog.log_bot_activity_to_channel(
                            guild_id, "ryzuo_logs", member.id, f"{member.name}#{member.discriminator}",
                            f"Role removed: {role.name}",
                            "Trigger word no longer in status or user went offline",
                            log_channel.id if log_channel else None
                        )
                except Exception as e:
                    logger.error(f"Failed to log role removal: {e}")

            except discord.Forbidden:
                logger.error(f"Missing permissions to remove role {role.name} in {member.guild.name}")
                # Clean up tracking since we can't remove the role
                if guild_id in self.server_users_with_role and member.id in self.server_users_with_role[guild_id]:
                    self.server_users_with_role[guild_id].remove(member.id)

            except discord.HTTPException as e:
                if e.status == 429:  # Rate limited
                    logger.warning(f"Rate limited when removing role from {member.display_name}, will retry next cycle")
                    # Don't update tracking if rate limited, so it will retry
                    raise  # Re-raise to be handled by the outer exception handler
                else:
                    logger.error(f"HTTP error removing role from {member.display_name}: {e}")
                    # Clean up tracking on HTTP error
                    if guild_id in self.server_users_with_role and member.id in self.server_users_with_role[guild_id]:
                        self.server_users_with_role[guild_id].remove(member.id)

        except Exception as e:
            logger.error(f"Unexpected error in handle_role_removal for {member.display_name}: {e}", exc_info=True)
            # Clean up tracking on error
            if guild_id in self.server_users_with_role and member.id in self.server_users_with_role[guild_id]:
                self.server_users_with_role[guild_id].remove(member.id)


async def setup(bot):
    """Setup function for the cog"""
    await bot.add_cog(ReppingSystem(bot))

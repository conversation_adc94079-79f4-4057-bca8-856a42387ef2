/**
 * Premium Features JavaScript Helper
 * Provides functions to check premium feature access and handle UI accordingly
 */

class PremiumFeatureManager {
    constructor() {
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    }

    /**
     * Check if a feature or sub-feature is premium and if user has access
     * @param {string} feature - The main feature name
     * @param {string} subfeature - Optional sub-feature name
     * @returns {Promise<Object>} - Result object with premium status and access info
     */
    async checkFeature(feature, subfeature = null) {
        const cacheKey = `${feature}${subfeature ? ':' + subfeature : ''}`;
        
        // Check cache first
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }

        try {
            const params = new URLSearchParams({ feature });
            if (subfeature) {
                params.append('subfeature', subfeature);
            }

            const response = await fetch(`/api/check-premium-feature?${params}`);
            const data = await response.json();

            if (data.success) {
                // Cache the result
                this.cache.set(cacheKey, {
                    data: data,
                    timestamp: Date.now()
                });
                return data;
            } else {
                throw new Error(data.error || 'Failed to check premium feature');
            }
        } catch (error) {
            console.error('Error checking premium feature:', error);
            return {
                success: false,
                is_premium: false,
                has_access: false,
                error: error.message
            };
        }
    }

    /**
     * Show/hide elements based on premium feature access
     * @param {string} feature - The main feature name
     * @param {string} subfeature - Optional sub-feature name
     * @param {string} selector - CSS selector for elements to show/hide
     * @param {boolean} showOnAccess - Whether to show (true) or hide (false) elements when user has access
     */
    async toggleElements(feature, subfeature, selector, showOnAccess = true) {
        const result = await this.checkFeature(feature, subfeature);
        const elements = document.querySelectorAll(selector);

        elements.forEach(element => {
            if (result.is_premium && !result.has_access) {
                // Premium feature without access
                if (showOnAccess) {
                    element.style.display = 'none';
                } else {
                    element.style.display = '';
                }
                element.classList.add('premium-locked');
            } else {
                // Free feature or premium with access
                if (showOnAccess) {
                    element.style.display = '';
                } else {
                    element.style.display = 'none';
                }
                element.classList.remove('premium-locked');
            }
        });

        return result;
    }

    /**
     * Add premium badges to elements
     * @param {string} feature - The main feature name
     * @param {string} subfeature - Optional sub-feature name
     * @param {string} selector - CSS selector for elements to add badges to
     */
    async addPremiumBadges(feature, subfeature, selector) {
        const result = await this.checkFeature(feature, subfeature);
        const elements = document.querySelectorAll(selector);

        elements.forEach(element => {
            // Remove existing badges
            const existingBadge = element.querySelector('.premium-badge');
            if (existingBadge) {
                existingBadge.remove();
            }

            if (result.is_premium) {
                const badge = document.createElement('span');
                badge.className = 'premium-badge';
                badge.innerHTML = '<i class="fas fa-crown"></i> Premium';
                badge.style.cssText = `
                    background: linear-gradient(135deg, #ffd700, #ffed4e);
                    color: #000;
                    padding: 2px 8px;
                    border-radius: 12px;
                    font-size: 0.75rem;
                    font-weight: 600;
                    margin-left: 8px;
                    display: inline-flex;
                    align-items: center;
                    gap: 4px;
                `;
                element.appendChild(badge);

                if (!result.has_access) {
                    element.classList.add('premium-locked');
                    element.style.opacity = '0.6';
                }
            }
        });

        return result;
    }

    /**
     * Handle premium feature clicks
     * @param {string} feature - The main feature name
     * @param {string} subfeature - Optional sub-feature name
     * @param {Function} callback - Function to call if user has access
     * @param {string} upgradeUrl - URL to redirect to for upgrade (default: /shop)
     */
    async handleFeatureClick(feature, subfeature, callback, upgradeUrl = '/shop') {
        const result = await this.checkFeature(feature, subfeature);

        if (result.is_premium && !result.has_access) {
            // Show premium upgrade modal or redirect
            if (confirm(`This is a premium feature. Would you like to upgrade to access it?`)) {
                window.location.href = upgradeUrl;
            }
            return false;
        } else {
            // User has access, execute callback
            if (typeof callback === 'function') {
                callback();
            }
            return true;
        }
    }

    /**
     * Initialize premium feature UI for a container
     * @param {string} containerSelector - CSS selector for the container
     * @param {Object} featureMap - Map of selectors to feature/subfeature names
     */
    async initializeContainer(containerSelector, featureMap) {
        const container = document.querySelector(containerSelector);
        if (!container) return;

        for (const [selector, featureInfo] of Object.entries(featureMap)) {
            const { feature, subfeature, action = 'badge' } = featureInfo;

            switch (action) {
                case 'badge':
                    await this.addPremiumBadges(feature, subfeature, selector);
                    break;
                case 'hide':
                    await this.toggleElements(feature, subfeature, selector, false);
                    break;
                case 'show':
                    await this.toggleElements(feature, subfeature, selector, true);
                    break;
            }
        }
    }

    /**
     * Clear the cache
     */
    clearCache() {
        this.cache.clear();
    }
}

// Create global instance
window.premiumFeatures = new PremiumFeatureManager();

// Helper functions for backward compatibility
window.checkPremiumFeature = (feature, subfeature) => window.premiumFeatures.checkFeature(feature, subfeature);
window.handlePremiumClick = (feature, subfeature, callback) => window.premiumFeatures.handleFeatureClick(feature, subfeature, callback);

// Auto-initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    // Add premium styling
    const style = document.createElement('style');
    style.textContent = `
        .premium-locked {
            position: relative;
            cursor: not-allowed !important;
        }
        
        .premium-locked::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.1);
            pointer-events: none;
            border-radius: inherit;
        }
        
        .premium-badge {
            animation: premiumGlow 2s ease-in-out infinite alternate;
        }
        
        @keyframes premiumGlow {
            from { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
            to { box-shadow: 0 0 15px rgba(255, 215, 0, 0.8); }
        }
    `;
    document.head.appendChild(style);
});

{% extends "base.html" %}

{% block title %}Auto-Roling Configuration - {{ server_info.name }}{% endblock %}

{% block styles %}
<style>
    .config-header {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
    }

    .config-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6, #6366f1);
    }

    .config-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .config-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
        margin-bottom: 0;
        opacity: 0.9;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        backdrop-filter: blur(20px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        border-color: #3b82f6;
        box-shadow: 0 15px 40px rgba(59, 130, 246, 0.15);
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6, #6366f1);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .feature-card:hover::before {
        opacity: 1;
    }
</style>
{% endblock %}

{% block content %}
<!-- Include Premium Features JavaScript -->
<script src="{{ url_for('static', filename='js/premium-features.js') }}"></script>

<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        {% include 'sidebar.html' %}
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <!-- Configuration Header -->
        <div class="config-header">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h1 class="config-title">
                        <i class="fas fa-user-plus text-primary"></i>
                        Auto-Roling Configuration
                    </h1>
                    <p class="config-subtitle">Automatically assign roles to new members</p>
                </div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>

        <!-- System Status Card -->
        <div class="feature-card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>System Status</h5>
                {% if auto_roling_settings and auto_roling_settings.enabled and not auto_roling_settings.permission_error %}
                <span class="badge bg-success">Active</span>
                {% elif auto_roling_settings and auto_roling_settings.permission_error %}
                <span class="badge bg-danger">Permission Error</span>
                {% elif auto_roling_settings and not auto_roling_settings.enabled %}
                <span class="badge bg-warning">Disabled</span>
                {% else %}
                <span class="badge bg-secondary">Not Configured</span>
                {% endif %}
            </div>
            <div class="card-body">
                {% if auto_roling_settings and auto_roling_settings.permission_error %}
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Permission Error:</strong> {{ auto_roling_settings.last_error or "The bot doesn't have permission to assign the configured role." }}
                    <br><small class="text-muted">Auto-roling has been automatically disabled. Please fix the permissions and clear this error.</small>
                </div>
                {% elif auto_roling_settings and auto_roling_settings.enabled %}
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Auto-roling is active!</strong> New members will automatically receive the configured role.
                </div>
                {% elif auto_roling_settings and not auto_roling_settings.enabled %}
                <div class="alert alert-warning">
                    <i class="fas fa-pause-circle me-2"></i>
                    <strong>Auto-roling is disabled.</strong> New members will not receive roles automatically.
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Auto-roling is not configured.</strong> Set up a role below to get started.
                </div>
                {% endif %}
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <!-- Configuration Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Configuration</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <input type="hidden" name="action" value="configure">
                            
                            <div class="mb-4">
                                <label for="role_id" class="form-label">Auto-Role</label>
                                <select class="form-select" id="role_id" name="role_id" required>
                                    <option value="">Select a role...</option>
                                    <!-- Roles will be populated by JavaScript -->
                                </select>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    This role will be automatically assigned to new members when they join the server.
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enabled" name="enabled" 
                                           {% if auto_roling_settings and auto_roling_settings.enabled %}checked{% endif %}>
                                    <label class="form-check-label" for="enabled">
                                        Enable Auto-Roling
                                    </label>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    When enabled, new members will automatically receive the selected role upon joining.
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Configuration
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Quick Actions -->
                {% if auto_roling_settings %}
                <div class="feature-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                    <input type="hidden" name="action" value="toggle">
                                    <input type="hidden" name="enabled" value="{% if auto_roling_settings.enabled %}{% else %}on{% endif %}">
                                    
                                    {% if auto_roling_settings.enabled %}
                                    <button type="submit" class="btn btn-warning w-100" 
                                            onclick="return confirm('Are you sure you want to disable auto-roling?')">
                                        <i class="fas fa-pause me-2"></i>Disable Auto-Roling
                                    </button>
                                    {% else %}
                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="fas fa-play me-2"></i>Enable Auto-Roling
                                    </button>
                                    {% endif %}
                                </form>
                            </div>
                            
                            {% if auto_roling_settings.permission_error %}
                            <div class="col-md-6">
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                    <input type="hidden" name="action" value="clear_error">

                                    <button type="submit" class="btn btn-info w-100">
                                        <i class="fas fa-eraser me-2"></i>Clear Permission Error
                                    </button>
                                </form>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <div class="col-lg-4">
                <!-- Information Card -->
                <div class="feature-card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-question-circle me-2"></i>How Auto-Roling Works</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">Auto-roling automatically assigns roles to new members:</p>
                        <ul class="mb-3">
                            <li><strong>On Join:</strong> New members get the role immediately</li>
                            <li><strong>Offline Protection:</strong> Checks every few seconds for missing roles</li>
                            <li><strong>Permission Safe:</strong> Pauses if bot lacks permissions</li>
                            <li><strong>Hierarchy Aware:</strong> Only assigns roles below bot's role</li>
                        </ul>
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-shield-alt me-1"></i>
                            <small><strong>Note:</strong> The bot must have "Manage Roles" permission and the auto-role must be below the bot's highest role.</small>
                        </div>
                    </div>
                </div>

                <!-- Premium Features Card -->
                <div class="feature-card mt-4" id="premium-features-card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-crown me-2"></i>Premium Features</h6>
                    </div>
                    <div class="card-body">
                        <div class="premium-feature" data-feature="auto_roling" data-subfeature="multiple_roles">
                            <h6 class="premium-feature-title">Multiple Roles Assignment</h6>
                            <p class="text-muted small mb-2">Assign multiple roles to new members at once</p>
                            <button class="btn btn-sm btn-outline-primary premium-feature-btn" disabled>
                                <i class="fas fa-users me-1"></i>Configure Multiple Roles
                            </button>
                        </div>

                        <hr class="my-3">

                        <div class="premium-feature" data-feature="auto_roling" data-subfeature="advanced_triggers">
                            <h6 class="premium-feature-title">Advanced Triggers</h6>
                            <p class="text-muted small mb-2">Set up conditional role assignment based on user activity</p>
                            <button class="btn btn-sm btn-outline-primary premium-feature-btn" disabled>
                                <i class="fas fa-cogs me-1"></i>Setup Advanced Triggers
                            </button>
                        </div>

                        <hr class="my-3">

                        <div class="premium-feature" data-feature="auto_roling" data-subfeature="role_hierarchy">
                            <h6 class="premium-feature-title">Role Hierarchy Management</h6>
                            <p class="text-muted small mb-2">Automatically manage role hierarchy and permissions</p>
                            <button class="btn btn-sm btn-outline-primary premium-feature-btn" disabled>
                                <i class="fas fa-layer-group me-1"></i>Manage Hierarchy
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialize premium features for auto-roling page
document.addEventListener('DOMContentLoaded', async function() {
    // Define feature mappings for this page
    const featureMap = {
        '.premium-feature[data-feature="auto_roling"][data-subfeature="multiple_roles"]': {
            feature: 'auto_roling',
            subfeature: 'multiple_roles',
            action: 'badge'
        },
        '.premium-feature[data-feature="auto_roling"][data-subfeature="advanced_triggers"]': {
            feature: 'auto_roling',
            subfeature: 'advanced_triggers',
            action: 'badge'
        },
        '.premium-feature[data-feature="auto_roling"][data-subfeature="role_hierarchy"]': {
            feature: 'auto_roling',
            subfeature: 'role_hierarchy',
            action: 'badge'
        }
    };

    // Initialize premium features
    await window.premiumFeatures.initializeContainer('#premium-features-card', featureMap);

    // Add click handlers for premium feature buttons
    document.querySelectorAll('.premium-feature-btn').forEach(btn => {
        btn.addEventListener('click', async function(e) {
            e.preventDefault();

            const featureDiv = this.closest('.premium-feature');
            const feature = featureDiv.dataset.feature;
            const subfeature = featureDiv.dataset.subfeature;

            const hasAccess = await window.premiumFeatures.handleFeatureClick(
                feature,
                subfeature,
                () => {
                    // This would be the actual feature implementation
                    alert(`${subfeature.replace('_', ' ')} feature would be configured here!`);
                }
            );

            if (hasAccess) {
                this.disabled = false;
                this.classList.remove('btn-outline-primary');
                this.classList.add('btn-primary');
            }
        });
    });
});

let serverRoles = [];

// Load Discord data for dropdowns
document.addEventListener('DOMContentLoaded', function() {
    loadServerRoles();
});

async function loadServerRoles() {
    try {
        const response = await fetch('/api/roles');
        const data = await response.json();

        console.log('Roles API response:', data);

        if (Array.isArray(data)) {
            serverRoles = data;
            console.log('Loaded server roles:', serverRoles.length);
            populateRoleDropdown();
        } else if (data.error) {
            console.error('Error from roles API:', data.error);
        }
    } catch (error) {
        console.error('Error loading server roles:', error);
    }
}

function populateRoleDropdown() {
    const roleSelect = document.getElementById('role_id');
    if (!roleSelect) {
        console.error('Role select element not found!');
        return;
    }

    console.log('Populating role dropdown...');
    console.log('Available roles:', serverRoles.length);

    // Clear existing options
    roleSelect.innerHTML = '<option value="">Select a role...</option>';

    // Filter to only show non-managed roles (the API already filters out @everyone)
    const assignableRoles = serverRoles.filter(role => !role.managed);

    console.log('Assignable roles:', assignableRoles.length);

    // Sort roles by position (highest first)
    assignableRoles.sort((a, b) => b.position - a.position);

    // Add all assignable roles to dropdown
    assignableRoles.forEach(role => {
        const option = document.createElement('option');
        option.value = role.id;
        option.textContent = role.name;

        // Set color if available
        if (role.color && role.color !== 0) {
            option.style.color = `#${role.color.toString(16).padStart(6, '0')}`;
        }

        // Select current role if editing
        {% if auto_roling_settings and auto_roling_settings.role_id %}
        if (role.id === '{{ auto_roling_settings.role_id }}') {
            option.selected = true;
        }
        {% endif %}

        roleSelect.appendChild(option);
    });
}
</script>
{% endblock %}

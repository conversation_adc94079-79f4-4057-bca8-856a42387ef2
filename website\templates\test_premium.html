{% extends "base.html" %}

{% block title %}Test Premium Enforcement{% endblock %}

{% block content %}
<!-- Include Navbar -->
{% include 'navbar.html' %}

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            {% include 'sidebar.html' %}
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <div class="main-content">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h3><i class="fas fa-bug me-2"></i>Test Premium Enforcement</h3>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Warning:</strong> This will forcibly disable all premium features for your servers, regardless of subscription status. Use for testing only.
                                </div>

                                <button id="testBtn" class="btn btn-danger btn-lg">
                                    <i class="fas fa-play me-2"></i>
                                    Run Premium Enforcement Test
                                </button>

                                <div id="results" class="mt-4" style="display: none;">
                                    <h5>Test Results:</h5>
                                    <div id="resultContent" class="alert alert-info"></div>
                                </div>

                                <div id="logs" class="mt-4">
                                    <h5>Debug Information:</h5>
                                    <div id="logContent" class="bg-dark text-light p-3" style="font-family: monospace; max-height: 400px; overflow-y: auto;">
                                        Click the test button to see debug information...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('testBtn').addEventListener('click', function() {
    const btn = this;
    const results = document.getElementById('results');
    const resultContent = document.getElementById('resultContent');
    const logContent = document.getElementById('logContent');
    
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Running Test...';
    
    logContent.innerHTML = 'Starting premium enforcement test...\n';
    
    fetch('/api/test-premium-enforcement', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-play me-2"></i>Run Premium Enforcement Test';
        
        results.style.display = 'block';
        
        if (data.success) {
            resultContent.className = 'alert alert-success';
            resultContent.innerHTML = `
                <strong>Test Completed Successfully!</strong><br>
                ${data.message}<br><br>
                <strong>Details:</strong><br>
                • Premium Features: ${data.premium_features.join(', ') || 'None'}<br>
                • Has Premium: ${data.has_premium ? 'Yes' : 'No'}<br>
                • Owned Servers: ${data.owned_servers}<br>
                • Features Disabled: ${data.disabled_count}
            `;
            
            logContent.innerHTML += `SUCCESS: ${data.message}\n`;
            logContent.innerHTML += `Premium features: ${JSON.stringify(data.premium_features)}\n`;
            logContent.innerHTML += `User has premium: ${data.has_premium}\n`;
            logContent.innerHTML += `Owned servers: ${data.owned_servers}\n`;
            logContent.innerHTML += `Disabled count: ${data.disabled_count}\n`;
        } else {
            resultContent.className = 'alert alert-danger';
            resultContent.innerHTML = `<strong>Test Failed:</strong> ${data.error}`;
            logContent.innerHTML += `ERROR: ${data.error}\n`;
        }
        
        // Scroll to bottom of logs
        logContent.scrollTop = logContent.scrollHeight;
    })
    .catch(error => {
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-play me-2"></i>Run Premium Enforcement Test';
        
        results.style.display = 'block';
        resultContent.className = 'alert alert-danger';
        resultContent.innerHTML = `<strong>Request Failed:</strong> ${error.message}`;
        
        logContent.innerHTML += `NETWORK ERROR: ${error.message}\n`;
        logContent.scrollTop = logContent.scrollHeight;
    });
});
</script>

<style>
.main-content {
    padding: 2rem;
}

.card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px 10px 0 0;
}

.btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border: none;
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

#logContent {
    border-radius: 5px;
    font-size: 0.9rem;
    line-height: 1.4;
}
</style>
{% endblock %}

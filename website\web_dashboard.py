import asyncio
import json
import logging
import os
import stripe
import hmac
import hashlib
import time
import threading
from datetime import datetime, timedelta, timezone
from functools import wraps
from typing import Optional, Dict, Any, List

from dotenv import load_dotenv
from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify, send_from_directory, g
from flask_wtf.csrf import CSRFProtect, generate_csrf
import jwt
import requests
import discord
from bson import ObjectId

# Load environment variables first
load_dotenv()

# Then import local modules
try:
    from oauth2 import get_oauth_url, get_token, get_user_info, get_user_guilds, create_jwt_token, login_required, get_license_keys
    from database import DatabaseManager
    from shard_data import shard_data_manager
    from shard_communication import shard_comm
    from statistics_aggregator import initialize_aggregator, get_aggregator
except ImportError as e:
    logging.error(f"Failed to import required modules: {e}")
    raise

# Load configuration from environment variables
MONGO_URL = os.getenv('MONGO_URL')
SECRET_KEY = os.getenv('SECRET_KEY')

if not MONGO_URL or not SECRET_KEY:
    raise ValueError("Missing required environment variables. Please check your .env file.")

# Discord OAuth2 configuration
DISCORD_CLIENT_ID = os.getenv('DISCORD_CLIENT_ID')
DISCORD_CLIENT_SECRET = os.getenv('DISCORD_CLIENT_SECRET')
DISCORD_REDIRECT_URI = os.getenv('DISCORD_REDIRECT_URI')

if not all([DISCORD_CLIENT_ID, DISCORD_CLIENT_SECRET]):
    raise ValueError("Missing required Discord OAuth2 configuration. Set DISCORD_CLIENT_ID and DISCORD_CLIENT_SECRET environment variables.")

# Stripe configuration
STRIPE_SECRET_KEY = os.getenv('STRIPE_SECRET_KEY')
STRIPE_PUBLISHABLE_KEY = os.getenv('STRIPE_PUBLISHABLE_KEY')
STRIPE_WEBHOOK_SECRET = os.getenv('STRIPE_WEBHOOK_SECRET')  # Add this to .env for webhook verification

if not all([STRIPE_SECRET_KEY, STRIPE_PUBLISHABLE_KEY]):
    raise ValueError("Missing required Stripe configuration. Set STRIPE_SECRET_KEY and STRIPE_PUBLISHABLE_KEY environment variables.")

# Configure Stripe
stripe.api_key = STRIPE_SECRET_KEY

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Utility function for ObjectId serialization
def convert_objectids_to_strings(obj):
    """Recursively convert ObjectId instances to strings for JSON serialization"""
    if isinstance(obj, list):
        return [convert_objectids_to_strings(item) for item in obj]
    elif isinstance(obj, dict):
        return {key: convert_objectids_to_strings(value) for key, value in obj.items()}
    elif isinstance(obj, ObjectId):
        return str(obj)
    else:
        return obj

def get_session_size():
    """Get the current session size in bytes"""
    try:
        import pickle
        return len(pickle.dumps(dict(session)))
    except Exception:
        return 0

def cleanup_session():
    """Clean up session data to reduce size"""
    try:
        # Remove large guild data if session is too big
        if 'guilds' in session and len(session['guilds']) > 30:
            session['guilds'] = session['guilds'][:30]

        # Remove old flash messages
        if '_flashes' in session and len(session['_flashes']) > 5:
            session['_flashes'] = session['_flashes'][-5:]

        session.modified = True
        logger.info(f"Session cleaned up, new size: {get_session_size()} bytes")
    except Exception as e:
        logger.warning(f"Error cleaning up session: {e}")

# Initialize Flask app
app = Flask(__name__)
app.secret_key = SECRET_KEY

# Configure session settings to handle large sessions better
app.config['SESSION_COOKIE_SECURE'] = os.getenv('FLASK_ENV') == 'production'  # Use HTTPS only in production
app.config['SESSION_COOKIE_HTTPONLY'] = True  # Prevent XSS
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'  # CSRF protection
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=7)  # Session expires in 7 days

# Initialize CSRF protection
csrf = CSRFProtect(app)

# Add CSRF token to all templates
@app.context_processor
def inject_csrf_token():
    return dict(csrf_token=generate_csrf)

# Add custom Jinja2 filters
@app.template_filter('number_format')
def number_format(value):
    """Format number with commas as thousand separators"""
    try:
        return "{:,}".format(int(value))
    except (ValueError, TypeError):
        return value

@app.template_filter('time_ago')
def time_ago(value):
    """Convert datetime to time ago format"""
    if not value:
        return "Never"

    try:
        from datetime import datetime, timezone

        # Ensure value is timezone-aware
        if isinstance(value, datetime):
            if value.tzinfo is None:
                value = value.replace(tzinfo=timezone.utc)
        else:
            # If it's a string, try to parse it
            if isinstance(value, str):
                try:
                    value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                except:
                    return "Unknown"
            else:
                return "Unknown"

        now = datetime.now(timezone.utc)
        diff = now - value

        if diff.days > 0:
            return f"{diff.days} day{'s' if diff.days != 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        else:
            return "Just now"
    except Exception:
        return "Unknown"

# Initialize database
db = DatabaseManager(MONGO_URL)

# Initialize statistics aggregator
stats_aggregator = initialize_aggregator(db)

def log_dashboard_activity_comprehensive(server_id: int, user_id: int, username: str, action: str,
                                       details: str = None, category: str = "general", channel_id: int = None):
    """Log dashboard activity to both database and Discord if enabled"""
    try:
        # Always log to database
        db.log_bot_activity(server_id, user_id, username, action, details, category, channel_id)

        # Also log to Discord by calling the enhanced database method
        db.log_bot_activity_with_discord(server_id, user_id, username, action, details, category, channel_id)

    except Exception as e:
        logger.error(f"Error in comprehensive dashboard activity logging: {e}")

# Bot reference removed - using shard communication instead
# All Discord interactions now go through the shard communication service

def get_server_channels_via_shard(server_id: str):
    """Get server channels via shard communication"""
    try:
        return shard_comm.run_async(shard_comm.get_server_channels(server_id))
    except Exception as e:
        logger.error(f"Error getting channels from shard: {e}")
        return []

def get_server_members_via_shard(server_id: str):
    """Get server members via shard communication"""
    try:
        return shard_comm.run_async(shard_comm.get_server_members(server_id))
    except Exception as e:
        logger.error(f"Error getting members from shard: {e}")
        return []

def get_server_roles_via_shard(server_id: str):
    """Get server roles via shard communication"""
    try:
        return shard_comm.run_async(shard_comm.get_server_roles(server_id))
    except Exception as e:
        logger.error(f"Error getting roles from shard: {e}")
        return []

def check_bot_in_server_via_shard(server_id: str):
    """Check if bot is in server via shard communication"""
    try:
        return shard_comm.run_async(shard_comm.check_bot_in_server(server_id))
    except Exception as e:
        logger.error(f"Error checking bot status from shard: {e}")
        return False

def get_server_statistics_via_shard(server_id: str):
    """Get server statistics via shard communication"""
    try:
        return shard_comm.run_async(shard_comm.get_server_statistics(server_id))
    except Exception as e:
        logger.error(f"Error getting statistics from shard: {e}")
        return {}

@app.before_request
def initialize_database():
    """Initialize database connection"""
    if not hasattr(initialize_database, 'initialized'):
        try:
            db.connect()
            logger.info("Web dashboard database connected successfully")
            initialize_database.initialized = True
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")

# Use the login_required decorator from oauth2.py
# We'll keep the old require_auth for backward compatibility
def require_auth(f):
    """Decorator to require authentication"""
    # Use wraps to preserve the original function's metadata
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        return f(*args, **kwargs)
    return decorated_function

# ========== PREMIUM FEATURE VALIDATION ==========

def is_premium_feature(feature_name: str) -> bool:
    """Check if a feature requires premium subscription - loads from database"""
    try:
        # Get premium features from database (set by admin panel)
        features = db.get_features()
        premium_features = [f['name'] for f in features if f.get('is_premium', False)]

        # Return based on database state - if no features are marked premium, then nothing is premium
        return feature_name in premium_features

    except Exception as e:
        logger.error(f"Error checking premium feature status: {e}")
        # Only fall back on database errors, not when database is empty
        # This ensures admin panel settings are respected
        default_premium = []  # Default to no premium features on error
        return feature_name in default_premium

def is_premium_subfeature(feature_name: str, subfeature_name: str) -> bool:
    """Check if a specific sub-feature within a feature requires premium subscription"""
    try:
        # Get granular premium features from database
        features = db.get_features()

        # Look for the specific feature
        for feature in features:
            if feature['name'] == feature_name:
                subfeatures = feature.get('premium_subfeatures', [])
                return subfeature_name in subfeatures

        # If feature not found in database, check default granular rules
        default_premium_subfeatures = {
            'auto_roling': ['multiple_roles', 'advanced_triggers', 'role_hierarchy'],
            'repping': ['custom_rewards', 'advanced_tracking', 'role_stacking'],
            'tempvoice': ['custom_permissions', 'advanced_settings', 'multiple_channels'],
            'music': ['premium_quality', 'playlist_management', 'advanced_filters'],
            'vent': ['custom_categories', 'advanced_moderation', 'analytics'],
            'sticky_messages': ['multiple_messages', 'advanced_formatting', 'scheduling'],
            'giveaways': ['advanced_requirements', 'multiple_winners', 'analytics'],
            'dm_support': ['priority_support', 'advanced_routing', 'analytics']
        }

        subfeatures = default_premium_subfeatures.get(feature_name, [])
        return subfeature_name in subfeatures

    except Exception as e:
        logger.error(f"Error checking premium subfeature status: {e}")
        return False

def check_user_owns_server(user_id: int, server_id: int) -> bool:
    """Check if user owns a specific Discord server"""
    try:
        # Get access token from session - handle both dict and string formats
        token_data = session.get('token')
        access_token = None

        if isinstance(token_data, dict):
            access_token = token_data.get('access_token')
        elif isinstance(token_data, str):
            # If token is stored as string, it might be a JWT token
            access_token = token_data

        if not access_token:
            logger.warning(f"No access token found for user {user_id}")
            return False

        # Get user's guilds from Discord API
        user_guilds = get_user_guilds(access_token)

        if not user_guilds:
            return False

        # Check if user owns the specific server
        for guild in user_guilds:
            if str(guild.get('id')) == str(server_id) and guild.get('owner'):
                return True

        return False

    except Exception as e:
        logger.error(f"Error checking server ownership for user {user_id}, server {server_id}: {e}")
        return False

def get_user_owned_servers(user_id: int) -> List[Dict[str, Any]]:
    """Get list of servers owned by the user"""
    try:
        # Get access token from session - handle both dict and string formats
        token_data = session.get('token')
        access_token = None

        if isinstance(token_data, dict):
            access_token = token_data.get('access_token')
        elif isinstance(token_data, str):
            # If token is stored as string, it might be a JWT token
            access_token = token_data

        if not access_token:
            logger.warning(f"No access token found for user {user_id}")
            return []

        # Get user's guilds from Discord API
        user_guilds = get_user_guilds(access_token)

        if not user_guilds:
            return []

        # Filter to only owned servers
        owned_servers = []
        for guild in user_guilds:
            if guild.get('owner'):
                owned_servers.append({
                    'id': guild.get('id'),
                    'name': guild.get('name'),
                    'icon': guild.get('icon'),
                    'permissions': guild.get('permissions', 0)
                })

        return owned_servers

    except Exception as e:
        logger.error(f"Error getting owned servers for user {user_id}: {e}")
        return []

def validate_premium_access(user_id: int, server_id: int) -> Dict[str, Any]:
    """Validate if user has premium access to a server"""
    result = {
        'has_access': False,
        'reason': '',
        'subscription_status': None,
        'owns_server': False
    }

    try:
        # Check if user has active subscription
        subscription = db.get_user_subscription(user_id)
        result['subscription_status'] = subscription.get('status') if subscription else None

        if not subscription:
            result['reason'] = 'No active subscription'
            return result

        # Check if user owns the server
        owns_server = check_user_owns_server(user_id, server_id)
        result['owns_server'] = owns_server

        if not owns_server:
            result['reason'] = 'User does not own this server'
            return result

        # All checks passed
        result['has_access'] = True
        result['reason'] = 'Access granted'
        return result

    except Exception as e:
        logger.error(f"Error validating premium access: {e}")
        result['reason'] = f'Validation error: {str(e)}'
        return result

def disable_premium_features_for_user(user_id: int) -> bool:
    """Disable all premium features for servers owned by a user who lost premium access"""
    try:
        # Get all servers owned by this user
        user_servers = []

        # Get user's servers from session or Discord API
        if 'user_servers' in session:
            user_servers = [server for server in session['user_servers'] if server.get('owner')]

        disabled_count = 0

        for server in user_servers:
            server_id = int(server['id'])

            # Get current premium features
            premium_features = [f['name'] for f in db.get_features() if f.get('is_premium', False)]

            for feature_name in premium_features:
                # Disable the feature based on its type
                if feature_name == 'repping':
                    # Disable repping system
                    config = db.get_server_config(server_id)
                    if config and (config.get('repping_role_id') or config.get('role_id')):
                        db.update_server_config_field(server_id, 'repping_enabled', False)
                        disabled_count += 1

                elif feature_name == 'tempvoice':
                    # Disable tempvoice system
                    if db.update_tempvoice_settings_enabled(server_id, False):
                        disabled_count += 1

                elif feature_name == 'music':
                    # Disable music system
                    if db.update_music_settings_enabled(server_id, False):
                        disabled_count += 1

                elif feature_name == 'dm_support':
                    # Disable DM support system
                    if db.update_dm_support_settings_enabled(server_id, False):
                        disabled_count += 1

                elif feature_name == 'sticky_messages':
                    # Disable sticky messages
                    if db.update_sticky_messages_enabled(server_id, False):
                        disabled_count += 1

        logger.info(f"Disabled {disabled_count} premium features for user {user_id}")
        return disabled_count > 0

    except Exception as e:
        logger.error(f"Error disabling premium features for user {user_id}: {e}")
        return False

def validate_premium_feature_access(server_id: int, feature_name: str) -> Dict[str, Any]:
    """Validate if user has access to a premium feature"""
    result = {
        'has_access': False,
        'is_premium': is_premium_feature(feature_name),
        'reason': '',
        'redirect_url': None
    }

    try:
        # If it's not a premium feature, allow access
        if not result['is_premium']:
            result['has_access'] = True
            result['reason'] = 'Free feature'
            return result

        # For premium features, check subscription and server ownership
        user_id = session.get('user_id')
        if not user_id:
            result['reason'] = 'Not authenticated'
            try:
                result['redirect_url'] = url_for('login')
            except RuntimeError:
                # Handle case where we're outside application context
                result['redirect_url'] = '/login'
            return result

        # Check subscription and server ownership
        validation = validate_premium_access(user_id, server_id)

        if validation['has_access']:
            result['has_access'] = True
            result['reason'] = 'Premium access granted'
        else:
            result['reason'] = validation['reason']
            try:
                result['redirect_url'] = url_for('shop')
            except RuntimeError:
                result['redirect_url'] = '/shop'

            # If user lost premium access, automatically disable premium features
            if not validation.get('subscription_status'):
                disable_premium_features_for_user(user_id)

        return result

    except Exception as e:
        logger.error(f"Error validating premium feature access: {e}")
        result['reason'] = f'Validation error: {str(e)}'
        return result

def require_premium_feature(feature_name: str):
    """Decorator to require premium access for specific features"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Get server_id from request or session
                server_id = request.form.get('server_id') or request.args.get('server_id') or session.get('server_id')

                if not server_id:
                    if request.is_json:
                        return jsonify({'error': 'Server ID required'}), 400
                    flash('Server ID required', 'error')
                    try:
                        return redirect(url_for('select_server'))
                    except RuntimeError:
                        return redirect('/select-server')

                # Validate premium feature access
                logger.info(f"Validating premium access for feature '{feature_name}' on server {server_id}")
                validation = validate_premium_feature_access(int(server_id), feature_name)
                logger.info(f"Premium validation result: {validation}")

                if not validation['has_access']:
                    if request.is_json:
                        return jsonify({
                            'error': f'Premium subscription required for {feature_name}',
                            'is_premium': validation['is_premium'],
                            'reason': validation['reason'],
                            'redirect_url': validation['redirect_url']
                        }), 403

                    if validation['is_premium']:
                        try:
                            return redirect(url_for('premium_required', feature=feature_name))
                        except RuntimeError:
                            return redirect(f'/premium-required?feature={feature_name}')

                return f(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in premium feature validation for {feature_name}: {e}")
                # For premium features, be strict - redirect to error page instead of allowing access
                if request.is_json:
                    return jsonify({'error': f'Premium validation error: {str(e)}'}), 500
                flash(f'Error validating premium access: {str(e)}', 'error')
                try:
                    return redirect(url_for('dashboard'))
                except RuntimeError:
                    return redirect('/dashboard')
        return decorated_function
    return decorator

# Global error handlers
@app.errorhandler(404)
def not_found_error(error):
    """Handle 404 errors"""
    if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'error': 'Page not found'}), 404

    # Don't show 404 error for dashboard redirects - let the dashboard route handle it
    if request.path == '/dashboard':
        return redirect(url_for('dashboard'))

    # For API routes, return JSON error
    if request.path.startswith('/api/'):
        return jsonify({'error': 'API endpoint not found'}), 404

    # For static files and other resources, don't flash messages
    if request.path.startswith('/static/') or request.path.startswith('/favicon'):
        return render_template('404.html'), 404

    # Only flash error for actual page requests that users would see
    logger.warning(f"404 error for path: {request.path}")

    # Check if this is a legitimate page request (not a bot or crawler)
    user_agent = request.headers.get('User-Agent', '').lower()
    if any(bot in user_agent for bot in ['bot', 'crawler', 'spider', 'scraper']):
        return render_template('404.html'), 404

    # Don't flash messages for public pages - just show 404 template
    public_paths = ['/status', '/shop', '/']
    if any(request.path.startswith(path) for path in public_paths):
        return render_template('404.html'), 404

    # For dashboard/authenticated pages, show error and redirect
    flash('The page you requested was not found.', 'error')
    return redirect(url_for('index'))

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {error}", exc_info=True)
    if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'error': 'An internal server error occurred'}), 500
    flash('An internal server error occurred. Please try again later.', 'error')
    return redirect(url_for('index'))

@app.errorhandler(403)
def forbidden_error(error):
    """Handle 403 errors"""
    if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'error': 'Access forbidden'}), 403
    flash('You do not have permission to access this resource.', 'error')
    return redirect(url_for('index'))

def handle_route_error(e, route_name, redirect_to='index'):
    """Common error handler for routes"""
    logger.error(f"Error in {route_name} route: {str(e)}", exc_info=True)
    if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'error': f'An error occurred in {route_name}. Please try again.'}), 500
    flash(f'An error occurred while loading {route_name}. Please try again.', 'error')
    return redirect(url_for(redirect_to))

def get_server_info(server_id: int) -> Optional[Dict[str, Any]]:
    """Get server information from Discord API"""
    try:
        # Try to get from session guilds if available
        guilds = session.get('guilds', [])
        user_servers = session.get('user_servers', [])
        server_id_str = str(server_id)

        # Try to find in guilds
        guild = next((g for g in guilds if str(g.get('id')) == server_id_str), None)
        if guild:
            return {
                'id': int(guild['id']),
                'name': guild.get('name', 'Unknown Server'),
                'icon': guild.get('icon'),
                'member_count': guild.get('approximate_member_count', guild.get('member_count', 0)),
                'owner_id': guild.get('owner_id')
            }

        # Try to find in user_servers
        user_server = next((s for s in user_servers if str(s.get('id')) == server_id_str), None)
        if user_server:
            return {
                'id': int(user_server['id']),
                'name': user_server.get('name', 'Unknown Server'),
                'icon': user_server.get('icon'),
                'member_count': user_server.get('member_count', 0),
                'owner_id': user_server.get('owner_id')
            }

        # Try to get from shard communication service
        try:
            server_info = shard_comm.run_async(shard_comm.get_server_info(str(server_id)))
            if server_info:
                return {
                    'id': server_info['id'],
                    'name': server_info['name'],
                    'icon': server_info['icon'],
                    'member_count': server_info['member_count'],
                    'owner_id': server_info['owner_id']
                }
        except Exception as e:
            logger.warning(f"Failed to get server info from shard: {e}")

        # Fallback to Discord API if shard communication fails
        token = session.get('token', {}).get('access_token')
        if token:
            headers = {
                'Authorization': f'Bearer {token}'
            }
            response = requests.get(
                f'https://discord.com/api/v10/users/@me/guilds/{server_id}',
                headers=headers
            )
            if response.status_code == 200:
                guild = response.json()
                return {
                    'id': int(guild['id']),
                    'name': guild.get('name', 'Unknown Server'),
                    'icon': guild.get('icon'),
                    'member_count': guild.get('approximate_member_count', guild.get('member_count', 0)),
                    'owner_id': guild.get('owner_id')
                }

        logger.warning(f"Could not fetch server info for {server_id}")
        return {
            'id': int(server_id),
            'name': 'Unknown Server',
            'icon': None,
            'member_count': 0,
            'owner_id': None
        }
    except Exception as e:
        logger.error(f"Error in get_server_info: {e}")
        return {
            'id': int(server_id),
            'name': 'Unknown Server',
            'icon': None,
            'member_count': 0,
            'owner_id': None
        }

def get_user_servers(license_key: str) -> List[Dict[str, Any]]:
    """Get all servers associated with a license key"""
    # Get license key info
    key_info = db.get_license_key_by_key(license_key)
    if not key_info or not key_info.get('redeemed'):
        return []
    
    # Get server info if key is redeemed to a server
    servers = []
    if key_info.get('server_id'):
        server_info = get_server_info(key_info['server_id'])
        if server_info:
            servers.append(server_info)
    
    return servers

@app.route('/')
def index():
    """Home page - redirect to dashboard if authenticated, otherwise show welcome page"""
    return render_template('index.html', discord_auth_url=get_oauth_url())

@app.route('/shop')
@login_required
def shop():
    """Shop page for purchasing premium features"""
    return render_template('shop.html')

@app.route('/premium-required')
@login_required
def premium_required():
    """Premium required page for blocked features"""
    feature_name = request.args.get('feature', 'Unknown Feature')

    # Get feature details
    feature_details = {
        'repping': {
            'name': 'Repping System',
            'description': 'Allow users to gain reputation and rewards through activity tracking.',
            'benefits': ['Custom rewards', 'Advanced tracking', 'Role stacking']
        },
        'dm_support': {
            'name': 'DM Support',
            'description': 'Handle support tickets through direct messages with advanced routing.',
            'benefits': ['Priority support', 'Advanced routing', 'Analytics dashboard']
        },
        'tempvoice': {
            'name': 'Temporary Voice Channels',
            'description': 'Create temporary voice channels with customizable settings.',
            'benefits': ['Custom permissions', 'Advanced settings', 'Multiple channels']
        },
        'music': {
            'name': 'Music System',
            'description': 'High-quality music playback with advanced controls and features.',
            'benefits': ['Premium quality', 'Playlist management', 'Advanced filters']
        },
        'statistics': {
            'name': 'Server Statistics',
            'description': 'View detailed server statistics and analytics.',
            'benefits': ['Detailed analytics', 'User activity tracking', 'Export capabilities']
        },
        'on-site-logs': {
            'name': 'Dashboard Logs',
            'description': 'Access comprehensive logging through the dashboard.',
            'benefits': ['Real-time logs', 'Advanced filtering', 'Export functionality']
        }
    }

    feature_info = feature_details.get(feature_name, {
        'name': feature_name.replace('_', ' ').title(),
        'description': 'This premium feature provides advanced functionality for your server.',
        'benefits': ['Enhanced features', 'Priority support', 'Advanced customization']
    })

    return render_template('premium_required.html', feature=feature_info)

@app.route('/status')
def status():
    """Status page showing shard information"""
    return render_template('status.html')

# Legal pages
@app.route('/terms')
def terms():
    """Terms of Service page"""
    return render_template('terms.html')

@app.route('/privacy')
def privacy():
    """Privacy Policy page"""
    return render_template('privacy.html')

@app.route('/refund')
def refund():
    """Refund Policy page"""
    return render_template('refund.html')

@app.route('/docs')
def docs():
    """Documentation page"""
    return render_template('docs.html')



@app.route('/dashboard')
@require_auth
def dashboard():
    """Main dashboard page"""
    try:
        # Monitor session size and clean up if needed
        session_size = get_session_size()
        if session_size > 3000:  # If session is getting large (3KB threshold)
            logger.warning(f"Large session detected: {session_size} bytes")
            cleanup_session()
            new_size = get_session_size()
            logger.info(f"Session size reduced from {session_size} to {new_size} bytes")

        # Get server ID from session
        server_id = session.get('server_id')
        if not server_id:
            logger.warning("No server_id in session, redirecting to server selection")
            # Clear any existing flash messages to prevent accumulation
            if '_flashes' in session:
                session.pop('_flashes', None)
            flash('Please select a server to access the dashboard.', 'info')
            return redirect(url_for('select_server'))
        
        # Get user ID from session
        user_id = session.get('user_id')
        if not user_id:
            logger.warning("No user_id in session, redirecting to login")
            return redirect(url_for('logout'))
        
        logger.info(f"Loading dashboard for server {server_id} (user: {user_id})")
        
        # Convert server_id to int if it's a string
        try:
            server_id_int = int(server_id)
        except (ValueError, TypeError) as e:
            logger.error(f"Invalid server_id format: {server_id}, error: {e}")
            flash('Invalid server ID. Please select a server again.', 'error')
            return redirect(url_for('select_server'))
        
        # Get server info from session or API
        server_info = get_server_info(server_id_int)
        if not server_info:
            logger.warning(f"Server info not found for server_id: {server_id}")
            flash('Server information could not be loaded. Please try selecting the server again.', 'error')
            return redirect(url_for('select_server'))
        
        # Get server configuration from database
        try:
            config = db.get_server_config(server_id_int) or {}
            logger.info(f"Loaded server config: {config}")
            
            # Handle legacy field names for backward compatibility
            if 'role_id' in config and 'repping_role_id' not in config:
                config['repping_role_id'] = config['role_id']
            if 'channel_id' in config and 'repping_channel_id' not in config:
                config['repping_channel_id'] = config['channel_id']
                
        except Exception as e:
            logger.error(f"Error loading server config: {e}", exc_info=True)
            config = {}
            flash('Error loading server configuration. Some features may not work correctly.', 'warning')
        
        # Define required fields and their display names
        field_display_names = {
            'repping_role_id': 'Repping Role',
            'repping_channel_id': 'Repping Channel',
            'trigger_word': 'Trigger Word',
            'vent_channel_id': 'Vent Channel',
            'tempvoice_interface_channel_id': 'Temp Voice Interface',
            'tempvoice_creator_channel_id': 'Temp Voice Creator'
        }
        
        # Always required fields for basic functionality
        required_fields = ['repping_role_id', 'repping_channel_id', 'trigger_word']
        
        # Check which features are enabled and require configuration
        enabled_features = []
        if config.get('vent_enabled', False):
            enabled_features.append('vent')
            required_fields.append('vent_channel_id')
            
        if config.get('tempvoice_enabled', False):
            enabled_features.append('tempvoice')
            required_fields.extend(['tempvoice_interface_channel_id', 'tempvoice_creator_channel_id'])
        
        # Find missing required fields
        missing_fields = []
        for field in required_fields:
            if not config.get(field):
                display_name = field_display_names.get(field, field.replace('_', ' ').title())
                missing_fields.append(display_name)
        
        # Determine if configuration is complete
        is_configured = len(missing_fields) == 0
        
        # Log configuration status for debugging
        logger.info(f"Server {server_id} configuration check:")
        logger.info(f"- Enabled features: {', '.join(enabled_features) if enabled_features else 'None'}")
        logger.info(f"- Missing required fields: {', '.join(missing_fields) if missing_fields else 'None'}")
        logger.info(f"- Configuration status: {'Complete' if is_configured else 'Incomplete'}")
        
        # For display in the template
        final_missing_fields = missing_fields
        
        # Get member count and bot status from shard
        member_count = 0
        bot_in_server = False
        try:
            # Check if bot is in server via shard communication
            bot_in_server = shard_comm.run_async(shard_comm.check_bot_in_server(str(server_id_int)))

            # Get server info for member count
            server_info_shard = shard_comm.run_async(shard_comm.get_server_info(str(server_id_int)))
            if server_info_shard:
                member_count = server_info_shard.get('member_count', 0)
                logger.info(f"Found server in shard with {member_count} members")
        except Exception as e:
            logger.error(f"Error getting server info from shard: {e}", exc_info=True)
        
        # Update session with latest info
        session['server_name'] = server_info.get('name', 'Unknown Server')
        session['member_count'] = member_count
        session['trigger_word'] = config.get('trigger_word', '/ryzuo')
        session.modified = True
        
        # Get user's servers for the sidebar
        user_servers = session.get('user_servers', [])
        
        # Prepare feature configurations

        # Extract repping and vent configurations
        repping_config = {
            'repping_role_id': config.get('repping_role_id', config.get('role_id')),
            'repping_channel_id': config.get('repping_channel_id', config.get('channel_id')),
            'trigger_word': config.get('trigger_word', '/ryzuo')
        }

        # Get additional feature settings from their respective collections
        vent_settings = db.get_vent_settings(server_id_int)
        tempvoice_settings = db.get_tempvoice_settings(server_id_int)
        music_settings = db.get_music_settings(server_id_int)
        sticky_messages = db.get_all_sticky_messages(server_id_int)
        dm_support_settings = db.get_dm_support_settings(server_id_int)
        gender_verification_settings = db.get_gender_verification_settings(server_id_int)
        auto_roling_settings = db.get_auto_roling_settings(server_id_int)

        # Check configuration status for each feature
        missing_fields = []

        # Check repping system (always required)
        if not config.get('repping_role_id') and not config.get('role_id'):
            missing_fields.append('repping_role_id')
        if not config.get('repping_channel_id') and not config.get('channel_id'):
            missing_fields.append('repping_channel_id')
        if not config.get('trigger_word'):
            missing_fields.append('trigger_word')

        # Check vent system (only if configured)
        if config.get('vent_channel_id') and not config.get('vent_channel_id'):
            missing_fields.append('vent_channel_id')

        # Check tempvoice system (only if configured)
        if tempvoice_settings and (not tempvoice_settings.get('interface_channel_id') or not tempvoice_settings.get('creator_channel_id')):
            if not tempvoice_settings.get('interface_channel_id'):
                missing_fields.append('tempvoice_interface_channel_id')
            if not tempvoice_settings.get('creator_channel_id'):
                missing_fields.append('tempvoice_creator_channel_id')

        is_configured = len(missing_fields) == 0
        
        # Format missing fields for display
        formatted_missing = []
        field_display_names = {
            'repping_role_id': 'Repping Role',
            'repping_channel_id': 'Repping Channel',
            'trigger_word': 'Trigger Word',
            'vent_channel_id': 'Vent Channel',
            'tempvoice_interface_channel_id': 'Temp Voice Interface Channel',
            'tempvoice_creator_channel_id': 'Temp Voice Creator Channel'
        }
        
        for field in missing_fields:
            formatted_missing.append(field_display_names.get(field, field.replace('_', ' ').title()))
        
        # Construct server icon URL
        server_icon_url = None
        if server_info.get('icon'):
            server_icon_url = f"https://cdn.discordapp.com/icons/{server_id}/{server_info['icon']}.png"

        # Convert ObjectIds to strings for JSON serialization
        sticky_messages = convert_objectids_to_strings(sticky_messages)
        vent_settings = convert_objectids_to_strings(vent_settings)
        tempvoice_settings = convert_objectids_to_strings(tempvoice_settings)
        dm_support_settings = convert_objectids_to_strings(dm_support_settings)
        gender_verification_settings = convert_objectids_to_strings(gender_verification_settings)
        auto_roling_settings = convert_objectids_to_strings(auto_roling_settings)

        # Prepare template context
        template_context = {
            'server_id': server_id_int,  # Use integer version for consistency
            'server_name': server_info.get('name', 'Unknown Server'),
            'server_icon': server_icon_url,
            'member_count': member_count,
            'trigger_word': config.get('trigger_word', '/ryzuo'),
            'user_servers': user_servers,
            'server_info': server_info,
            'config': config,  # Pass the full config instead of just repping_config
            'vent_settings': vent_settings,
            'tempvoice_settings': tempvoice_settings,
            'music_settings': music_settings,
            'sticky_messages': sticky_messages,
            'dm_support_settings': dm_support_settings,
            'gender_verification_settings': gender_verification_settings,
            'auto_roling_settings': auto_roling_settings,
            'is_configured': is_configured,
            'missing_fields': missing_fields,  # Use the list of display names
            'final_missing_fields': formatted_missing,  # Also pass the formatted missing fields
            'settings': config,
            'bot_in_server': bot_in_server,
            'bot_client_id': os.getenv('CLIENT_ID', '1234567890')  # Add bot client ID for invite links
        }
        
        logger.info(f"Rendering dashboard with context: {template_context}")
        return render_template('dashboard.html', **template_context)
                            
    except Exception as e:
        logger.error(f"Error in dashboard route: {str(e)}", exc_info=True)
        flash('An error occurred while loading the dashboard. Please try again.', 'error')
        return redirect(url_for('select_server'))

@app.route('/login')
def login():
    """Redirect to Discord OAuth2 login"""
    if 'token' in session:
        return redirect(url_for('dashboard'))
    return redirect(get_oauth_url())

@app.route('/oauth/callback')
def oauth_callback():
    """Handle OAuth2 callback from Discord"""
    if 'code' not in request.args:
        flash('No authorization code received from Discord', 'error')
        return redirect(url_for('login'))
    
    try:
        # Exchange the authorization code for an access token
        token = get_token(request.args['code'])
        access_token = token['access_token']
        
        # Get user information
        user_info = get_user_info(access_token)
        
        # Store user info in session
        discord_user = {
            'id': user_info['id'],
            'username': user_info['username'],
            'discriminator': user_info.get('discriminator', '0'),
            'avatar': user_info.get('avatar'),
            'email': user_info.get('email')
        }
        
        # Create or update user account in database
        try:
            avatar_url = None
            if user_info.get('avatar'):
                avatar_url = f"https://cdn.discordapp.com/avatars/{user_info['id']}/{user_info['avatar']}.png"
            
            user_created = db.create_or_update_user(
                user_id=int(user_info['id']),
                username=user_info['username'],
                avatar=avatar_url,
                email=user_info.get('email')
            )
            
            if user_created:
                logger.info(f"User account created/updated for {user_info['username']} (ID: {user_info['id']})")
                
                # Create a welcome notification for new users
                existing_user = db.get_user(int(user_info['id']))
                if existing_user and existing_user.get('created_at'):
                    # Check if this is a new user (created within the last minute)
                    from datetime import datetime, timezone, timedelta
                    created_at = existing_user.get('created_at')
                    if isinstance(created_at, datetime):
                        time_diff = datetime.now(timezone.utc) - created_at
                        if time_diff < timedelta(minutes=1):  # New user
                            db.create_notification(
                                user_id=int(user_info['id']),
                                title="Welcome to Ryzuo!",
                                message="Welcome to the Ryzuo dashboard! You can now manage your servers and access premium features.",
                                notification_type="success"
                            )
            else:
                logger.warning(f"Failed to create/update user account for {user_info['username']}")
        except Exception as e:
            logger.error(f"Error creating/updating user account: {e}")
        
        # Create JWT token for session management
        session_token = create_jwt_token(discord_user)
        session['token'] = session_token
        session['discord_user'] = discord_user
        
        # Make session permanent
        session.permanent = True
        
        # Get user's guilds
        guilds = get_user_guilds(access_token)
        # Store only essential guild data to reduce session size
        essential_guilds = []
        for guild in guilds:
            essential_guilds.append({
                'id': guild['id'],
                'name': guild['name'],
                'icon': guild.get('icon'),
                'owner': guild.get('owner', False),
                'permissions': guild.get('permissions', '0')
            })
        session['guilds'] = essential_guilds
        
        # Debug: Log user info
        logger.info(f"Authenticated user: {user_info['username']} (ID: {user_info['id']})")
        
        # Get user's active license keys from database (excluding disabled ones)
        logger.info(f"Fetching license keys for user ID: {user_info['id']} (type: {type(user_info['id'])})")
        license_keys = db.get_user_license_keys(user_info['id'], include_disabled=True)  # Include disabled to show them with visual indicator
        logger.info(f"Found {len(license_keys)} license keys for user")

        # Debug: Log session user_id for comparison
        session_user_id = session.get('user_id')
        logger.info(f"Session user_id: {session_user_id} (type: {type(session_user_id)})")

        # Ensure user_id is stored in session consistently
        session['user_id'] = str(user_info['id'])
        
        # Debug: Log guilds and license keys
        logger.info(f"User is in {len(guilds)} guilds")
        logger.info(f"User has {len(license_keys)} license keys")
        
        # Create a mapping of server IDs to guild data for faster lookup
        guilds_map = {g['id']: g for g in guilds if 'id' in g}
        logger.info(f"Created guilds map with {len(guilds_map)} entries")
        
        # Find all servers where the user has a license key
        user_servers = []
        for key in license_keys:
            try:
                if not key or 'server_id' not in key:
                    logger.warning(f"Skipping invalid license key (missing server_id): {key}")
                    continue
                    
                server_id = key['server_id']
                server_id_str = str(server_id)
                logger.info(f"Processing license key for server ID: {server_id_str}")
                
                # Find the guild in the user's guilds
                user_guild = guilds_map.get(server_id_str)
                
                if not user_guild:
                    logger.warning(f"User is not in server {server_id_str} (or bot doesn't have access)")
                    continue
                
                # Get server info from the guild data
                server_info = {
                    'id': int(server_id_str),
                    'name': user_guild.get('name', 'Unknown Server'),
                    'icon': user_guild.get('icon'),
                    'member_count': user_guild.get('approximate_member_count', 0),
                    'owner_id': user_guild.get('owner_id')
                }
                
                # Check if the key is disabled
                is_disabled = key.get('disabled', False)
                logger.info(f"License key for server {server_id_str} - Disabled: {is_disabled}")
                
                server_data = {
                    'id': int(server_id_str),
                    'name': server_info['name'],
                    'icon': f"https://cdn.discordapp.com/icons/{server_id_str}/{server_info['icon']}.png" if server_info.get('icon') else None,
                    'member_count': server_info['member_count'],
                    'license_key': key['key'],
                    'disabled': is_disabled,
                    'is_owner': int(user_info['id']) == int(user_guild.get('owner_id', 0))
                }
                
                logger.info(f"Adding server to user_servers: {server_data['name']} (ID: {server_data['id']})")
                user_servers.append(server_data)
                
            except Exception as e:
                logger.error(f"Error processing license key {key.get('key', 'unknown')}: {str(e)}", exc_info=True)
                continue
        
        # Store user's servers with valid licenses in session
        session['user_servers'] = user_servers

        # Clean up session to reduce size
        cleanup_session()
        session_size = get_session_size()
        logger.info(f"Session size after OAuth: {session_size} bytes")

        # Always redirect to server selection page (supports both free and premium features)
        logger.info(f'Redirecting user {user_info["username"]} to server selection')
        return redirect(url_for('select_server'))
        
    except Exception as e:
        logger.error(f'OAuth callback error: {str(e)}', exc_info=True)
        flash('Failed to authenticate with Discord. Please try again.', 'error')
        return redirect(url_for('login'))

@app.route('/select-server')
@login_required
def select_server():
    """Page to select a server if user has access to multiple"""
    logger.info("Entering select_server route")
    
    # Get user ID from session
    user_id = session.get('discord_user', {}).get('id')
    if not user_id:
        logger.error("No user ID found in session")
        flash('Session expired. Please log in again.', 'error')
        return redirect(url_for('logout'))
    
    logger.info(f"User ID from session: {user_id}")
    
    # Get fresh license keys from database
    try:
        logger.info(f"Querying license keys for user_id: {user_id} (type: {type(user_id)})")
        license_keys = db.get_user_license_keys(user_id, include_disabled=True)
        logger.info(f"Found {len(license_keys)} license keys for user {user_id}")

        # Note: We now show all servers, not just those with license keys (for free features)
        logger.info(f"Found {len(license_keys)} license keys for user {user_id}")
            
        # Get user's guilds from session
        guilds = session.get('guilds', [])
        logger.info(f"User is in {len(guilds)} guilds")
        
        # Log guild information for debugging
        logger.info(f"Processing {len(guilds)} guilds from Discord API")

        # Separate redeemed and unredeemed keys
        redeemed_keys = [key for key in license_keys if key.get('redeemed', False) and key.get('server_id')]
        unredeemed_keys = [key for key in license_keys if not key.get('redeemed', False)]
        logger.info(f"Found {len(redeemed_keys)} redeemed keys and {len(unredeemed_keys)} unredeemed keys")

        # Create mapping of server IDs to guild data
        guilds_map = {}
        for i, g in enumerate(guilds, 1):
            try:
                guild_id = g.get('id')
                if not guild_id:
                    logger.warning(f"Guild at index {i} has no ID, skipping")
                    continue
                    
                guild_name = g.get('name', 'Unknown Guild')
                member_count = g.get('approximate_member_count', g.get('member_count', 0))
                icon_hash = g.get('icon')
                
                # Log guild details
                logger.debug(f"Guild {i}: {guild_name} (ID: {guild_id})")
                
                # Ensure member_count is an integer
                try:
                    member_count = int(member_count) if member_count else 0
                except (ValueError, TypeError):
                    member_count = 0
                    
                # Add member_count to guild data if not present
                if 'approximate_member_count' not in g and member_count > 0:
                    g['approximate_member_count'] = member_count
                
                logger.debug(f"  - Members: {member_count}")
                logger.debug(f"  - Icon: {'Yes' if icon_hash else 'No'}")
                logger.debug(f"  - Owner ID: {g.get('owner_id', 'N/A')}")
                
                # Add to map with processed data
                guilds_map[guild_id] = g
                
            except Exception as e:
                logger.error(f"Error processing guild {i}: {str(e)}")
                logger.debug(f"Problematic guild data: {g}", exc_info=True)
        
        logger.info(f"Created guilds map with {len(guilds_map)} valid guilds")
        
        logger.info(f"Created guilds map with {len(guilds_map)} entries")
        
        # Start with user servers list - now show ALL servers with admin permissions
        user_servers = []

        # Create a map of servers with license keys for reference
        licensed_servers = {}
        for key in redeemed_keys:
            if key and 'server_id' in key:
                licensed_servers[str(key['server_id'])] = key

        # Process ALL user's guilds where they have admin permissions
        for guild_id, guild in guilds_map.items():
            try:
                # Check user permissions in this guild
                permissions = int(guild.get('permissions', 0))
                is_admin = (permissions & 0x8) == 0x8  # Administrator permission
                is_owner = guild.get('owner', False)

                # Skip if user doesn't have admin permissions
                if not (is_admin or is_owner):
                    logger.debug(f"Skipping server {guild_id} - user doesn't have admin permissions")
                    continue
                    
                # Check if bot is in the server using the check_bot_in_server method
                try:
                    # Use the dedicated method to check bot presence
                    bot_in_server = shard_comm.run_async(shard_comm.check_bot_in_server(str(guild_id)))
                    logger.debug(f"Bot presence check for {guild.get('name', 'Unknown')} ({guild_id}): {bot_in_server}")
                    
                    if not bot_in_server:
                        logger.debug(f"Bot not found in server {guild.get('name', 'Unknown')} ({guild_id})")
                        continue
                        
                except Exception as e:
                    logger.warning(f"Failed to check bot presence for server {guild_id} ({guild.get('name', 'Unknown')}): {e}")
                    # Skip if we can't verify the bot is in the server
                    continue
                
                # Get server info from the guild data
                server_name = guild.get('name', 'Unknown Server')
                owner_id = guild.get('owner_id')

                # Debug log the guild data structure
                logger.debug(f"Processing guild data for {server_name} (ID: {guild_id})")

                # Handle icon - try multiple possible locations
                icon_hash = None
                icon_sources = ['icon', 'icon_hash', 'guild_icon', 'icon_url']
                for source in icon_sources:
                    if guild.get(source):
                        icon_hash = guild[source]
                        break

                # If we have an icon URL, extract the hash
                if icon_hash and ('http://' in icon_hash or 'https://' in icon_hash):
                    # Extract hash from URL like https://cdn.discordapp.com/icons/1234567890/abc123.png
                    parts = icon_hash.split('/')
                    if len(parts) >= 2:
                        icon_hash = parts[-1].split('.')[0]

                # Get member count with multiple fallbacks
                member_count = 1
                try:
                    # Try different possible field names for member count
                    member_fields = ['approximate_member_count', 'member_count', 'members_count', 'size']
                    for field in member_fields:
                        if guild.get(field) is not None:
                            try:
                                member_count = max(1, int(guild[field]))
                                logger.debug(f"Found member count in {field}: {member_count}")
                                break
                            except (ValueError, TypeError):
                                logger.debug(f"Error parsing {field} for member count")

                    # If still no valid count, try to fetch from Discord API
                    if member_count <= 1 and os.getenv("BOT_TOKEN"):
                        try:
                            headers = {'Authorization': f'Bot {os.getenv("BOT_TOKEN")}'}
                            response = requests.get(
                                f'https://discord.com/api/v10/guilds/{guild_id}?with_counts=true',
                                headers=headers,
                                timeout=5
                            )
                            if response.status_code == 200:
                                guild_data = response.json()
                                api_member_count = guild_data.get('approximate_member_count')
                                if api_member_count is not None:
                                    member_count = max(1, int(api_member_count))
                                    logger.debug(f"Fetched member count via API: {member_count}")
                        except Exception as e:
                            logger.error(f"Error fetching member count from API: {e}")

                except Exception as e:
                    logger.error(f"Error getting member count: {e}")
                    member_count = 1  # Fallback to 1

                logger.info(f"Server {server_name} (ID: {guild_id}) - Members: {member_count}, Icon: {'Yes' if icon_hash else 'No'}")

                # Check if this server has a license key
                license_key_data = licensed_servers.get(guild_id)
                has_license = license_key_data is not None
                is_disabled = license_key_data.get('disabled', False) if license_key_data else False

                # Safely handle owner comparison
                is_server_owner = False
                if owner_id is not None:
                    try:
                        is_server_owner = str(user_id) == str(owner_id)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Error comparing user IDs: {e}")

                # We already checked bot presence above

                # Build the icon URL if we have an icon hash
                icon_url = None
                if icon_hash:
                    # Clean the icon hash
                    clean_icon_hash = icon_hash.split('?')[0].split('#')[0]
                    icon_url = f"https://cdn.discordapp.com/icons/{guild_id}/{clean_icon_hash}.png"

                    # Try with different extensions if .png fails
                    icon_extensions = ['png', 'jpg', 'jpeg', 'webp', 'gif']
                    if not any(icon_url.lower().endswith(ext) for ext in icon_extensions):
                        icon_url = f"https://cdn.discordapp.com/icons/{guild_id}/{clean_icon_hash}.png"

                server_data = {
                    'id': int(guild_id),
                    'name': server_name,
                    'icon': icon_url,
                    'member_count': max(1, member_count),  # Ensure at least 1 member
                    'license_key': license_key_data.get('key') if license_key_data else None,
                    'has_license': has_license,
                    'disabled': is_disabled,
                    'is_owner': is_server_owner,
                    'bot_in_server': bot_in_server
                }

                logger.debug(f"Created server data: {server_data}")
                logger.info(f"Adding server to user_servers: {server_data['name']} (ID: {server_data['id']}) - License: {has_license}, Bot present: {bot_in_server}")
                user_servers.append(server_data)

            except Exception as e:
                logger.error(f"Error processing guild {guild_id}: {str(e)}", exc_info=True)
                continue

        # Add unredeemed keys as individual "Redeem Key" cards
        for key in unredeemed_keys:
            try:
                # Create a special card for unredeemed keys
                redeem_card = {
                    'id': f"redeem_{key['key'][:8]}",  # Use a special ID format
                    'name': 'Redeem License Key',
                    'icon': None,
                    'member_count': 0,
                    'license_key': key['key'],
                    'has_license': False,
                    'disabled': False,
                    'is_owner': True,  # User owns the key
                    'unredeemed': True,  # Special flag to identify redeem cards
                    'key_preview': f"{key['key'][:8]}...{key['key'][-4:]}"
                }
                user_servers.append(redeem_card)
                logger.info(f"Added unredeemed key card: {redeem_card['key_preview']}")
            except Exception as e:
                logger.error(f"Error processing unredeemed key {key.get('key', 'unknown')}: {str(e)}", exc_info=True)
                continue

        if not user_servers:
            logger.warning(f"No servers found for user {user_id}")
            # Instead of redirecting to homepage, show the select-server page with invite instructions
            return render_template('select_server.html',
                                 user_servers=[],
                                 no_servers=True,
                                 invite_url=f"https://discord.com/oauth2/authorize?client_id={os.getenv('DISCORD_CLIENT_ID', '1389466386396483714')}&permissions=8&scope=bot")
            
        # Update session with fresh server data
        session['user_servers'] = user_servers
        
        # Always show server selection page (no auto-selection)
            
        # Sort servers by: 
        # 1. Redeem cards first
        # 2. Servers where user is owner
        # 3. Servers where user is admin
        # 4. Within each category, sort by server name
        redeem_cards = [s for s in user_servers if s.get('unredeemed')]
        
        # Separate owned and admin servers (non-redeem cards)
        owned_servers = []
        admin_servers = []
        
        for server in user_servers:
            if server.get('unredeemed'):
                continue  # Already handled by redeem_cards
                
            if server.get('is_owner'):
                owned_servers.append(server)
            else:
                admin_servers.append(server)
        
        # Sort each category by name
        sorted_redeem = sorted(redeem_cards, key=lambda x: x.get('key_preview', '').lower())
        sorted_owned = sorted(owned_servers, key=lambda x: x.get('name', '').lower())
        sorted_admin = sorted(admin_servers, key=lambda x: x.get('name', '').lower())
        
        # Combine all servers in the correct order
        sorted_servers = sorted_redeem + sorted_owned + sorted_admin

        # Calculate free servers count by subtracting licensed servers from total servers
        free_count = len([s for s in user_servers if not s.get('has_license', False)])
        logger.info(f"Rendering server selection page with {len(sorted_servers)} servers ({len(redeem_cards)} redeem cards, {len(licensed_servers)} licensed, {free_count} free)")
        return render_template('server_selection.html', servers=sorted_servers, username=session.get('discord_user', {}).get('username', 'User'))
        
    except Exception as e:
        logger.error(f'Error in select_server: {str(e)}', exc_info=True)
        flash('An error occurred while loading your servers. Please try again.', 'error')
        return redirect(url_for('logout'))

@app.route('/select-server/<server_id>', methods=['GET', 'POST'])
@login_required
def select_server_id(server_id):
    """Set the selected server in session"""
    try:
        logger.info(f"Starting server selection for server_id: {server_id}")
        
        # Ensure we have a valid session
        if 'user_id' not in session:
            logger.warning("No user_id in session, attempting to restore from token")
            if 'token' in session:
                try:
                    # Try to restore user_id from token if possible
                    from oauth2 import SECRET_KEY
                    import jwt
                    payload = jwt.decode(session['token'], SECRET_KEY, algorithms=['HS256'])
                    session['user_id'] = str(payload['user_id'])
                    logger.info(f"Restored user_id from token: {session['user_id']}")
                except Exception as e:
                    logger.error(f"Failed to restore user_id from token: {str(e)}")
                    if request.is_json:
                        return jsonify({'error': 'Session expired. Please log in again.', 'logout': True}), 401
                    return redirect(url_for('logout'))
            else:
                logger.warning("No token found in session")
                if request.is_json:
                    return jsonify({'error': 'Session expired. Please log in again.', 'logout': True}), 401
                return redirect(url_for('logout'))
        
        # Ensure session is permanent
        session.permanent = True
        
        # Convert server_id to string for consistency
        server_id = str(server_id)
        user_id = session.get('user_id')
        logger.info(f"Selecting server {server_id} for user {user_id}")
        
        # Verify the user has access to this server (check if they have admin permissions)
        user_servers = session.get('user_servers', [])
        logger.info(f"User {user_id} has access to {len(user_servers)} servers")

        # Check if user has access to this server (either in user_servers or has admin permissions)
        has_access = any(str(s.get('id')) == server_id for s in user_servers)

        if not has_access:
            # Try to verify access through Discord API
            guilds = session.get('guilds', [])
            guild = next((g for g in guilds if str(g.get('id')) == server_id), None)

            if guild:
                permissions = int(guild.get('permissions', 0))
                is_admin = (permissions & 0x8) == 0x8  # Administrator permission
                is_owner = guild.get('owner', False)
                has_access = is_admin or is_owner

        if not has_access:
            logger.warning(f"User {user_id} attempted to access unauthorized server {server_id}")
            if request.is_json:
                return jsonify({'error': 'You do not have administrator permissions in this server.', 'redirect': url_for('select_server')}), 403
            flash('You do not have administrator permissions in this server.', 'error')
            return redirect(url_for('select_server'))
        
        # Refresh user servers if not in session
        if 'user_servers' not in session or not session['user_servers']:
            try:
                logger.info(f"Refreshing server list for user {user_id}")
                # Get user's license keys (optional now)
                license_keys = db.get_user_license_keys(user_id)
                logger.info(f"Found {len(license_keys)} license keys for user {user_id}")
                
                # Get guilds from Discord API
                guilds = get_user_guilds(session.get('token', {}).get('access_token'))
                logger.info(f"Found {len(guilds or [])} guilds from Discord API")
                
                if not guilds:
                    logger.warning(f"No guilds found for user {user_id}")
                    if request.is_json:
                        return jsonify({'error': 'No servers found. Make sure the bot is in at least one server.'}), 404
                    flash('No servers found. Make sure the bot is in at least one server.', 'error')
                    return redirect(url_for('select_server'))
                
                # Process guilds and match with license keys
                user_servers = []
                for guild in guilds:
                    guild_id = str(guild.get('id'))
                    for key in license_keys:
                        if str(key.get('server_id')) == guild_id:
                            # Get server configuration for trigger word
                            server_config = db.get_server_config(guild_id) or {}
                            logger.debug(f"Found server config for {guild_id}: {server_config}")
                            
                            user_servers.append({
                                'id': guild_id,
                                'name': guild.get('name', 'Unknown Server'),
                                'icon': guild.get('icon'),
                                'member_count': guild.get('approximate_member_count', 0),
                                'license_key': key.get('key'),
                                'trigger_word': server_config.get('trigger_word', '/ryzuo')
                            })
                            break
                
                if not user_servers:
                    logger.warning(f"No servers found with valid license keys for user {user_id}")
                    if request.is_json:
                        return jsonify({'error': 'No servers found with valid license keys.', 'redirect': url_for('no_license')}), 403
                    return redirect(url_for('no_license'))
                
                session['user_servers'] = user_servers
                session['guilds'] = guilds
                logger.info(f"Refreshed {len(user_servers)} servers for user {user_id}")
                
            except Exception as e:
                logger.error(f"Error refreshing user servers: {str(e)}", exc_info=True)
                if request.is_json:
                    return jsonify({'error': 'Failed to load server information. Please try again.', 'redirect': url_for('select_server')}), 500
                flash('Failed to load server information. Please try again.', 'error')
                return redirect(url_for('select_server'))
        
        # Verify the selected server is in user's servers
        user_servers = session.get('user_servers', [])
        selected_server = next((s for s in user_servers if str(s.get('id')) == server_id), None)
        
        if not selected_server:
            logger.warning(f"Server {server_id} not found in user's servers")
            if request.is_json:
                return jsonify({'error': 'Server not found in your servers.', 'redirect': url_for('select_server')}), 404
            flash('Server not found in your servers.', 'error')
            return redirect(url_for('select_server'))
        
        # Get fresh server info from Discord API
        try:
            server_info = get_server_info(server_id)
            if not server_info:
                raise Exception("Failed to fetch server info from Discord")
                
            # Update session with selected server
            session['server_id'] = server_id
            session['server_name'] = server_info.get('name', selected_server.get('name', 'Unknown Server'))
            session['server_icon'] = server_info.get('icon')
            session['member_count'] = server_info.get('member_count', 0)
            session['trigger_word'] = selected_server.get('trigger_word', '/ryzuo')
            
            # Ensure we have the license key for this server
            if 'license_key' not in selected_server and 'license_key' in session:
                session['license_key'] = selected_server.get('license_key')
            
            logger.info(f"Successfully selected server {server_id} ({session['server_name']}) for user {user_id}")
            
            # Ensure session is saved before responding
            session.modified = True
            
            # Clear any existing flash messages to prevent them from persisting
            if '_flashes' in session:
                session.pop('_flashes', None)
            
            if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'success': True,
                    'redirect': url_for('dashboard'),
                    'server': {
                        'id': server_id,
                        'name': session['server_name'],
                        'icon': server_info.get('icon'),
                        'member_count': server_info.get('member_count', 0)
                    }
                })
                
            return redirect(url_for('dashboard'))
            
        except Exception as e:
            logger.error(f"Error getting server info: {str(e)}", exc_info=True)
            if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'error': 'Failed to get server information. Please try again.',
                    'redirect': url_for('select_server')
                }), 500
            flash('Failed to get server information. Please try again.', 'error')
            return redirect(url_for('select_server'))
        
    except Exception as e:
        logger.error(f'Error selecting server {server_id}: {str(e)}', exc_info=True)
        if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'error': 'An error occurred while selecting the server. Please try again.',
                'logout': isinstance(e, (KeyError, AttributeError))  # Suggest logout for session errors
            }), 500
        flash('An error occurred while selecting the server. Please try again.', 'error')
        return redirect(url_for('select_server'))

@app.route('/no-license')
def no_license():
    """Show no license page when user has no valid licenses"""
    # Check if user is logged in
    if 'discord_user' not in session:
        flash('Please log in to view this page', 'info')
        return redirect(url_for('login'))

    # Double-check if user really has no servers with licenses
    if 'user_servers' in session and session['user_servers']:
        # Verify at least one server has a valid license
        for server in session['user_servers']:
            if server.get('license_key'):
                return redirect(url_for('select_server'))

    # Get user info for display
    user_info = session.get('discord_user', {})
    username = user_info.get('username', 'User')

    # Get support server invite from config or use default
    support_server = os.getenv('SUPPORT_SERVER_INVITE')

    # Log the access attempt
    logger.info(f'User {username} (ID: {user_info.get("id", "unknown")} accessed no_license page')

    return render_template('no_license.html',
                         support_server=support_server,
                         username=username)

@app.route('/api/user-servers')
@require_auth
def api_user_servers():
    """Get user's Discord servers for license redemption"""
    try:
        # Get user's guilds from session
        guilds = session.get('guilds', [])

        # Filter servers where user has admin permissions (regardless of bot presence)
        eligible_servers = []
        logger.info(f"Checking permissions for {len(guilds)} guilds")

        for guild in guilds:
            # Check if user has admin permissions or is server owner
            try:
                permissions = int(guild.get('permissions', 0))
            except (ValueError, TypeError):
                logger.warning(f"Invalid permissions value for guild {guild.get('id')}: {guild.get('permissions')}")
                permissions = 0

            is_owner = guild.get('owner', False)

            logger.debug(f"Guild {guild.get('name', 'Unknown')} ({guild.get('id')}): permissions={permissions}, is_owner={is_owner}, has_admin={(permissions & 0x8) != 0}")

            # User needs either Administrator permission OR be the server owner
            if (permissions & 0x8) or is_owner:  # Administrator permission OR owner
                try:
                    # Check if server already has a license
                    existing_license = db.get_server_license_key(int(guild['id']))
                    if not existing_license:
                        # Check if bot is in the server via shard communication
                        bot_present = check_bot_in_server_via_shard(str(guild['id']))

                        eligible_servers.append({
                            'id': guild['id'],
                            'name': guild['name'],
                            'icon': guild.get('icon'),
                            'bot_present': bot_present
                        })
                except Exception as e:
                    logger.warning(f"Error checking server {guild['id']}: {e}")
                    # Still add the server even if there's an error checking bot presence
                    eligible_servers.append({
                        'id': guild['id'],
                        'name': guild['name'],
                        'icon': guild.get('icon'),
                        'bot_present': False
                    })

        return jsonify({
            'success': True,
            'servers': eligible_servers
        })

    except Exception as e:
        logger.error(f"Error getting user servers: {e}")
        return jsonify({
            'success': False,
            'error': 'Failed to load servers'
        }), 500

@app.route('/redeem-license-key', methods=['POST'])
@require_auth
def redeem_license_key():
    """Redeem a license key for a server via dashboard"""
    try:
        data = request.get_json()
        license_key = data.get('license_key', '').strip()
        server_id = data.get('server_id')

        if not license_key or not server_id:
            return jsonify({
                'success': False,
                'error': 'License key and server ID are required'
            }), 400

        # Validate server ID format
        try:
            server_id = int(server_id)
        except ValueError:
            return jsonify({
                'success': False,
                'error': 'Invalid server ID format'
            }), 400

        user_id = session.get('user_id')
        if not user_id:
            return jsonify({
                'success': False,
                'error': 'User session invalid'
            }), 401

        logger.info(f"Redeeming license key for user_id: {user_id} (type: {type(user_id)})")

        # Check if user has admin permissions in the server
        guilds = session.get('guilds', [])
        user_guild = None
        for guild in guilds:
            if int(guild['id']) == server_id:
                try:
                    permissions = int(guild.get('permissions', 0))
                except (ValueError, TypeError):
                    logger.warning(f"Invalid permissions value for guild {guild.get('id')}: {guild.get('permissions')}")
                    permissions = 0

                is_owner = guild.get('owner', False)

                # User needs either Administrator permission OR be the server owner
                if (permissions & 0x8) or is_owner:  # Administrator permission OR owner
                    user_guild = guild
                    break

        if not user_guild:
            return jsonify({
                'success': False,
                'error': 'You do not have administrator permissions in this server or are not the server owner'
            }), 403

        # Check if bot is in the server via shard communication
        bot_in_server = check_bot_in_server_via_shard(str(server_id))
        if not bot_in_server:
            # Bot is not in server - return special response to trigger bot invite
            return jsonify({
                'success': False,
                'error': 'bot_not_present',
                'message': 'Bot needs to be invited to this server first',
                'server_name': user_guild.get('name', 'Unknown Server'),
                'bot_invite_url': f'https://discord.com/oauth2/authorize?client_id=1389466386396483714&scope=bot%20applications.commands&permissions=8&guild_id={server_id}'
            }), 400

        # Check if server already has a license
        existing_license = db.get_server_license_key(server_id)
        if existing_license:
            return jsonify({
                'success': False,
                'error': 'This server already has an active license key'
            }), 400

        # License key system removed - replaced with subscription system
        return jsonify({
            'success': False,
            'error': 'License key system has been replaced with subscription system. Please visit /shop to subscribe.'
        }), 400

    except Exception as e:
        logger.error(f"Error in deprecated license key endpoint: {e}")
        return jsonify({
            'success': False,
            'error': 'This feature has been deprecated'
        }), 500

@app.route('/logout')
def logout():
    """Logout and clear session with security measures"""
    # Log the logout attempt
    user_info = session.get('discord_user', {})
    username = user_info.get('username', 'Unknown User')
    user_id = user_info.get('id', 'Unknown ID')
    
    # Log the successful logout
    logger.info(f'User {username} (ID: {user_id}) logged out successfully')
    
    # Clear the session data
    session.clear()
    
    # Clear the session cookie by setting it to expire
    session.modified = True
    
    # Add security headers to prevent caching of sensitive pages
    response = redirect(url_for('index'))
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    
    flash('You have been successfully logged out.', 'success')
    return response

@app.route('/debug/session-info')
@login_required
def debug_session_info():
    """Debug route to check session size and contents"""
    if not session.get('discord_user', {}).get('id') == '1378705093301375026':  # Only for specific user
        return jsonify({'error': 'Unauthorized'}), 403

    session_size = get_session_size()
    session_keys = list(session.keys())

    # Get size of individual session components
    component_sizes = {}
    for key in session_keys:
        try:
            import pickle
            component_sizes[key] = len(pickle.dumps(session[key]))
        except:
            component_sizes[key] = 0

    return jsonify({
        'total_size': session_size,
        'session_keys': session_keys,
        'component_sizes': component_sizes,
        'guild_count': len(session.get('guilds', [])),
        'user_servers_count': len(session.get('user_servers', []))
    })

@app.route('/debug/permissions')
@login_required
def debug_permissions():
    """Debug route to check user permissions"""
    guilds = session.get('guilds', [])

    permission_info = []
    for guild in guilds[:10]:  # Limit to first 10 guilds for readability
        try:
            permissions = int(guild.get('permissions', 0))
        except (ValueError, TypeError):
            permissions = 0

        is_owner = guild.get('owner', False)
        has_admin = (permissions & 0x8) != 0

        permission_info.append({
            'id': guild.get('id'),
            'name': guild.get('name', 'Unknown'),
            'permissions': permissions,
            'permissions_binary': bin(permissions),
            'is_owner': is_owner,
            'has_admin': has_admin,
            'eligible': has_admin or is_owner
        })

    return jsonify({
        'total_guilds': len(guilds),
        'showing_first': min(10, len(guilds)),
        'permission_details': permission_info
    })

@app.route('/configure/repping', methods=['GET', 'POST'])
@login_required
@require_premium_feature('repping')
def configure_repping():
    """Configure the repping system"""
    if 'server_id' not in session:
        return redirect(url_for('select_server'))

    server_id = session['server_id']

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    server_info = get_server_info(server_id_int)

    if not server_info:
        flash('Could not fetch server information. Please try again.', 'danger')
        return redirect(url_for('dashboard'))

    # Get current configuration
    config = db.get_server_config(server_id_int) or {}


    
    if request.method == 'POST':
        # Get form data
        repping_role_id = request.form.get('repping_role')
        repping_channel_id = request.form.get('repping_channel')
        trigger_word = request.form.get('trigger_word', '/ryzuo').strip()
        
        # Validate trigger word
        if not trigger_word:
            flash('Please enter a trigger word', 'danger')
            return redirect(url_for('configure_repping'))
            
        # Prepare updates
        updates = {
            'repping_role_id': repping_role_id,
            'repping_channel_id': repping_channel_id,
            'trigger_word': trigger_word,
            'updated_at': datetime.now(timezone.utc)
        }
        
        # Add debugging
        logger.info(f"Saving repping config for server {server_id_int}: {updates}")

        # Save to database using the same method as other configs
        success = db.save_server_config(server_id_int, updates)
        logger.info(f"Repping config save result: {success}")

        if success:
            flash('Repping system configuration saved successfully!', 'success')

            # Update session with new trigger word
            session['trigger_word'] = trigger_word
            session.modified = True

            # Check if we need to redirect to dashboard or stay on page
            if 'save_and_continue' in request.form:
                return redirect(url_for('dashboard'))
            return redirect(url_for('configure_repping'))
        else:
            flash('Failed to save configuration. Please try again.', 'danger')
    
    # Get current values or defaults (check both new and legacy field names)
    repping_config = {
        'role_id': config.get('repping_role_id') or config.get('role_id', ''),
        'channel_id': config.get('repping_channel_id') or config.get('channel_id', ''),
        'trigger_word': config.get('trigger_word', '/ryzuo')
    }

    # Get server info for the template
    server_icon = f"https://cdn.discordapp.com/icons/{server_id_int}/{server_info.get('icon')}.png" if server_info.get('icon') else None
    
    return render_template('configure_repping.html',
                         server_name=server_info.get('name', 'Unknown Server'),
                         server_icon=server_icon,
                         config=repping_config,
                         title='Configure Repping System')

@app.route('/configure/vent', methods=['GET', 'POST'])
@require_auth
@require_premium_feature('vent')
def configure_vent():
    """Configure vent system"""
    if 'server_id' not in session:
        return redirect(url_for('select_server'))

    server_id = session['server_id']

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))



    server_info = get_server_info(server_id_int)

    if not server_info:
        flash('Could not fetch server information. Please try again.', 'danger')
        return redirect(url_for('dashboard'))
        
    # Get current configuration
    config = db.get_server_config(server_id) or {}

    if request.method == 'POST':
        vent_channel_id = request.form.get('vent_channel_id', '').strip()

        if not vent_channel_id:
            flash('Vent channel ID is required', 'error')
            return redirect(url_for('configure_vent'))

        try:
            vent_channel_id = int(vent_channel_id)
        except ValueError:
            flash('Invalid channel ID', 'error')
            return redirect(url_for('configure_vent'))

        # Log channel is recommended but not required

        # Add debugging
        logger.info(f"Saving vent settings for server {server_id_int}, channel {vent_channel_id}")

        # Save vent settings
        success = db.set_vent_settings(server_id_int, vent_channel_id)
        logger.info(f"Vent settings save result: {success}")

        if success:
            flash('Vent system configured successfully!', 'success')
        else:
            flash('Failed to save vent configuration', 'error')

        return redirect(url_for('dashboard'))

    # GET request - show form
    vent_settings = db.get_vent_settings(server_id_int)
    config = db.get_server_config(server_id_int)
    logging_config = db.get_logging_config(server_id_int)
    server_info = get_server_info(server_id_int)

    return render_template('configure_vent.html',
                         vent_settings=vent_settings,
                         config=config,
                         logging_config=logging_config,
                         server_info=server_info)

@app.route('/configure/tempvoice', methods=['GET', 'POST'])
@require_auth
@require_premium_feature('tempvoice')
def configure_tempvoice():
    """Configure temp voice system"""
    logger.info(f"Tempvoice configuration accessed. Method: {request.method}")
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))



    if request.method == 'POST':
        logger.info(f"POST request received for tempvoice configuration. Form data: {request.form}")
        interface_channel_id = request.form.get('interface_channel_id', '').strip()
        creator_channel_id = request.form.get('creator_channel_id', '').strip()
        default_user_limit = request.form.get('default_user_limit', '').strip()

        logger.info(f"Parsed values: interface={interface_channel_id}, creator={creator_channel_id}, limit={default_user_limit}")

        if not all([interface_channel_id, creator_channel_id]):
            logger.warning(f"Missing required fields: interface={bool(interface_channel_id)}, creator={bool(creator_channel_id)}")
            flash('Interface and creator channel IDs are required', 'error')
            return redirect(url_for('configure_tempvoice'))

        try:
            interface_channel_id = int(interface_channel_id)
            creator_channel_id = int(creator_channel_id)
            default_user_limit = int(default_user_limit) if default_user_limit else None
        except ValueError:
            flash('Invalid channel ID or user limit', 'error')
            return redirect(url_for('configure_tempvoice'))

        # Save tempvoice settings
        logger.info(f"Attempting to save tempvoice settings for server {server_id_int}: interface={interface_channel_id}, creator={creator_channel_id}, limit={default_user_limit}")
        success = db.set_tempvoice_settings(server_id_int, interface_channel_id, creator_channel_id, default_user_limit)

        if success:
            logger.info(f"Successfully saved tempvoice settings for server {server_id_int}")

            # Send request to bot to post the interface
            try:
                from shard_communication import shard_comm

                # Find the correct shard
                shard_id = shard_comm.find_server_shard(str(server_id_int))
                logger.info(f"Found shard {shard_id} for server {server_id_int}")

                if shard_id is None:
                    logger.error(f"Could not find shard for server {server_id_int}")
                    flash('Temp voice system configured successfully! Please manually post the interface using the bot commands.', 'warning')
                else:
                    # Make the request to post interface
                    result = shard_comm.run_async(shard_comm._make_shard_request(
                        shard_id,
                        f'server/{server_id_int}/post-tempvoice-interface',
                        'POST',
                        {
                            'interface_channel_id': interface_channel_id,
                            'creator_channel_id': creator_channel_id
                        }
                    ))

                    logger.info(f"Shard request result: {result}")

                    if result and result.get('success'):
                        flash('Temp voice system configured successfully! Interface posted to channel.', 'success')
                    elif result and result.get('error'):
                        logger.error(f"Shard returned error: {result.get('error')}")
                        flash(f'Temp voice system configured successfully! Error posting interface: {result.get("error")}', 'warning')
                    else:
                        logger.warning(f"Unexpected shard response: {result}")
                        flash('Temp voice system configured successfully! Interface posting may have failed - check the channel.', 'warning')

            except Exception as e:
                logger.error(f"Error posting tempvoice interface: {e}", exc_info=True)
                flash('Temp voice system configured successfully! Error posting interface - please use bot commands to post manually.', 'warning')
        else:
            logger.error(f"Failed to save tempvoice settings for server {server_id_int}")
            flash('Failed to save temp voice configuration', 'error')

        return redirect(url_for('dashboard'))

    # GET request - show form
    tempvoice_settings = db.get_tempvoice_settings(server_id_int)
    server_info = get_server_info(server_id_int)

    return render_template('configure_tempvoice.html',
                         tempvoice_settings=tempvoice_settings,
                         server_info=server_info)

@app.route('/configure/music', methods=['GET', 'POST'])
@require_auth
@require_premium_feature('music')
def configure_music():
    """Configure music system"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    # Check if server has license
    if not db.is_server_licensed(server_id_int):
        flash('This server does not have a valid license', 'error')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'update_dj_role':
            dj_role_id = request.form.get('dj_role_id', '').strip()

            if dj_role_id:
                try:
                    dj_role_id = int(dj_role_id)
                except ValueError:
                    flash('Invalid DJ role ID', 'error')
                    return redirect(url_for('configure_music'))
            else:
                dj_role_id = None

            # Update DJ role (will create settings if they don't exist due to upsert)
            success = db.update_music_dj_role(server_id_int, dj_role_id)

            if success:
                if dj_role_id:
                    flash('DJ role updated successfully!', 'success')
                else:
                    flash('DJ role removed successfully!', 'success')
            else:
                flash('Failed to update DJ role', 'error')

        return redirect(url_for('configure_music'))

    # GET request - show form
    music_settings = db.get_music_settings(server_id_int)
    server_info = get_server_info(server_id_int)

    return render_template('configure_music.html',
                         music_settings=music_settings,
                         server_info=server_info)

@app.route('/user-settings', methods=['GET', 'POST'])
@require_auth
def user_settings():
    """User settings page for personal preferences and account management"""
    user_id = session.get('user_id')
    discord_user = session.get('discord_user')

    if request.method == 'POST':
        # Handle user settings updates here
        # For now, just show a success message
        flash('User settings updated successfully!', 'success')
        return redirect(url_for('user_settings'))

    # Get user's subscription
    user_subscription = db.get_user_subscription(user_id)

    # Get user's owned servers
    user_owned_servers = get_user_owned_servers(user_id)

    # Enhance owned servers with additional info
    for server in user_owned_servers:
        try:
            # Get more detailed server info if needed
            server_info = get_server_info(server['id'])
            if server_info:
                server.update({
                    'member_count': server_info.get('member_count', 0),
                    'features': server_info.get('features', [])
                })
        except Exception as e:
            logger.error(f"Error getting detailed server info for {server['id']}: {e}")

    return render_template('user_settings.html',
                         discord_user=discord_user,
                         user_subscription=user_subscription,
                         user_owned_servers=user_owned_servers)

@app.route('/settings', methods=['GET', 'POST'])
@require_auth
def settings():
    """Server settings page including ignored users and other configurations"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    if request.method == 'POST':
        action = request.form.get('action')
        logger.info(f"Settings POST request - action: {action}, form data: {dict(request.form)}")

        if action == 'add_ignored_user':
            user_id = request.form.get('user_id', '').strip()

            if not user_id:
                flash('User ID is required', 'error')
                return redirect(url_for('settings'))

            try:
                user_id = int(user_id)
            except ValueError:
                flash('Invalid user ID', 'error')
                return redirect(url_for('settings'))

            success = db.add_ignored_user(server_id_int, user_id)
            if success:
                flash('User added to ignored list successfully!', 'success')
                # Log the action
                db.log_bot_activity(
                    server_id_int,
                    session['user_id'],
                    "Web Dashboard User",
                    "User added to ignored list",
                    f"User ID: {user_id}",
                    "config"
                )
            else:
                flash('Failed to add user to ignored list', 'error')

        elif action == 'remove_ignored_user':
            user_id = request.form.get('user_id', '').strip()

            try:
                user_id = int(user_id)
            except ValueError:
                flash('Invalid user ID', 'error')
                return redirect(url_for('settings'))

            success = db.remove_ignored_user(server_id_int, user_id)
            if success:
                flash('User removed from ignored list successfully!', 'success')
                # Log the action
                db.log_bot_activity(
                    server_id_int,
                    session['user_id'],
                    "Web Dashboard User",
                    "User removed from ignored list",
                    f"User ID: {user_id}",
                    "config"
                )
            else:
                flash('Failed to remove user from ignored list', 'error')



        return redirect(url_for('settings'))

    # GET request - show settings page
    config = db.get_server_config(server_id_int)
    server_info = get_server_info(server_id_int)
    ignored_users = config.get('ignored_users', []) if config else []

    # Get license key information for this server
    license_key = db.get_server_license_key_full(server_id_int)

    return render_template('settings.html',
                         config=config,
                         ignored_users=ignored_users,
                         server_info=server_info,
                         license_key=license_key)

@app.route('/transfer-license-server', methods=['POST'])
@require_auth
def transfer_license_server():
    """Transfer license key to a different server"""
    try:
        data = request.get_json()
        target_server_id = data.get('target_server_id')

        if not target_server_id:
            return jsonify({'success': False, 'error': 'Target server ID is required'})

        # Validate server ID format
        try:
            target_server_id = int(target_server_id)
        except ValueError:
            return jsonify({'success': False, 'error': 'Invalid server ID format'})

        current_server_id = session.get('server_id')
        user_id = session.get('user_id')

        if not current_server_id or not user_id:
            return jsonify({'success': False, 'error': 'Session invalid'})

        current_server_id = int(current_server_id)

        # Get current license key for this server
        current_license = db.get_server_license_key_full(current_server_id)
        if not current_license:
            return jsonify({'success': False, 'error': 'No license key found for current server'})

        # Check if user owns the license
        if current_license.get('redeemed_by') != user_id:
            return jsonify({'success': False, 'error': 'You do not own this license key'})

        # Check if it's the same server (no transfer needed)
        if target_server_id == current_server_id:
            return jsonify({'success': False, 'error': 'Cannot transfer license to the same server'})

        # Check if target server already has a license
        existing_license = db.get_server_license_key(target_server_id)
        if existing_license:
            return jsonify({'success': False, 'error': 'Target server already has an active license key'})

        # Get user's guilds to verify they own the target server
        guilds = session.get('guilds', [])
        target_guild = None
        for guild in guilds:
            if int(guild['id']) == target_server_id:
                target_guild = guild
                break

        if not target_guild:
            return jsonify({'success': False, 'error': 'Target server not found or you are not a member'})

        # Check if user is owner of target server
        if not target_guild.get('owner'):
            return jsonify({'success': False, 'error': 'You must be the owner of the target server'})

        # Transfer the license (this will automatically clean up old server data since it's different)
        success = db.transfer_key_to_server(current_license['key'], user_id, target_server_id)

        if success:
            logger.info(f"License key transferred from server {current_server_id} to {target_server_id} by user {user_id}")
            return jsonify({'success': True, 'message': 'License transferred successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to transfer license key'})

    except Exception as e:
        logger.error(f"Error transferring license to server: {e}")
        return jsonify({'success': False, 'error': 'An unexpected error occurred'})

@app.route('/transfer-license-user', methods=['POST'])
@require_auth
def transfer_license_user():
    """Transfer license key ownership to another user"""
    try:
        data = request.get_json()
        target_user_id = data.get('target_user_id')

        if not target_user_id:
            return jsonify({'success': False, 'error': 'Target user ID is required'})

        # Validate user ID format
        try:
            target_user_id = int(target_user_id)
        except ValueError:
            return jsonify({'success': False, 'error': 'Invalid user ID format'})

        current_server_id = session.get('server_id')
        user_id = session.get('user_id')

        if not current_server_id or not user_id:
            return jsonify({'success': False, 'error': 'Session invalid'})

        current_server_id = int(current_server_id)

        if target_user_id == user_id:
            return jsonify({'success': False, 'error': 'Cannot transfer license to yourself'})

        # Get current license key for this server
        current_license = db.get_server_license_key_full(current_server_id)
        if not current_license:
            return jsonify({'success': False, 'error': 'No license key found for current server'})

        # Check if user owns the license
        if current_license.get('redeemed_by') != user_id:
            return jsonify({'success': False, 'error': 'You do not own this license key'})

        # Transfer the license ownership (this will automatically reset all server data since new owner needs to reconfigure)
        success = db.transfer_key_to_user(current_license['key'], user_id, target_user_id)

        if success:
            logger.info(f"License key ownership transferred from user {user_id} to {target_user_id} for server {current_server_id}")
            return jsonify({'success': True, 'message': 'License ownership transferred successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to transfer license ownership'})

    except Exception as e:
        logger.error(f"Error transferring license to user: {e}")
        return jsonify({'success': False, 'error': 'An unexpected error occurred'})

@app.route('/configure/dm-support', methods=['GET', 'POST'])
@require_auth
@require_premium_feature('dm_support')
def configure_dm_support():
    """Configure DM support system"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))


    if request.method == 'POST':
        category_id = request.form.get('category_id', '').strip()
        support_role_id = request.form.get('support_role_id', '').strip()

        if not all([category_id, support_role_id]):
            flash('All fields are required', 'error')
            return redirect(url_for('configure_dm_support'))

        try:
            category_id = int(category_id)
            support_role_id = int(support_role_id)
        except ValueError:
            flash('Invalid category ID or role ID', 'error')
            return redirect(url_for('configure_dm_support'))


        # Create support-logs channel name
        support_logs_channel_id = None  # Will be created by the bot if needed

        # Save DM support settings
        success = db.set_dm_support_settings(server_id_int, category_id, support_role_id, support_logs_channel_id)

        if success:
            flash('DM support system configured successfully!', 'success')
            # Log the action
            db.log_bot_activity(
                server_id_int,
                session['user_id'],
                "Web Dashboard User",
                "DM support system configured",
                f"Category ID: {category_id}, Support Role ID: {support_role_id}",
                "config"
            )
        else:
            flash('Failed to save DM support configuration', 'error')

        return redirect(url_for('dashboard'))

    # GET request - show form
    dm_support_settings = db.get_dm_support_settings(server_id_int)
    config = db.get_server_config(server_id_int)
    server_info = get_server_info(server_id_int)

    return render_template('configure_dm_support.html',
                         dm_support_settings=dm_support_settings,
                         config=config,
                         server_info=server_info)

@app.route('/configure/auto-roling', methods=['GET', 'POST'])
@require_auth
@require_premium_feature('auto_roling')
def configure_auto_roling():
    """Configure auto-roling system"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))



    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'configure':
            role_id = request.form.get('role_id')
            enabled = 'enabled' in request.form

            if not role_id:
                flash('Please select a role for auto-roling', 'error')
            else:
                try:
                    role_id_int = int(role_id)

                    # Save auto-roling settings
                    if db.set_auto_roling_settings(server_id_int, role_id_int, enabled):
                        # Clear any previous permission errors
                        db.clear_auto_roling_error(server_id_int)

                        # Log the configuration change
                        user_id = session.get('user_id')
                        username = session.get('discord_user', {}).get('username', 'Unknown User')
                        log_dashboard_activity_comprehensive(
                            server_id_int, user_id, username,
                            f"Auto-roling configured: Role ID {role_id_int}, Enabled: {enabled}",
                            f"Role: <@&{role_id_int}>, Status: {'Enabled' if enabled else 'Disabled'}",
                            "dashboard_updates"
                        )

                        flash('Auto-roling settings saved successfully!', 'success')
                    else:
                        flash('Failed to save auto-roling settings', 'error')
                except ValueError:
                    flash('Invalid role ID', 'error')

        elif action == 'toggle':
            enabled = 'enabled' in request.form

            if db.update_auto_roling_status(server_id_int, enabled):
                # Clear permission errors if re-enabling
                if enabled:
                    db.clear_auto_roling_error(server_id_int)

                # Log the status change
                user_id = session.get('user_id')
                username = session.get('discord_user', {}).get('username', 'Unknown User')
                log_dashboard_activity_comprehensive(
                    server_id_int, user_id, username,
                    f"Auto-roling {'enabled' if enabled else 'disabled'}",
                    f"Status changed to: {'Enabled' if enabled else 'Disabled'}",
                    "dashboard_updates"
                )

                flash(f'Auto-roling {"enabled" if enabled else "disabled"} successfully!', 'success')
            else:
                flash('Failed to update auto-roling status', 'error')

        elif action == 'clear_error':
            if db.clear_auto_roling_error(server_id_int):
                # Log the error clearance
                user_id = session.get('user_id')
                username = session.get('discord_user', {}).get('username', 'Unknown User')
                log_dashboard_activity_comprehensive(
                    server_id_int, user_id, username,
                    "Auto-roling permission error cleared",
                    "Permission error status reset",
                    "dashboard_updates"
                )

                flash('Permission error cleared. You can now re-enable auto-roling.', 'success')
            else:
                flash('Failed to clear permission error', 'error')



        return redirect(url_for('configure_auto_roling'))

    # GET request - show form
    auto_roling_settings = db.get_auto_roling_settings(server_id_int)
    logging_config = db.get_logging_config(server_id_int)
    server_info = get_server_info(server_id_int)

    # Convert ObjectId to string for JSON serialization
    if auto_roling_settings and '_id' in auto_roling_settings:
        auto_roling_settings['_id'] = str(auto_roling_settings['_id'])

    return render_template('configure_auto_roling.html',
                         auto_roling_settings=auto_roling_settings,
                         logging_config=logging_config,
                         server_info=server_info)

@app.route('/configure/gender-verification', methods=['GET', 'POST'])
@require_auth
def configure_gender_verification():
    """Configure gender verification system"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))



    if request.method == 'POST':
        channel_id = request.form.get('channel_id', '').strip()
        category_id = request.form.get('category_id', '').strip()
        support_role_id = request.form.get('support_role_id', '').strip()
        paper_text = request.form.get('paper_text', '').strip()

        if not all([channel_id, category_id, support_role_id, paper_text]):
            flash('All fields are required', 'error')
            return redirect(url_for('configure_gender_verification'))

        try:
            channel_id = int(channel_id)
            category_id = int(category_id)
            support_role_id = int(support_role_id)
        except ValueError:
            flash('Invalid channel ID, category ID, or role ID', 'error')
            return redirect(url_for('configure_gender_verification'))

        # Save gender verification settings
        success = db.set_gender_verification_settings(server_id_int, channel_id, category_id, support_role_id, paper_text)

        if success:
            flash('Gender verification system configured successfully!', 'success')
            # Log the action
            db.log_bot_activity(
                server_id_int,
                session['user_id'],
                "Web Dashboard User",
                "Gender verification system configured",
                f"Channel ID: {channel_id}, Category ID: {category_id}, Support Role ID: {support_role_id}",
                "config"
            )
        else:
            flash('Failed to save gender verification configuration', 'error')

        return redirect(url_for('dashboard'))

    # GET request - show form
    gender_verification_settings = db.get_gender_verification_settings(server_id_int)
    server_info = get_server_info(server_id_int)

    return render_template('configure_gender_verification.html',
                         gender_verification_settings=gender_verification_settings,
                         server_info=server_info)

@app.route('/api/toggle-feature', methods=['POST'])
@require_auth
def toggle_feature():
    """API endpoint to enable/disable features"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        return jsonify({'success': False, 'error': 'Invalid server ID'}), 400

    try:
        data = request.get_json()
        feature = data.get('feature')
        enabled = data.get('enabled', True)

        if not feature:
            return jsonify({'success': False, 'error': 'Feature name is required'}), 400

        # Check if this is a premium feature and if user has access
        validation = validate_premium_feature_access(server_id_int, feature)
        if not validation['has_access']:
            return jsonify({
                'success': False,
                'error': f'{feature.title()} is a premium feature. Please subscribe to access it.',
                'is_premium': validation['is_premium'],
                'reason': validation['reason'],
                'redirect_url': validation['redirect_url']
            }), 403

        success = False



        if feature == 'repping':
            # Update repping system enabled status in server config
            success = db.update_server_config_field(server_id_int, 'repping_enabled', enabled)

        elif feature == 'vent':
            # Update vent settings
            vent_settings = db.get_vent_settings(server_id_int)
            if vent_settings:
                success = db.update_vent_settings_enabled(server_id_int, enabled)
            else:
                return jsonify({'success': False, 'error': 'Vent system not configured'}), 400

        elif feature == 'tempvoice':
            # Update tempvoice settings
            tempvoice_settings = db.get_tempvoice_settings(server_id_int)
            if tempvoice_settings:
                success = db.update_tempvoice_settings_enabled(server_id_int, enabled)
            else:
                return jsonify({'success': False, 'error': 'TempVoice system not configured'}), 400

        elif feature == 'music':
            # Update music settings (will create if doesn't exist due to upsert)
            logger.info(f"Toggling music feature for server {server_id_int}, enabled: {enabled}")
            success = db.update_music_settings_enabled(server_id_int, enabled)
            logger.info(f"Music settings update result: {success}")

        elif feature == 'dm_support':
            # Update DM support settings
            dm_support_settings = db.get_dm_support_settings(server_id_int)
            if dm_support_settings:
                success = db.update_dm_support_settings_enabled(server_id_int, enabled)
            else:
                return jsonify({'success': False, 'error': 'DM Support system not configured'}), 400

        elif feature == 'sticky_messages':
            # Update sticky messages enabled status
            sticky_messages = db.get_all_sticky_messages(server_id_int)
            if sticky_messages:
                success = db.update_sticky_messages_enabled(server_id_int, enabled)
            else:
                return jsonify({'success': False, 'error': 'No sticky messages configured'}), 400

        elif feature == 'gender_verification':
            # Update gender verification settings
            gender_verification_settings = db.get_gender_verification_settings(server_id_int)
            if gender_verification_settings:
                success = db.update_gender_verification_settings_enabled(server_id_int, enabled)
            else:
                return jsonify({'success': False, 'error': 'Gender verification system not configured'}), 400

        elif feature == 'auto_roling':
            # Update auto-roling settings
            auto_roling_settings = db.get_auto_roling_settings(server_id_int)
            if auto_roling_settings and auto_roling_settings.get('role_id'):
                # Clear permission errors when enabling
                if enabled:
                    db.clear_auto_roling_error(server_id_int)
                success = db.update_auto_roling_status(server_id_int, enabled)
            else:
                return jsonify({'success': False, 'error': 'Auto-roling system not configured'}), 400

        else:
            return jsonify({'success': False, 'error': 'Unknown feature'}), 400

        if success:
            # Log the action
            log_dashboard_activity_comprehensive(
                server_id_int,
                session['user_id'],
                "Web Dashboard User",
                f"{feature.title()} system {'enabled' if enabled else 'disabled'}",
                f"Feature toggled via dashboard",
                "config"
            )

            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'Failed to update feature settings'}), 500

    except Exception as e:
        logger.error(f"Error toggling feature: {e}", exc_info=True)
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@app.route('/configure/logs', methods=['GET', 'POST'])
@require_auth
def configure_logs():
    """Configure comprehensive logging system"""
    if 'server_id' not in session:
        return redirect(url_for('select_server'))

    server_id = session['server_id']

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'set_global_settings':
            # Handle global logging settings
            dashboard_enabled = 'enable_dashboard_logging' in request.form
            discord_enabled = 'enable_discord_logging' in request.form
            log_channel_id = request.form.get('log_channel_id', '').strip()

            # Convert empty string to None
            if not log_channel_id:
                log_channel_id = None
            else:
                try:
                    log_channel_id = int(log_channel_id)
                except ValueError:
                    flash('Invalid channel ID', 'error')
                    return redirect(url_for('configure_logs'))

            # Update logging config with global settings
            logging_config = db.get_logging_config(server_id_int)
            logging_config['dashboard_enabled'] = dashboard_enabled
            logging_config['discord_enabled'] = discord_enabled
            logging_config['log_channel_id'] = log_channel_id

            # Debug logging
            logger.info(f"[DEBUG] Saving global logging settings for server {server_id_int}")
            logger.info(f"[DEBUG] Dashboard enabled: {dashboard_enabled}")
            logger.info(f"[DEBUG] Discord enabled: {discord_enabled}")
            logger.info(f"[DEBUG] Log channel ID: {log_channel_id}")

            success = db.update_logging_config(server_id_int, logging_config)

            if success:
                flash('Global logging settings updated successfully!', 'success')
                # Log the action
                settings_desc = []
                if dashboard_enabled:
                    settings_desc.append("Dashboard logging enabled")
                if discord_enabled:
                    settings_desc.append(f"Discord logging enabled (Channel: {log_channel_id})")

                log_dashboard_activity_comprehensive(
                    server_id_int,
                    session['user_id'],
                    "Web Dashboard User",
                    "Global logging settings updated",
                    "; ".join(settings_desc) if settings_desc else "All logging disabled",
                    "config",
                    log_channel_id
                )
            else:
                flash('Failed to update global logging settings', 'error')

        elif action == 'migrate_to_new_defaults':
            # Handle migration to new default enabled events
            success = db.migrate_logging_config_to_new_defaults(server_id_int)

            if success:
                flash('Successfully migrated to new logging defaults! Many more event types are now enabled.', 'success')
                log_dashboard_activity_comprehensive(
                    server_id_int,
                    session['user_id'],
                    "Web Dashboard User",
                    "Migrated logging configuration to new defaults",
                    "Enabled additional Discord event logging types",
                    "config"
                )
            else:
                flash('Failed to migrate logging configuration', 'error')

        elif action == 'update_config':
            # Handle logging configuration update
            logging_config = db.get_logging_config(server_id_int)

            # Process all log types from the default configuration to ensure we have all types
            default_config = db._get_default_logging_config()
            for log_type in default_config['log_types'].keys():
                enabled = f'enabled_{log_type}' in request.form
                dashboard = f'dashboard_{log_type}' in request.form
                discord = f'discord_{log_type}' in request.form
                color = request.form.get(f'color_{log_type}', default_config['log_types'][log_type]['color'])

                logging_config['log_types'][log_type] = {
                    'enabled': enabled,
                    'dashboard': dashboard,
                    'discord': discord,
                    'color': color
                }

            success = db.update_logging_config(server_id_int, logging_config)

            if success:
                flash('Logging configuration updated successfully!', 'success')
                # Log the action
                enabled_count = sum(1 for log_type in logging_config['log_types'].values() if log_type['enabled'])
                # Use a more specific category that maps to dashboard_updates
                log_dashboard_activity_comprehensive(
                    server_id_int,
                    session['user_id'],
                    "Web Dashboard User",
                    "Logging configuration updated",
                    f"Enabled {enabled_count} log types",
                    "config"  # This maps to dashboard_updates
                )
            else:
                flash('Failed to update logging configuration', 'error')

        return redirect(url_for('configure_logs'))

    # GET request - show form
    logging_config = db.get_logging_config(server_id_int)
    server_info = get_server_info(server_id_int)

    return render_template('configure_logs.html',
                         logging_config=logging_config,
                         server_info=server_info)

@app.route('/configure/sticky-messages', methods=['GET', 'POST'])
@require_auth
@require_premium_feature('sticky_messages')
def configure_sticky_messages():
    """Configure sticky messages"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))



    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'create':
            channel_id = request.form.get('channel_id', '').strip()
            content = request.form.get('content', '').strip()

            if not all([channel_id, content]):
                flash('Channel and content are required', 'error')
                return redirect(url_for('configure_sticky_messages'))

            try:
                channel_id = int(channel_id)
            except ValueError:
                flash('Invalid channel ID', 'error')
                return redirect(url_for('configure_sticky_messages'))

            # Add debugging
            logger.info(f"Creating sticky message for server {server_id_int}, channel {channel_id}")

            # Check if sticky message already exists for this channel
            existing_sticky = db.get_sticky_message(server_id_int, channel_id)

            # Create or update sticky message
            success = db.create_sticky_message(server_id_int, channel_id, content)
            logger.info(f"Sticky message creation result: {success}")

            if success:
                if existing_sticky:
                    flash('Sticky message updated successfully!', 'success')
                else:
                    flash('Sticky message created successfully!', 'success')
                # Log the action
                db.log_bot_activity(
                    server_id_int,
                    session['user_id'],
                    "Web Dashboard User",
                    "Sticky message created",
                    f"Channel ID: {channel_id}, Content length: {len(content)} characters",
                    "sticky",
                    channel_id
                )
            else:
                flash('Failed to create sticky message', 'error')

        elif action == 'remove':
            channel_id = request.form.get('channel_id', '').strip()

            try:
                channel_id = int(channel_id)
            except ValueError:
                flash('Invalid channel ID', 'error')
                return redirect(url_for('configure_sticky_messages'))

            # Remove sticky message
            success = db.remove_sticky_message(server_id_int, channel_id)

            if success:
                flash('Sticky message removed successfully!', 'success')
                # Log the action with comprehensive logging
                log_dashboard_activity_comprehensive(
                    server_id_int,
                    session['user_id'],
                    "Web Dashboard User",
                    "Sticky message removed",
                    f"Channel ID: {channel_id}",
                    "sticky"  # This maps to sticky_created, but we need sticky_deleted
                )

                # Also log specifically as sticky_deleted
                db.log_bot_activity_with_discord(
                    server_id_int,
                    session['user_id'],
                    "Web Dashboard User",
                    "Sticky message deleted",
                    f"Channel ID: {channel_id}",
                    "sticky_deleted"  # Use specific category for deletion
                )
            else:
                flash('Failed to remove sticky message', 'error')

        return redirect(url_for('configure_sticky_messages'))

    # GET request - show form
    sticky_messages = db.get_all_sticky_messages(server_id_int)
    server_info = get_server_info(server_id_int)

    # Convert ObjectIds to strings for JSON serialization
    sticky_messages = convert_objectids_to_strings(sticky_messages)

    return render_template('configure_sticky_messages.html',
                         sticky_messages=sticky_messages,
                         server_info=server_info)

# Keep the old automod route for backward compatibility
@app.route('/configure/automod')
@require_auth
def configure_automod():
    """Redirect to settings page"""
    return redirect(url_for('settings'))

@app.after_request
def add_cors_headers(response):
    # Only allow CORS for our own domain and specific trusted origins
    origin = request.headers.get('Origin')
    allowed_origins = [
        'https://ryzuo.com',
        'https://www.ryzuo.com',
        'http://localhost:5000',
        'http://127.0.0.1:5000'
    ]

    if origin in allowed_origins:
        response.headers['Access-Control-Allow-Origin'] = origin
        response.headers['Access-Control-Allow-Credentials'] = 'true'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, X-CSRFToken, X-Requested-With, Authorization'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'

    # Add security headers
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'

    return response

@app.before_request
def handle_preflight():
    """Handle CORS preflight requests"""
    if request.method == "OPTIONS":
        response = jsonify({'status': 'ok'})
        origin = request.headers.get('Origin')
        allowed_origins = [
            'https://ryzuo.com',
            'https://www.ryzuo.com',
            'http://localhost:5000',
            'http://127.0.0.1:5000'
        ]

        if origin in allowed_origins:
            response.headers['Access-Control-Allow-Origin'] = origin
            response.headers['Access-Control-Allow-Credentials'] = 'true'
            response.headers['Access-Control-Allow-Headers'] = 'Content-Type, X-CSRFToken, X-Requested-With, Authorization'
            response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'

        return response

@app.route('/test-tawk')
def test_tawk():
    """Test page for Tawk.to integration"""
    return render_template('test_tawk.html')

@app.route('/api/server-info')
@require_auth
def api_server_info():
    """API endpoint to get server information"""
    try:
        server_id = int(session.get('server_id', 0))
        if not server_id:
            return jsonify({'error': 'No server selected'}), 400
            
        server_info = get_server_info(server_id)
        if not server_info:
            return jsonify({'error': 'Server not found'}), 404

        # Initialize response with basic server info
        response_data = {
            'id': str(server_id),
            'name': server_info.get('name', 'Unknown Server'),
            'icon': server_info.get('icon'),
            'member_count': 0,
            'icon_url': f"https://cdn.discordapp.com/icons/{server_id}/{server_info.get('icon')}.png" if server_info.get('icon') else None,
            'roles': [],
            'channels': []
        }

        # Get member count and other guild info via shard communication
        try:
            server_info_shard = shard_comm.run_async(shard_comm.get_server_info(str(server_id)))
            if server_info_shard:
                response_data['member_count'] = server_info_shard.get('member_count', 0)
                response_data.update({
                    'name': server_info_shard.get('name', response_data['name']),
                    'icon': server_info_shard.get('icon'),
                    'icon_url': server_info_shard.get('icon'),
                    'owner_id': str(server_info_shard.get('owner_id', ''))
                })

                # Get roles via shard communication
                try:
                    roles_data = shard_comm.run_async(shard_comm.get_server_roles(str(server_id)))
                    if roles_data:
                        response_data['roles'] = roles_data
                except Exception as e:
                    logger.warning(f"Failed to get roles via shard: {e}")
                    response_data['roles'] = []

                # Get channels via shard communication
                try:
                    channels_data = shard_comm.run_async(shard_comm.get_server_channels(str(server_id)))
                    if channels_data:
                        response_data['channels'] = channels_data
                except Exception as e:
                    logger.warning(f"Failed to get channels via shard: {e}")
                    response_data['channels'] = []
            else:
                logger.warning(f"Server {server_id} not found via shard communication")
                # Use cached member count if available
                response_data['member_count'] = server_info.get('member_count', 0)
        except Exception as e:
            logger.error(f"Error getting server data via shard: {e}")
            # Use cached member count if available
            response_data['member_count'] = server_info.get('member_count', 0)
        
        # Add configuration from database (convert ObjectId to string)
        config = db.get_server_config(server_id) or {}
        # Convert ObjectId to string for JSON serialization
        if '_id' in config:
            config['_id'] = str(config['_id'])
        response_data['config'] = config

        return jsonify(response_data)
        
    except Exception as e:
        logging.error(f'Error in api_server_info: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/channels')
@require_auth
def api_channels():
    """API endpoint to get server channels"""
    try:
        server_id = session.get('server_id')
        if not server_id:
            logger.error("No server_id in session")
            return jsonify({'error': 'No server selected'}), 400

        # Convert server_id to int for consistency
        try:
            server_id = int(server_id)
        except (ValueError, TypeError) as e:
            logger.error(f"Invalid server_id format: {server_id}, error: {e}")
            return jsonify({'error': 'Invalid server ID format'}), 400

        # Get channels via shard communication
        try:
            channels_data = shard_comm.run_async(shard_comm.get_server_channels(str(server_id)))
            if not channels_data:
                logger.warning("No channels data from shard, returning fallback response")
                return jsonify({
                    'error': 'Server not found',
                    'fallback': True,
                    'message': 'Server not found or bot not in server. Please enter channel IDs manually.'
                }), 404

            return jsonify(channels_data)

        except Exception as e:
            logger.error(f"Error getting channels via shard: {e}")
            return jsonify({
                'error': 'Shard communication failed',
                'fallback': True,
                'message': 'Please enter channel IDs manually. Unable to fetch channels from bot.'
            }), 503

    except Exception as e:
        logger.error(f'Unexpected error in api_channels: {str(e)}', exc_info=True)
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/roles')
@require_auth
def api_roles():
    """API endpoint to get server roles (only roles below bot's highest role)"""
    try:
        server_id = session.get('server_id')
        if not server_id:
            logger.error("No server_id in session")
            return jsonify({'error': 'No server selected'}), 400

        # Convert server_id to int for consistency
        try:
            server_id = int(server_id)
        except (ValueError, TypeError) as e:
            logger.error(f"Invalid server_id format: {server_id}, error: {e}")
            return jsonify({'error': 'Invalid server ID format'}), 400

        # Get roles via shard communication
        try:
            roles_data = shard_comm.run_async(shard_comm.get_server_roles(str(server_id)))
            if not roles_data:
                logger.warning("No roles data from shard, returning fallback response")
                return jsonify({
                    'error': 'Server not found',
                    'fallback': True,
                    'message': 'Server not found or bot not in server. Please enter role IDs manually.'
                }), 404

            return jsonify(roles_data)

        except Exception as e:
            logger.error(f"Error getting roles via shard: {e}")
            return jsonify({
                'error': 'Shard communication failed',
                'fallback': True,
                'message': 'Please enter role IDs manually. Unable to fetch roles from bot.'
            }), 503

    except Exception as e:
        logger.error(f'Unexpected error in api_roles: {str(e)}', exc_info=True)
        return jsonify({'error': 'Internal server error'}), 500

def _get_members(guild):
    """Helper function to get members with proper async handling"""
    try:
        # Ensure the guild is chunked
        if not guild.chunked:
            # Run the coroutine in the bot's event loop
            future = asyncio.run_coroutine_threadsafe(guild.chunk(), bot.loop)
            future.result(timeout=10)  # Wait up to 10 seconds for chunking to complete
        return guild.members
    except Exception as e:
        logger.error(f"Error fetching members for guild {guild.id}: {e}")
        return []

@app.route('/api/members')
@require_auth
def api_members():
    """API endpoint to get server members"""
    server_id = session.get('server_id')
    search_query = request.args.get('q', '').lower()
    
    if not server_id:
        return jsonify({'error': 'No server selected'}), 400

    if not bot:
        return jsonify({'error': 'Bot not connected'}), 500

    try:
        guild = bot.get_guild(server_id)
        if not guild:
            return jsonify({'error': 'Server not found'}), 404

        # Get members synchronously
        members = _get_members(guild)
        
        # Process members
        member_list = []
        for member in members:
            if member.bot:
                continue
                
            # Get avatar URL with fallback to default avatar
            avatar_url = str(member.avatar.url) if member.avatar else f'https://cdn.discordapp.com/embed/avatars/{int(member.discriminator) % 5}.png'
            
            member_list.append({
                'id': str(member.id),
                'name': f'{member.name}#{member.discriminator}',
                'display_name': member.display_name,
                'avatar_url': avatar_url
            })
        
        # Apply search filter if query provided
        if search_query:
            member_list = [
                member for member in member_list 
                if (search_query in member['name'].lower() or 
                     search_query in member['display_name'].lower())
            ]
        
        # Sort by display name
        member_list.sort(key=lambda x: x['display_name'].lower())
        
        return jsonify(member_list)
    except Exception as e:
        logging.error(f'Error fetching members: {str(e)}', exc_info=True)
        return jsonify({'error': 'Failed to fetch members. Please try again.'}), 500

@app.route('/site-logs')
@require_auth
@require_premium_feature('on-site-logs')
def site_logs():
    """Dashboard logs viewer"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    # Get filter parameters
    category = request.args.get('category', '')
    limit = min(int(request.args.get('limit', 100)), 500)  # Max 500 logs

    # Get logs from database
    logs = db.get_bot_logs(server_id_int, limit=limit, category=category if category else None)

    # Get live statistics for activity breakdown
    stats = db.get_log_statistics(server_id_int, days=0)  # Use 0 to indicate all-time stats

    # Get server info
    server_info = get_server_info(server_id_int)

    # Convert ObjectIds to strings for JSON serialization
    logs = convert_objectids_to_strings(logs)
    stats = convert_objectids_to_strings(stats)

    return render_template('site-logs.html',
                         logs=logs,
                         stats=stats,
                         server_info=server_info,
                         current_category=category,
                         current_limit=limit)

@app.route('/api/logs')
@require_auth
def api_logs():
    """API endpoint for logs (for AJAX updates)"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        return jsonify({'error': 'Invalid server ID'}), 400

    category = request.args.get('category', '')
    limit = min(int(request.args.get('limit', 100)), 500)

    logs = db.get_bot_logs(server_id_int, limit=limit, category=category if category else None)

    # Convert ObjectIds and datetime objects to strings for JSON serialization
    logs = convert_objectids_to_strings(logs)
    for log in logs:
        if 'timestamp' in log:
            log['timestamp'] = log['timestamp'].isoformat()

    return jsonify(logs)

@app.route('/stats')
@require_auth
@require_premium_feature('statistics')
def stats():
    """Dashboard stats viewer using new sharding protocol"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        flash('Invalid server ID', 'error')
        return redirect(url_for('select_server'))

    # Get server statistics - try shard communication first, then database with better fallbacks
    try:
        server_stats = shard_comm.run_async(shard_comm.get_server_statistics(str(server_id_int)))
        if not server_stats:
            # Fallback to database if shard communication fails
            server_stats = db.get_server_statistics(server_id_int, None)
    except Exception as e:
        logger.warning(f"Failed to get server stats via shard: {e}")
        server_stats = db.get_server_statistics(server_id_int, None)

    # Ensure we have basic server info even if stats are empty
    if not server_stats or not isinstance(server_stats, dict):
        server_stats = {}

    # Get server info to fill in missing data
    server_info_data = get_server_info(server_id_int)
    if server_info_data:
        # Fill in missing server stats with server info data
        if 'member_count' not in server_stats or server_stats.get('member_count', 0) == 0:
            server_stats['member_count'] = server_info_data.get('member_count', 0)
        if 'channel_count' not in server_stats or server_stats.get('channel_count', 0) == 0:
            server_stats['channel_count'] = server_info_data.get('channel_count', 0)
        if 'premium_subscription_count' not in server_stats:
            server_stats['premium_subscription_count'] = server_info_data.get('premium_subscription_count', 0)
        if 'online_count' not in server_stats:
            server_stats['online_count'] = server_info_data.get('online_count', 0)

    # Get live log statistics for activity breakdown - default to 30 days for better data
    log_stats = db.get_log_statistics(server_id_int, days=30)

    # Get top repping users
    top_repping_users = db.get_top_repping_users(server_id_int, limit=6, bot=None)

    # Get server info using shard communication
    server_info = get_server_info(server_id_int)

    # Convert ObjectIds to strings for JSON serialization
    log_stats = convert_objectids_to_strings(log_stats)
    top_repping_users = convert_objectids_to_strings(top_repping_users)

    return render_template('stats.html',
                         server_stats=server_stats,
                         log_stats=log_stats,
                         server_info=server_info,
                         top_repping_users=top_repping_users)

@app.route('/api/stats-data')
@require_auth
def api_stats_data():
    """API endpoint for stats data with time period filtering using new sharding protocol"""
    server_id = session.get('server_id')

    # Convert to int for consistency
    try:
        server_id_int = int(server_id)
    except (ValueError, TypeError):
        return jsonify({'error': 'Invalid server ID'}), 400

    # Get time period from query params
    period = request.args.get('period', '30')  # Default to 30 days

    try:
        days = int(period)
    except ValueError:
        days = 30

    # Get log statistics for the specified period (this comes from database, not shards)
    log_stats = db.get_log_statistics(server_id_int, days=days)

    # Convert ObjectIds to strings for JSON serialization
    log_stats = convert_objectids_to_strings(log_stats)

    return jsonify({
        'success': True,
        'log_stats': log_stats,
        'period': days
    })

@app.route('/api/homepage-stats')
def api_homepage_stats():
    """API endpoint for homepage statistics (public) - Calculate from actual server data"""
    try:
        # Get all servers from all shards to calculate accurate totals
        all_servers = shard_comm.run_async(shard_comm.get_all_servers_from_all_shards())

        # Calculate totals from actual server data
        total_servers = len(all_servers)
        total_members = sum(server.get('member_count', 0) for server in all_servers)

        # Get shard data for additional stats
        shards = shard_data_manager.get_all_shards()
        total_channels = sum(shard.get('channel_count', 0) for shard in shards if shard.get('status') == 'operational')

        # Calculate average latency
        operational_shards = [s for s in shards if s.get('status') == 'operational' and s.get('latency', 0) > 0]
        average_latency = sum(s.get('latency', 0) for s in operational_shards) / len(operational_shards) if operational_shards else 0

        # Get the most recent timestamp from shards
        last_updated = None
        if shards:
            timestamps = [s.get('last_seen') or s.get('timestamp') for s in shards if s.get('last_seen') or s.get('timestamp')]
            if timestamps:
                last_updated = max(timestamps)

        if not last_updated:
            last_updated = datetime.now(timezone.utc).isoformat()

        logger.info(f"Homepage stats: {total_servers} servers, {total_members} members from {len(all_servers)} actual servers")

        return jsonify({
            'success': True,
            'total_servers': total_servers,
            'total_members': total_members,
            'total_channels': total_channels,
            'total_shards': len(shards),
            'online_shards': len([s for s in shards if s.get('status') == 'operational']),
            'average_latency': round(average_latency),
            'last_updated': last_updated,
            'cache_age_minutes': _calculate_cache_age(last_updated),
            'source': 'real_servers'  # Indicate this is calculated from actual server data
        })
    except Exception as e:
        logger.error(f"Error getting homepage stats: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'total_servers': 0,
            'total_members': 0,
            'total_channels': 0,
            'total_shards': 0,
            'online_shards': 0,
            'average_latency': 0
        }), 500

@app.route('/api/homepage-servers')
def api_homepage_servers():
    """API endpoint for homepage server showcase (public) - Get servers sorted by size"""
    try:
        # Get all servers from all shards
        all_servers = shard_comm.run_async(shard_comm.get_all_servers_from_all_shards())

        if not all_servers:
            logger.warning("No servers data available for homepage")
            return jsonify({
                'success': False,
                'error': 'No servers data available',
                'servers': []
            }), 404

        # Filter out servers with missing data and sort by member count
        valid_servers = []
        for server in all_servers:
            if server.get('member_count', 0) > 0 and server.get('name'):
                # Get icon URL - shard API provides full URL in 'icon' field
                icon_url = None
                if server.get('icon'):
                    # Shard API already provides the full URL
                    icon_url = server['icon']
                elif server.get('icon_url'):
                    icon_url = server['icon_url']
                elif server.get('icon_hash'):
                    # Fallback: construct URL from hash if needed
                    icon_url = f"https://cdn.discordapp.com/icons/{server['id']}/{server['icon_hash']}.png"

                valid_servers.append({
                    'id': server['id'],
                    'name': server['name'],
                    'member_count': server['member_count'],
                    'icon_url': icon_url,
                    'shard_id': server.get('shard_id')
                })

        # Sort by member count (largest first)
        valid_servers.sort(key=lambda x: x['member_count'], reverse=True)

        logger.info(f"Homepage servers: returning {len(valid_servers)} servers from {len(all_servers)} total")

        # Debug: Log first few servers with their icon URLs
        if valid_servers:
            for i, server in enumerate(valid_servers[:3]):
                logger.info(f"Server {i+1}: {server['name']} - Icon: {server['icon_url']} - Members: {server['member_count']}")

        return jsonify({
            'success': True,
            'servers': valid_servers,
            'total_servers': len(valid_servers)
        })

    except Exception as e:
        logger.error(f"Error getting homepage servers: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'servers': []
        }), 500

def _calculate_cache_age(last_updated_str):
    """Calculate cache age in minutes"""
    try:
        if not last_updated_str:
            return None

        if isinstance(last_updated_str, str):
            last_updated = datetime.fromisoformat(last_updated_str.replace('Z', '+00:00'))
        else:
            last_updated = last_updated_str

        if last_updated.tzinfo is None:
            last_updated = last_updated.replace(tzinfo=timezone.utc)

        age_seconds = (datetime.now(timezone.utc) - last_updated).total_seconds()
        return round(age_seconds / 60, 1)
    except Exception:
        return None

# ========== SHARD API ENDPOINTS ==========

@app.route('/api/shard-heartbeat', methods=['POST'])
@csrf.exempt
def api_shard_heartbeat():
    """Receive heartbeat from shards"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate API key
        api_key = data.get('api_key')
        if not shard_data_manager.validate_api_key(api_key):
            return jsonify({'error': 'Invalid API key'}), 401

        # Update shard status
        success = shard_data_manager.update_shard_status(data)

        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'error': 'Failed to update shard status'}), 500

    except Exception as e:
        logger.error(f"Error processing shard heartbeat: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/shard-event', methods=['POST'])
def api_shard_event():
    """Receive events from shards (guild join/leave, etc.)"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate API key
        api_key = data.get('api_key')
        if not shard_data_manager.validate_api_key(api_key):
            return jsonify({'error': 'Invalid API key'}), 401

        # Log the event (you can extend this to store events in database)
        event_type = data.get('event')
        shard_id = data.get('shard_id')
        logger.info(f"Shard {shard_id} event: {event_type}")

        return jsonify({'success': True})

    except Exception as e:
        logger.error(f"Error processing shard event: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/shards')
def api_shards():
    """Get all shard statuses (public endpoint for status page)"""
    try:
        # Get real-time shard data from shard_data_manager
        shards = shard_data_manager.get_all_shards()

        # Get aggregated statistics from our cache
        cached_stats = get_aggregator().get_cached_statistics()

        # Combine real-time shard status with cached aggregated stats
        total_stats = {
            'total_shards': len(shard_comm.get_active_shards()),
            'total_guilds': cached_stats.get('total_servers', 0),
            'total_users': cached_stats.get('total_members', 0),
            'average_latency': cached_stats.get('average_latency', 0),
            'operational_shards': len([s for s in shards if s.get('status') == 'operational']),
            'last_updated': cached_stats.get('last_updated', datetime.now(timezone.utc).isoformat()),
            'cache_age_minutes': _calculate_cache_age(cached_stats.get('last_updated'))
        }

        return jsonify({
            'success': True,
            'shards': shards,
            'total_stats': total_stats
        })

    except Exception as e:
        logger.error(f"Error getting shard data: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/force-stats-refresh', methods=['POST'])
def api_force_stats_refresh():
    """Force immediate statistics refresh (admin endpoint)"""
    try:
        # Force aggregation
        stats = get_aggregator().force_aggregation()

        if stats:
            return jsonify({
                'success': True,
                'message': 'Statistics refreshed successfully',
                'stats': stats
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to refresh statistics'
            }), 500

    except Exception as e:
        logger.error(f"Error forcing stats refresh: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/shards/<int:shard_id>')
def api_shard_detail(shard_id):
    """Get detailed information for a specific shard"""
    try:
        shard = shard_data_manager.get_shard_status(shard_id)

        if shard:
            return jsonify({
                'success': True,
                'shard': shard
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Shard not found'
            }), 404

    except Exception as e:
        logger.error(f"Error getting shard {shard_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/find-server/<server_id>')
def api_find_server(server_id):
    """Find which shard contains a specific server"""
    try:
        shard_id = shard_data_manager.find_server_shard(server_id)

        if shard_id is not None:
            return jsonify({
                'success': True,
                'shard_id': shard_id,
                'server_id': server_id
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Server not found or shard offline'
            }), 404

    except Exception as e:
        logger.error(f"Error finding server {server_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/notifications')
@login_required
def notifications():
    """Notifications page"""
    return render_template('notifications.html')

@app.route('/giveaways')
@login_required
def giveaways():
    """Giveaways management page"""
    if 'server_id' not in session:
        return redirect(url_for('select_server'))
    return render_template('giveaways.html')

@app.route('/api/user-info')
@login_required
def api_user_info():
    """API endpoint to get current user information"""
    try:
        user_data = {
            'id': session.get('user_id'),
            'username': session.get('username'),
            'discriminator': session.get('discriminator'),
            'avatar': session.get('avatar'),
            'global_name': session.get('global_name')
        }

        return jsonify({
            'success': True,
            'user': user_data
        })
    except Exception as e:
        logger.error(f"Error getting user info: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test-notification', methods=['POST'])
@login_required
def create_test_notification():
    """Create a test notification for debugging"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({"error": "Not logged in"}), 401

        # Create a test notification
        success = db.create_notification(
            user_id=int(user_id),
            title="Test Notification",
            message="This is a test notification to verify the system is working.",
            notification_type="info"
        )

        return jsonify({"success": success, "message": "Test notification created"})
    except Exception as e:
        logger.error(f"Error creating test notification: {e}")
        return jsonify({"error": "Failed to create test notification"}), 500

@app.route('/api/notifications', methods=['GET'])
@login_required
def get_notifications():
    """Get all notifications for the current user"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({"error": "Not logged in"}), 401

        logger.info(f"API: Getting notifications for user_id: {user_id} (type: {type(user_id)})")
        notifications = db.get_user_notifications(int(user_id))
        logger.info(f"API: Retrieved {len(notifications)} notifications")
        logger.debug(f"API: Notifications data: {notifications}")
        return jsonify(notifications)
    except Exception as e:
        logger.error(f"Error getting notifications: {e}")
        return jsonify({"error": "Failed to get notifications"}), 500

@app.route('/api/notifications/count', methods=['GET'])
@login_required
def get_notifications_count():
    """Get unread notification count"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({"error": "Not logged in"}), 401

        count = db.get_unread_notification_count(int(user_id))
        return jsonify({"success": True, "count": count})
    except Exception as e:
        logger.error(f"Error getting notification count: {e}")
        return jsonify({"error": "Failed to get notification count"}), 500

@app.route('/api/notifications/read/<notification_id>', methods=['POST'])
@login_required
def mark_notification_read(notification_id):
    """Mark a notification as read"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({"error": "Not logged in"}), 401

        success = db.mark_notification_read(notification_id, int(user_id))
        return jsonify({"success": success})
    except Exception as e:
        logger.error(f"Error marking notification as read: {e}")
        return jsonify({"error": "Failed to mark notification as read"}), 500

@app.route('/api/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    """Mark all notifications as read"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({"error": "Not logged in"}), 401

        success = db.mark_all_notifications_read(int(user_id))
        return jsonify({"success": success})
    except Exception as e:
        logger.error(f"Error marking all notifications as read: {e}")
        return jsonify({"error": "Failed to mark all notifications as read"}), 500

@app.route('/api/notifications/delete/<notification_id>', methods=['DELETE'])
@login_required
def delete_notification(notification_id):
    """Delete a notification"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({"error": "Not logged in"}), 401

        success = db.dismiss_notification(notification_id, int(user_id))
        return jsonify({"success": success})
    except Exception as e:
        logger.error(f"Error deleting notification: {e}")
        return jsonify({"error": "Failed to delete notification"}), 500

@app.route('/api/notifications/clear', methods=['DELETE'])
@login_required
def clear_notifications():
    """Clear all notifications"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({"error": "Not logged in"}), 401

        success = db.clear_all_notifications(int(user_id))
        return jsonify({"success": success})
    except Exception as e:
        logger.error(f"Error clearing notifications: {e}")
        return jsonify({"error": "Failed to clear notifications"}), 500

@app.route('/api/check-premium-feature')
@login_required
def api_check_premium_feature():
    """API endpoint to check if a feature or sub-feature is premium"""
    try:
        feature_name = request.args.get('feature')
        subfeature_name = request.args.get('subfeature')
        server_id = session.get('server_id')

        if not feature_name:
            return jsonify({'error': 'Feature name is required'}), 400

        if not server_id:
            return jsonify({'error': 'No server selected'}), 400

        # Check if it's a sub-feature or main feature
        if subfeature_name:
            is_premium = is_premium_subfeature(feature_name, subfeature_name)
        else:
            is_premium = is_premium_feature(feature_name)

        # If it's premium, check if user has access
        has_access = True
        if is_premium:
            validation = validate_premium_feature_access(int(server_id), feature_name)
            has_access = validation['has_access']

        return jsonify({
            'success': True,
            'is_premium': is_premium,
            'has_access': has_access,
            'feature': feature_name,
            'subfeature': subfeature_name
        })

    except Exception as e:
        logger.error(f"Error checking premium feature: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ========== PREMIUM ENFORCEMENT BACKGROUND TASK ==========

def premium_enforcement_worker():
    """Background worker that checks and enforces premium features every 30 seconds"""
    logger.info("Premium enforcement worker started")

    while True:
        try:
            # Wait 30 seconds between checks
            time.sleep(30)

            logger.debug("Running premium enforcement check...")

            # Get all premium features from database
            premium_features = {}
            features = db.get_features()

            for feature in features:
                if feature.get('is_premium', False):
                    premium_features[feature['name']] = {
                        'is_premium': True,
                        'premium_subfeatures': feature.get('premium_subfeatures', [])
                    }

            if not premium_features:
                logger.debug("No premium features configured, skipping enforcement")
                continue

            # Get all servers from database
            all_servers = db.get_all_servers()
            disabled_count = 0

            logger.debug(f"Checking {len(all_servers)} servers for premium enforcement")

            for server in all_servers:
                server_id = server.get('server_id')
                if not server_id:
                    continue

                try:
                    # Get server owner from Discord API or database
                    owner_id = server.get('owner_id')

                    if not owner_id:
                        # Try to get owner from server info
                        server_info = get_server_info(server_id)
                        if server_info:
                            owner_id = server_info.get('owner_id')

                    if owner_id:
                        # Check if owner has premium subscription
                        has_premium = db.is_server_premium_for_user(server_id, owner_id)

                        if not has_premium:
                            # Disable all premium features for this server
                            for feature_name in premium_features.keys():
                                disabled = disable_feature_for_server(server_id, feature_name)
                                if disabled:
                                    disabled_count += 1
                                    logger.info(f"Auto-disabled premium feature '{feature_name}' for server {server_id} (owner {owner_id} has no premium)")
                    else:
                        # If no owner info, disable premium features as safety measure
                        for feature_name in premium_features.keys():
                            disabled = disable_feature_for_server(server_id, feature_name)
                            if disabled:
                                disabled_count += 1
                                logger.info(f"Auto-disabled premium feature '{feature_name}' for server {server_id} (no owner info)")

                except Exception as e:
                    logger.error(f"Error checking premium status for server {server_id}: {e}")
                    continue

            if disabled_count > 0:
                logger.info(f"Premium enforcement completed: disabled {disabled_count} premium features across {len(all_servers)} servers")
            else:
                logger.debug(f"Premium enforcement completed: no changes needed (checked {len(all_servers)} servers)")

        except Exception as e:
            logger.error(f"Error in premium enforcement worker: {e}")
            # Continue running even if there's an error
            continue

def disable_feature_for_server(server_id: int, feature_name: str) -> bool:
    """Disable a specific feature for a server by removing/resetting its configuration"""
    try:
        disabled = False

        if feature_name == 'repping':
            # Remove repping configuration completely
            if db.remove_repping_config(server_id):
                disabled = True
            # Also disable in server config
            if db.update_server_config_field(server_id, 'repping_enabled', False):
                disabled = True

        elif feature_name == 'tempvoice':
            # Remove tempvoice configuration completely
            if db.remove_tempvoice_config(server_id):
                disabled = True

        elif feature_name == 'music':
            # Remove music configuration completely
            if db.remove_music_config(server_id):
                disabled = True

        elif feature_name == 'dm_support':
            # Remove DM support configuration completely
            if db.remove_dm_support_config(server_id):
                disabled = True

        elif feature_name == 'sticky_messages':
            # Remove sticky messages configuration completely
            if db.remove_sticky_messages_config(server_id):
                disabled = True

        elif feature_name == 'auto_roling':
            # Remove auto-roling configuration completely
            if db.remove_auto_roling_config(server_id):
                disabled = True
            # Also disable in server config
            if db.update_server_config_field(server_id, 'auto_roling_enabled', False):
                disabled = True

        elif feature_name == 'vent':
            # Remove vent configuration completely
            if db.remove_vent_config(server_id):
                disabled = True
            # Also disable in server config
            if db.update_server_config_field(server_id, 'vent_enabled', False):
                disabled = True

        elif feature_name == 'statistics':
            # Statistics access is handled by route-level enforcement
            return True

        elif feature_name == 'on-site-logs':
            # Logs access is handled by route-level enforcement
            return True

        return disabled

    except Exception as e:
        logger.error(f"Error disabling feature {feature_name} for server {server_id}: {e}")
        return False

def start_premium_enforcement():
    """Start the premium enforcement background worker"""
    worker_thread = threading.Thread(target=premium_enforcement_worker, daemon=True)
    worker_thread.start()
    logger.info("Premium enforcement background worker started")

# Start the premium enforcement worker when the module loads
start_premium_enforcement()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)

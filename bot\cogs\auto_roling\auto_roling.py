"""
Auto-Roling System Cog for Discord Bot
Handles automatic role assignment to new members and offline protection
"""

import discord
from discord.ext import commands, tasks
import asyncio
import logging
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class AutoRolingSystem(commands.Cog):
    """Cog for handling the auto-roling system functionality"""
    
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
        
    async def cog_load(self):
        """Called when the cog is loaded"""
        logger.info("Loading Auto-Roling System cog...")
        
        # Start the auto-roling checking task
        if not self.check_auto_roling.is_running():
            self.check_auto_roling.start()
            
        logger.info("Auto-Roling System cog loaded successfully")
        
    async def cog_unload(self):
        """Called when the cog is unloaded"""
        logger.info("Unloading Auto-Roling System cog...")
        
        # Stop the auto-roling checking task
        if self.check_auto_roling.is_running():
            self.check_auto_roling.cancel()
            
        logger.info("Auto-Roling System cog unloaded")

    @commands.Cog.listener()
    async def on_member_join(self, member):
        """Handle member joins for auto-roling"""
        try:
            # Handle auto-roling (include bots but respect ignored users)
            guild_id = member.guild.id

            # Check if auto-roling is explicitly disabled (for premium enforcement)
            config = self.db.get_server_config(guild_id)
            if config and config.get('auto_roling_enabled') is False:
                logger.debug(f"Auto-roling disabled for server {guild_id}")
                return

            # For free users, auto-roling is allowed (basic tier)
            # Premium enforcement is handled by the website removing configurations

            # Get ignored users list
            config = self.db.get_server_config(guild_id)
            ignored_users = config.get('ignored_users', []) if config else []

            # Check if auto-roling is explicitly disabled (for premium enforcement)
            if config and config.get('auto_roling_enabled') is False:
                return

            logger.info(f"Processing auto-roling for {member.display_name} ({member.id}) in guild {guild_id}")

            if member.id not in ignored_users:
                # Get auto-roling settings
                auto_roling_settings = self.db.get_auto_roling_settings(guild_id)
                logger.info(f"Auto-roling settings for guild {guild_id}: {auto_roling_settings}")

                if auto_roling_settings and auto_roling_settings.get('enabled') and not auto_roling_settings.get('permission_error'):
                    role_id = auto_roling_settings.get('role_id')
                    logger.info(f"Auto-roling role_id for guild {guild_id}: {role_id}")

                    if role_id:
                        # Get the role
                        role = member.guild.get_role(role_id)
                        if role:
                            logger.info(f"Found auto-role {role.name} ({role_id}), assigning to {member.display_name}")
                            # Assign the role
                            await self.assign_auto_role(member, role, guild_id)
                        else:
                            logger.warning(f"Auto-roling role {role_id} not found in guild {guild_id}")
                    else:
                        logger.warning(f"No role_id configured for auto-roling in guild {guild_id}")
                else:
                    if not auto_roling_settings:
                        logger.info(f"No auto-roling settings found for guild {guild_id}")
                    elif not auto_roling_settings.get('enabled'):
                        logger.info(f"Auto-roling disabled for guild {guild_id}")
                    elif auto_roling_settings.get('permission_error'):
                        logger.warning(f"Auto-roling has permission error for guild {guild_id}")
            else:
                logger.debug(f"Skipping auto-roling for ignored user {member.display_name} ({member.id})")

        except Exception as e:
            logger.error(f"Error in auto-roling on_member_join handler: {e}")

    async def assign_auto_role(self, member, role, guild_id):
        """Assign auto-role to a member with permission checks"""
        try:
            # Check if user already has the role
            if role in member.roles:
                logger.debug(f"User {member.display_name} already has auto-role {role.name}")
                return

            # Check if we can actually assign the role
            bot_member = member.guild.me
            if not bot_member.guild_permissions.manage_roles:
                error_msg = "Missing 'Manage Roles' permission"
                logger.warning(f"Cannot assign auto-role in {member.guild.name} - {error_msg}")
                self.db.set_auto_roling_error(guild_id, error_msg)
                return

            if bot_member.top_role <= role and bot_member != member.guild.owner:
                error_msg = f"Auto-role {role.name} is above bot's highest role"
                logger.warning(f"Cannot assign auto-role {role.name} in {member.guild.name} - {error_msg}")
                self.db.set_auto_roling_error(guild_id, error_msg)
                return

            if member.top_role >= bot_member.top_role and member != member.guild.owner:
                logger.debug(f"Skipping auto-role for {member.display_name} - their highest role is above bot's")
                return

            # Add the role
            await member.add_roles(role, reason="Auto-roling system")

            logger.info(f"Assigned auto-role {role.name} to {member.display_name} in guild {member.guild.name}")

            # Log to database for dashboard
            try:
                self.db.log_bot_activity(
                    guild_id,
                    member.id,
                    f"{member.name}#{member.discriminator}",
                    f"Auto-role assigned: {role.name}",
                    f"New member automatically received role",
                    "auto_roling",
                    None
                )

                # Also log to Discord if enabled
                logging_cog = self.bot.get_cog('LoggingSystem')
                if logging_cog:
                    await logging_cog.log_bot_activity_to_channel(
                        guild_id, "ryzuo_logs", member.id, f"{member.name}#{member.discriminator}",
                        f"Auto-role assigned: {role.name}",
                        f"New member automatically received role",
                        None
                    )
            except Exception as e:
                logger.error(f"Failed to log auto-role assignment: {e}")

        except discord.HTTPException as e:
            if e.status == 429:  # Rate limited
                logger.warning(f"Rate limited during auto-role assignment, will retry later...")
            else:
                error_msg = f"HTTP error during role assignment: {e}"
                logger.error(error_msg)
                self.db.set_auto_roling_error(guild_id, error_msg)
        except discord.Forbidden:
            error_msg = "Forbidden: Bot lacks permission to assign roles"
            logger.error(f"Cannot assign auto-role - {error_msg}")
            self.db.set_auto_roling_error(guild_id, error_msg)
        except Exception as e:
            error_msg = f"Unexpected error during role assignment: {e}"
            logger.error(error_msg)
            self.db.set_auto_roling_error(guild_id, error_msg)

    @tasks.loop(seconds=10)  # Check every 10 seconds for auto-roling offline protection
    async def check_auto_roling(self):
        """Check for members missing auto-roles (offline protection)"""
        if not self.bot.is_ready():
            return

        try:
            for guild in self.bot.guilds:
                # Check if auto-roling is explicitly disabled (for premium enforcement)
                config = self.db.get_server_config(guild.id)
                if config and config.get('auto_roling_enabled') is False:
                    continue

                # Get auto-roling settings
                auto_roling_settings = self.db.get_auto_roling_settings(guild.id)
                if not auto_roling_settings or not auto_roling_settings.get('enabled'):
                    continue

                # Skip if there's a permission error
                if auto_roling_settings.get('permission_error'):
                    continue

                role_id = auto_roling_settings.get('role_id')
                if not role_id:
                    continue

                # Get the role
                role = guild.get_role(role_id)
                if not role:
                    logger.warning(f"Auto-roling role {role_id} not found in guild {guild.id}")
                    continue

                # Get ignored users list
                config = self.db.get_server_config(guild.id)
                ignored_users = config.get('ignored_users', []) if config else []

                # Check all members (including bots, but respect ignored list)
                members_to_assign = []
                for member in guild.members:
                    # Skip ignored users
                    if member.id in ignored_users:
                        continue

                    # Check if member should have the role but doesn't
                    if role not in member.roles:
                        members_to_assign.append(member)

                # Assign roles with rate limiting
                for i, member in enumerate(members_to_assign[:5]):  # Limit to 5 per check to avoid rate limits
                    try:
                        await self.assign_auto_role(member, role, guild.id)
                        # Small delay between assignments
                        if i < len(members_to_assign) - 1:
                            await asyncio.sleep(0.1)
                    except Exception as e:
                        logger.error(f"Error assigning auto-role to {member.display_name}: {e}")

        except Exception as e:
            logger.error(f"Error in check_auto_roling task: {e}")

    @commands.command(name='test_autorole')
    @commands.has_permissions(administrator=True)
    async def test_autorole(self, ctx, member: discord.Member = None):
        """Test auto-role assignment for a specific member or yourself"""
        try:
            target_member = member or ctx.author
            guild_id = ctx.guild.id

            await ctx.send(f"Testing auto-role for {target_member.display_name}...")

            # Get auto-roling settings
            auto_roling_settings = self.db.get_auto_roling_settings(guild_id)

            if not auto_roling_settings:
                await ctx.send("❌ No auto-roling settings found for this server.")
                return

            if not auto_roling_settings.get('enabled'):
                await ctx.send("❌ Auto-roling is disabled for this server.")
                return

            if auto_roling_settings.get('permission_error'):
                await ctx.send(f"❌ Auto-roling has permission error: {auto_roling_settings.get('last_error')}")
                return

            role_id = auto_roling_settings.get('role_id')
            if not role_id:
                await ctx.send("❌ No role configured for auto-roling.")
                return

            role = ctx.guild.get_role(role_id)
            if not role:
                await ctx.send(f"❌ Auto-role {role_id} not found in this server.")
                return

            if role in target_member.roles:
                await ctx.send(f"✅ {target_member.display_name} already has the auto-role {role.name}")
                return

            # Assign the role
            await self.assign_auto_role(target_member, role, guild_id)
            await ctx.send(f"✅ Successfully assigned auto-role {role.name} to {target_member.display_name}")

        except Exception as e:
            logger.error(f"Error in test_autorole command: {e}")
            await ctx.send(f"❌ Error testing auto-role: {e}")

    @commands.command(name='autorole_status')
    @commands.has_permissions(administrator=True)
    async def autorole_status(self, ctx):
        """Check auto-role configuration status"""
        try:
            guild_id = ctx.guild.id

            # Get auto-roling settings
            auto_roling_settings = self.db.get_auto_roling_settings(guild_id)

            embed = discord.Embed(title="Auto-Role Status", color=discord.Color.blue())

            if not auto_roling_settings:
                embed.add_field(name="Status", value="❌ Not configured", inline=False)
                embed.add_field(name="Solution", value="Configure auto-roling in the dashboard", inline=False)
            else:
                enabled = auto_roling_settings.get('enabled', False)
                role_id = auto_roling_settings.get('role_id')
                permission_error = auto_roling_settings.get('permission_error', False)
                last_error = auto_roling_settings.get('last_error')

                embed.add_field(name="Enabled", value="✅ Yes" if enabled else "❌ No", inline=True)
                embed.add_field(name="Permission Error", value="❌ Yes" if permission_error else "✅ No", inline=True)

                if role_id:
                    role = ctx.guild.get_role(role_id)
                    if role:
                        embed.add_field(name="Auto-Role", value=f"✅ {role.name} ({role.id})", inline=False)
                    else:
                        embed.add_field(name="Auto-Role", value=f"❌ Role {role_id} not found", inline=False)
                else:
                    embed.add_field(name="Auto-Role", value="❌ No role configured", inline=False)

                if last_error:
                    embed.add_field(name="Last Error", value=last_error, inline=False)

            await ctx.send(embed=embed)

        except Exception as e:
            logger.error(f"Error in autorole_status command: {e}")
            await ctx.send(f"❌ Error checking auto-role status: {e}")


async def setup(bot):
    """Setup function for the cog"""
    await bot.add_cog(AutoRolingSystem(bot))

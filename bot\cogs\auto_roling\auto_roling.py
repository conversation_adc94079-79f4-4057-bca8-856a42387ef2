"""
Auto-Roling System Cog for Discord Bot
Handles automatic role assignment to new members and offline protection
"""

import discord
from discord.ext import commands, tasks
import asyncio
import logging
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class AutoRolingSystem(commands.Cog):
    """Cog for handling the auto-roling system functionality"""
    
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
        
    async def cog_load(self):
        """Called when the cog is loaded"""
        logger.info("Loading Auto-Roling System cog...")
        
        # Start the auto-roling checking task
        if not self.check_auto_roling.is_running():
            self.check_auto_roling.start()
            
        logger.info("Auto-Roling System cog loaded successfully")
        
    async def cog_unload(self):
        """Called when the cog is unloaded"""
        logger.info("Unloading Auto-Roling System cog...")
        
        # Stop the auto-roling checking task
        if self.check_auto_roling.is_running():
            self.check_auto_roling.cancel()
            
        logger.info("Auto-Roling System cog unloaded")

    @commands.Cog.listener()
    async def on_member_join(self, member):
        """Handle member joins for auto-roling"""
        try:
            guild_id = member.guild.id
            logger.info(f"Member {member.display_name} ({member.id}) joined guild {guild_id}")

            # Get server configuration
            logger.info(f"Getting server configuration for guild {guild_id}")
            config = self.db.get_server_config(guild_id)
            logger.info(f"Server config: {config}")

            # Check if auto-roling is explicitly disabled (for premium enforcement)
            if config and config.get('auto_roling_enabled') is False:
                logger.info(f"Auto-roling explicitly disabled for server {guild_id}")
                return

            # Get ignored users list
            ignored_users = config.get('ignored_users', []) if config else []
            logger.info(f"Ignored users list: {ignored_users}")

            if member.id in ignored_users:
                logger.info(f"Skipping auto-roling for ignored user {member.display_name} ({member.id})")
                return

            # Get auto-roling settings
            logger.info(f"Getting auto-roling settings for guild {guild_id}")
            auto_roling_settings = self.db.get_auto_roling_settings(guild_id)
            logger.info(f"Auto-roling settings for guild {guild_id}: {auto_roling_settings}")

            if not auto_roling_settings:
                logger.info(f"No auto-roling settings found for guild {guild_id}")
                return

            if not auto_roling_settings.get('enabled', False):
                logger.info(f"Auto-roling disabled for guild {guild_id}")
                return

            if auto_roling_settings.get('permission_error', False):
                logger.warning(f"Auto-roling has permission error for guild {guild_id}")
                return

            # Assign basic auto-role (free feature)
            await self.assign_basic_auto_role(member, auto_roling_settings, guild_id)

            # Assign additional roles if premium (premium feature)
            await self.assign_additional_roles(member, auto_roling_settings, guild_id)

        except Exception as e:
            logger.error(f"Error in auto-roling on_member_join handler: {e}", exc_info=True)

    async def assign_basic_auto_role(self, member, auto_roling_settings, guild_id):
        """Assign the basic auto-role (free feature)"""
        try:
            logger.info(f"Starting basic auto-role assignment for {member.display_name} in guild {guild_id}")

            role_id = auto_roling_settings.get('role_id')
            logger.info(f"Basic role_id from settings: {role_id}")

            if not role_id:
                logger.warning(f"No basic role_id configured for guild {guild_id}")
                return

            role = member.guild.get_role(role_id)
            logger.info(f"Found role object: {role}")

            if not role:
                logger.error(f"Basic auto-role {role_id} not found in guild {guild_id}")
                # Mark as permission error
                self.db.set_auto_roling_permission_error(guild_id, f"Role {role_id} not found")
                return

            logger.info(f"Assigning basic auto-role {role.name} ({role_id}) to {member.display_name}")
            await self.assign_auto_role(member, role, guild_id, "basic")

        except Exception as e:
            logger.error(f"Error assigning basic auto-role: {e}", exc_info=True)

    async def assign_additional_roles(self, member, auto_roling_settings, guild_id):
        """Assign additional roles (premium feature)"""
        try:
            additional_roles = auto_roling_settings.get('additional_join_roles', [])
            if not additional_roles:
                logger.debug(f"No additional roles configured for guild {guild_id}")
                return

            # Check if server has premium access
            # This is a simplified check - in production you might want more sophisticated validation
            owner_id = member.guild.owner_id
            if not self.db.is_user_subscribed(owner_id):
                logger.debug(f"Server {guild_id} owner {owner_id} doesn't have premium, skipping additional roles")
                return

            logger.info(f"Assigning {len(additional_roles)} additional roles to {member.display_name}")

            for role_id in additional_roles:
                if not role_id:
                    continue

                role = member.guild.get_role(role_id)
                if role:
                    await self.assign_auto_role(member, role, guild_id, "additional")
                else:
                    logger.warning(f"Additional auto-role {role_id} not found in guild {guild_id}")

        except Exception as e:
            logger.error(f"Error assigning additional roles: {e}", exc_info=True)

    async def assign_auto_role(self, member, role, guild_id, role_type="basic"):
        """Assign auto-role to a member with permission checks"""
        try:
            logger.info(f"assign_auto_role called: member={member.display_name}, role={role.name}, guild={guild_id}, type={role_type}")

            # Check if user already has the role
            if role in member.roles:
                logger.info(f"User {member.display_name} already has auto-role {role.name}")
                return

            # Check if we can actually assign the role
            bot_member = member.guild.me
            if not bot_member.guild_permissions.manage_roles:
                error_msg = "Missing 'Manage Roles' permission"
                logger.warning(f"Cannot assign auto-role in {member.guild.name} - {error_msg}")
                self.db.set_auto_roling_error(guild_id, error_msg)
                return

            if bot_member.top_role <= role and bot_member != member.guild.owner:
                error_msg = f"Auto-role {role.name} is above bot's highest role"
                logger.warning(f"Cannot assign auto-role {role.name} in {member.guild.name} - {error_msg}")
                self.db.set_auto_roling_error(guild_id, error_msg)
                return

            if member.top_role >= bot_member.top_role and member != member.guild.owner:
                logger.debug(f"Skipping auto-role for {member.display_name} - their highest role is above bot's")
                return

            # Add the role
            await member.add_roles(role, reason=f"Auto-roling system ({role_type} role)")

            logger.info(f"Assigned {role_type} auto-role {role.name} to {member.display_name} in guild {member.guild.name}")

            # Log to database for dashboard
            try:
                self.db.log_bot_activity(
                    guild_id,
                    member.id,
                    f"{member.name}#{member.discriminator}",
                    f"Auto-role assigned: {role.name} ({role_type})",
                    f"New member automatically received {role_type} role",
                    "auto_roling",
                    None
                )

                # Also log to Discord if enabled
                logging_cog = self.bot.get_cog('LoggingSystem')
                if logging_cog:
                    await logging_cog.log_bot_activity_to_channel(
                        guild_id, "ryzuo_logs", member.id, f"{member.name}#{member.discriminator}",
                        f"Auto-role assigned: {role.name}",
                        f"New member automatically received role",
                        None
                    )
            except Exception as e:
                logger.error(f"Failed to log auto-role assignment: {e}")

        except discord.HTTPException as e:
            if e.status == 429:  # Rate limited
                logger.warning(f"Rate limited during auto-role assignment, will retry later...")
            else:
                error_msg = f"HTTP error during role assignment: {e}"
                logger.error(error_msg)
                self.db.set_auto_roling_error(guild_id, error_msg)
        except discord.Forbidden:
            error_msg = "Forbidden: Bot lacks permission to assign roles"
            logger.error(f"Cannot assign auto-role - {error_msg}")
            self.db.set_auto_roling_error(guild_id, error_msg)
        except Exception as e:
            error_msg = f"Unexpected error during role assignment: {e}"
            logger.error(error_msg)
            self.db.set_auto_roling_error(guild_id, error_msg)

    @tasks.loop(minutes=5)  # Check every 5 minutes for offline protection (premium feature)
    async def check_auto_roling(self):
        """Check for members missing auto-roles (offline protection - premium feature)"""
        if not self.bot.is_ready():
            return

        try:
            for guild in self.bot.guilds:
                # Check if auto-roling is explicitly disabled (for premium enforcement)
                config = self.db.get_server_config(guild.id)
                if config and config.get('auto_roling_enabled') is False:
                    continue

                # Get auto-roling settings
                auto_roling_settings = self.db.get_auto_roling_settings(guild.id)
                if not auto_roling_settings or not auto_roling_settings.get('enabled'):
                    continue

                # Skip if there's a permission error
                if auto_roling_settings.get('permission_error'):
                    continue

                # Check if offline protection is enabled (premium feature)
                offline_protection = auto_roling_settings.get('offline_protection_enabled', False)
                if not offline_protection:
                    continue

                # Check if server has premium access
                owner_id = guild.owner_id
                if not self.db.is_user_subscribed(owner_id):
                    logger.debug(f"Server {guild.id} owner {owner_id} doesn't have premium, skipping offline protection")
                    continue

                logger.debug(f"Running offline protection for guild {guild.name} ({guild.id})")

                # Get all roles to assign (basic + additional)
                roles_to_assign = []

                # Basic role
                basic_role_id = auto_roling_settings.get('role_id')
                if basic_role_id:
                    basic_role = guild.get_role(basic_role_id)
                    if basic_role:
                        roles_to_assign.append(('basic', basic_role))

                # Additional roles (premium)
                additional_roles = auto_roling_settings.get('additional_join_roles', [])
                for role_id in additional_roles:
                    if role_id:
                        role = guild.get_role(role_id)
                        if role:
                            roles_to_assign.append(('additional', role))

                if not roles_to_assign:
                    continue

                # Get ignored users list
                ignored_users = config.get('ignored_users', []) if config else []

                # Check all members for missing roles
                members_to_process = []
                for member in guild.members:
                    # Skip ignored users
                    if member.id in ignored_users:
                        continue

                    # Check if member is missing any auto-roles
                    missing_roles = []
                    for role_type, role in roles_to_assign:
                        if role not in member.roles:
                            missing_roles.append((role_type, role))

                    if missing_roles:
                        members_to_process.append((member, missing_roles))

                # Process members with rate limiting
                processed_count = 0
                for member, missing_roles in members_to_process[:10]:  # Limit to 10 members per check
                    try:
                        for role_type, role in missing_roles:
                            await self.assign_auto_role(member, role, guild.id, f"offline-{role_type}")
                            await asyncio.sleep(0.2)  # Small delay between role assignments

                        processed_count += 1
                        if processed_count >= 10:  # Safety limit
                            break

                    except Exception as e:
                        logger.error(f"Error in offline protection for {member.display_name}: {e}")

                if processed_count > 0:
                    logger.info(f"Offline protection: processed {processed_count} members in guild {guild.name}")

        except Exception as e:
            logger.error(f"Error in check_auto_roling task: {e}", exc_info=True)

    @commands.command(name='test_autorole')
    @commands.has_permissions(administrator=True)
    async def test_autorole(self, ctx, member: discord.Member = None):
        """Test auto-role assignment for a specific member or yourself"""
        try:
            target_member = member or ctx.author
            guild_id = ctx.guild.id

            await ctx.send(f"Testing auto-role for {target_member.display_name}...")

            # Get auto-roling settings
            auto_roling_settings = self.db.get_auto_roling_settings(guild_id)

            if not auto_roling_settings:
                await ctx.send("❌ No auto-roling settings found for this server.")
                return

            if not auto_roling_settings.get('enabled'):
                await ctx.send("❌ Auto-roling is disabled for this server.")
                return

            if auto_roling_settings.get('permission_error'):
                await ctx.send(f"❌ Auto-roling has permission error: {auto_roling_settings.get('last_error')}")
                return

            role_id = auto_roling_settings.get('role_id')
            if not role_id:
                await ctx.send("❌ No role configured for auto-roling.")
                return

            role = ctx.guild.get_role(role_id)
            if not role:
                await ctx.send(f"❌ Auto-role {role_id} not found in this server.")
                return

            if role in target_member.roles:
                await ctx.send(f"✅ {target_member.display_name} already has the auto-role {role.name}")
                return

            # Assign the role
            await self.assign_auto_role(target_member, role, guild_id)
            await ctx.send(f"✅ Successfully assigned auto-role {role.name} to {target_member.display_name}")

        except Exception as e:
            logger.error(f"Error in test_autorole command: {e}")
            await ctx.send(f"❌ Error testing auto-role: {e}")

    @commands.command(name='autorole_status')
    @commands.has_permissions(administrator=True)
    async def autorole_status(self, ctx):
        """Check auto-role configuration status"""
        try:
            guild_id = ctx.guild.id

            # Get auto-roling settings
            auto_roling_settings = self.db.get_auto_roling_settings(guild_id)

            embed = discord.Embed(title="Auto-Role Status", color=discord.Color.blue())

            if not auto_roling_settings:
                embed.add_field(name="Status", value="❌ Not configured", inline=False)
                embed.add_field(name="Solution", value="Configure auto-roling in the dashboard", inline=False)
            else:
                enabled = auto_roling_settings.get('enabled', False)
                role_id = auto_roling_settings.get('role_id')
                permission_error = auto_roling_settings.get('permission_error', False)
                last_error = auto_roling_settings.get('last_error')

                embed.add_field(name="Enabled", value="✅ Yes" if enabled else "❌ No", inline=True)
                embed.add_field(name="Permission Error", value="❌ Yes" if permission_error else "✅ No", inline=True)

                if role_id:
                    role = ctx.guild.get_role(role_id)
                    if role:
                        embed.add_field(name="Auto-Role", value=f"✅ {role.name} ({role.id})", inline=False)
                    else:
                        embed.add_field(name="Auto-Role", value=f"❌ Role {role_id} not found", inline=False)
                else:
                    embed.add_field(name="Auto-Role", value="❌ No role configured", inline=False)

                if last_error:
                    embed.add_field(name="Last Error", value=last_error, inline=False)

            await ctx.send(embed=embed)

        except Exception as e:
            logger.error(f"Error in autorole_status command: {e}")
            await ctx.send(f"❌ Error checking auto-role status: {e}")


async def setup(bot):
    """Setup function for the cog"""
    await bot.add_cog(AutoRolingSystem(bot))

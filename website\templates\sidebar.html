<div class="sidebar p-4">
    <div class="sidebar-header mb-4">
        <h6 class="text-uppercase mb-0" style="color: var(--text-secondary); font-weight: 700; font-size: 0.75rem; letter-spacing: 0.1em;">
            <i class="fas fa-cogs me-2" style="color: var(--primary-color);"></i>Configuration
        </h6>
    </div>

    <nav class="nav flex-column gap-1">
        <a class="nav-link {{ 'active' if request.endpoint == 'dashboard' }}" href="{{ url_for('dashboard') }}">
            <i class="fas fa-tachometer-alt text-primary"></i>
            <span>Overview</span>
            {% if request.endpoint == 'dashboard' %}
            <div class="nav-indicator"></div>
            {% endif %}
        </a>

        <div class="nav-section mt-3 mb-2">
            <small class="text-muted text-uppercase" style="font-weight: 600; font-size: 0.7rem; letter-spacing: 0.05em;">Features</small>
        </div>

        <a class="nav-link {{ 'active' if request.endpoint == 'configure_repping' }}" href="{{ url_for('configure_repping') }}">
            <i class="fas fa-star text-warning"></i>
            <span>Repping System</span>
            {% if request.endpoint == 'configure_repping' %}
            <div class="nav-indicator"></div>
            {% endif %}
        </a>
        <a class="nav-link {{ 'active' if request.endpoint == 'configure_auto_roling' }}" href="{{ url_for('configure_auto_roling') }}">
            <i class="fas fa-user-plus text-success"></i>
            <span>Auto-Roling</span>
            {% if request.endpoint == 'configure_auto_roling' %}
            <div class="nav-indicator"></div>
            {% endif %}
        </a>
        <a class="nav-link {{ 'active' if request.endpoint == 'configure_vent' }}" href="{{ url_for('configure_vent') }}">
            <i class="fas fa-heart text-danger"></i>
            <span>Vent System</span>
            {% if request.endpoint == 'configure_vent' %}
            <div class="nav-indicator"></div>
            {% endif %}
        </a>
        <a class="nav-link {{ 'active' if request.endpoint == 'configure_tempvoice' }}" href="{{ url_for('configure_tempvoice') }}">
            <i class="fas fa-microphone text-info"></i>
            <span>Temp Voice</span>
            {% if request.endpoint == 'configure_tempvoice' %}
            <div class="nav-indicator"></div>
            {% endif %}
        </a>
        <a class="nav-link {{ 'active' if request.endpoint == 'configure_music' }}" href="{{ url_for('configure_music') }}">
            <i class="fas fa-music text-purple"></i>
            <span>Music System</span>
            {% if request.endpoint == 'configure_music' %}
            <div class="nav-indicator"></div>
            {% endif %}
        </a>
        <a class="nav-link {{ 'active' if request.endpoint == 'configure_sticky_messages' }}" href="{{ url_for('configure_sticky_messages') }}">
            <i class="fas fa-thumbtack text-success"></i>
            <span>Sticky Messages</span>
            {% if request.endpoint == 'configure_sticky_messages' %}
            <div class="nav-indicator"></div>
            {% endif %}
        </a>
        <a class="nav-link {{ 'active' if request.endpoint == 'giveaways' }}" href="{{ url_for('giveaways') }}">
            <i class="fas fa-gift" style="color: var(--accent-color);"></i>
            <span>Giveaways</span>
            {% if request.endpoint == 'giveaways' %}
            <div class="nav-indicator"></div>
            {% endif %}
        </a>
        <a class="nav-link {{ 'active' if request.endpoint == 'configure_dm_support' }}" href="{{ url_for('configure_dm_support') }}">
            <i class="fas fa-ticket-alt text-primary"></i>
            <span>DM Support</span>
            {% if request.endpoint == 'configure_dm_support' %}
            <div class="nav-indicator"></div>
            {% endif %}
        </a>
        <a class="nav-link {{ 'active' if request.endpoint == 'configure_gender_verification' }}" href="{{ url_for('configure_gender_verification') }}">
            <i class="fas fa-shield-alt text-secondary"></i>
            <span>Gender Verification</span>
            {% if request.endpoint == 'configure_gender_verification' %}
            <div class="nav-indicator"></div>
            {% endif %}
        </a>

        <div class="nav-section mt-3 mb-2">
            <small class="text-muted text-uppercase" style="font-weight: 600; font-size: 0.7rem; letter-spacing: 0.05em;">Management</small>
        </div>

        <a class="nav-link {{ 'active' if request.endpoint == 'configure_logs' }}" href="{{ url_for('configure_logs') }}">
            <i class="fas fa-clipboard-list text-info"></i>
            <span>Logs Configuration</span>
            {% if request.endpoint == 'configure_logs' %}
            <div class="nav-indicator"></div>
            {% endif %}
        </a>
        <a class="nav-link {{ 'active' if request.endpoint == 'settings' }}" href="{{ url_for('settings') }}">
            <i class="fas fa-cog text-secondary"></i>
            <span>Settings</span>
            {% if request.endpoint == 'settings' %}
            <div class="nav-indicator"></div>
            {% endif %}
        </a>
        <a class="nav-link {{ 'active' if request.endpoint == 'site_logs' }}" href="{{ url_for('site_logs') }}">
            <i class="fas fa-file-alt text-warning"></i>
            <span>On-Site Logs</span>
            {% if request.endpoint == 'site_logs' %}
            <div class="nav-indicator"></div>
            {% endif %}
        </a>
        <a class="nav-link {{ 'active' if request.endpoint == 'stats' }}" href="{{ url_for('stats') }}">
            <i class="fas fa-chart-bar" style="color: var(--accent-color);"></i>
            <span>Stats</span>
            {% if request.endpoint == 'stats' %}
            <div class="nav-indicator"></div>
            {% endif %}
        </a>
    </nav>
</div>

<script>
// Premium feature validation for sidebar links
document.addEventListener('DOMContentLoaded', function() {
    initializePremiumSidebar();
});

async function initializePremiumSidebar() {
    // Map sidebar links to their features
    const featureMap = {
        'configure_repping': { feature: 'repping' },
        'configure_dm_support': { feature: 'dm_support' },
        'configure_tempvoice': { feature: 'tempvoice' },
        'configure_music': { feature: 'music' },
        'stats': { feature: 'statistics' },
        'site_logs': { feature: 'on-site-logs' }
    };

    // Check each feature and update sidebar accordingly
    for (const [endpoint, featureInfo] of Object.entries(featureMap)) {
        try {
            const response = await fetch(`/api/check-premium-feature?feature=${featureInfo.feature}`);
            const data = await response.json();

            if (data.success && data.is_premium && !data.has_access) {
                // Find the link and update it
                const link = document.querySelector(`a[href*="${endpoint}"]`);
                if (link) {
                    // Add premium indicator
                    const span = link.querySelector('span');
                    if (span && !span.querySelector('.premium-indicator')) {
                        const premiumBadge = document.createElement('span');
                        premiumBadge.className = 'premium-indicator';
                        premiumBadge.innerHTML = ' <i class="fas fa-crown" style="color: gold; font-size: 0.8em;"></i>';
                        span.appendChild(premiumBadge);
                    }

                    // Update click handler to redirect to premium page
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        window.location.href = '/premium-required?feature=' + featureInfo.feature;
                    });

                    // Add visual styling
                    link.style.opacity = '0.7';
                    link.title = 'Premium feature - Click to upgrade';
                }
            }
        } catch (error) {
            console.error(`Error checking premium status for ${endpoint}:`, error);
        }
    }
}
</script>

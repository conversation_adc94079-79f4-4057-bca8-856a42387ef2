"""
Sticky Messages System Cog for Discord Bot
Handles automatic reposting of sticky messages when new messages are sent
"""

import discord
from discord.ext import commands
import asyncio
import logging
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class StickyMessagesSystem(commands.Cog):
    """Cog for handling the sticky messages system functionality"""
    
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
        
        # Rate limiting for sticky messages (per channel)
        self.sticky_message_cooldowns = {}
        
        # Global rate limiting for sticky messages (Discord ToS compliance)
        self.global_sticky_last_action = None
        
        # Processing locks to prevent duplicate sticky message processing
        self.sticky_processing_locks = set()
        
    async def cog_load(self):
        """Called when the cog is loaded"""
        logger.info("Loading Sticky Messages System cog...")
        
        # Check and repost sticky messages after restart
        await self.check_sticky_messages_on_startup()
        
        logger.info("Sticky Messages System cog loaded successfully")
        
    async def cog_unload(self):
        """Called when the cog is unloaded"""
        logger.info("Unloading Sticky Messages System cog...")
        logger.info("Sticky Messages System cog unloaded")

    @commands.Cog.listener()
    async def on_message(self, message):
        """Handle sticky message reposting for guild messages"""
        await self.handle_sticky_messages(message)

    async def handle_sticky_messages(self, message):
        """Handle sticky message reposting for guild messages"""
        try:
            # Only process messages in guilds
            if not message.guild:
                return

            # Debug logging for bot messages
            if message.author.bot:
                logger.info(f"[STICKY DEBUG] Bot message from {message.author} (ID: {message.author.id}) in {message.channel.name}: {message.content[:50]}")
                logger.info(f"[STICKY DEBUG] Our bot ID: {self.bot.user.id}, Message author ID: {message.author.id}, Same? {message.author.id == self.bot.user.id}")
                if message.embeds:
                    for i, embed in enumerate(message.embeds):
                        logger.info(f"[STICKY DEBUG] Embed {i}: title='{embed.title}', description='{embed.description[:50] if embed.description else 'None'}'")
                else:
                    logger.info(f"[STICKY DEBUG] No embeds in bot message")

            # Skip our own bot messages to prevent loops, EXCEPT for specific bot messages that should trigger sticky
            if message.author.id == self.bot.user.id:
                # Allow sticky messages to be triggered by role assignment notifications and other important bot messages
                # But skip if this message IS a sticky message to prevent infinite loops
                if message.embeds and len(message.embeds) > 0:
                    embed = message.embeds[0]
                    if embed.title == "📌 Stickied Message":
                        logger.debug(f"[STICKY DEBUG] Skipping our own sticky message")
                        return
                    elif embed.title == "✅ Role Assigned":
                        logger.info(f"[STICKY DEBUG] Processing sticky message triggered by role assignment notification")
                        # Continue processing for role assignment messages
                    else:
                        logger.debug(f"[STICKY DEBUG] Processing sticky message triggered by bot embed: {embed.title}")
                        # Continue processing for other bot embeds
                else:
                    logger.debug(f"[STICKY DEBUG] Skipping our own bot message (no embeds)")
                    return

            # Check if sticky messages is a premium feature
            if self.db.is_premium_feature("sticky_messages"):
                # Skip if server owner doesn't have subscription
                if message.guild.owner_id and not self.db.is_server_premium_for_user(message.guild.id, message.guild.owner_id):
                    logger.debug(f"Skipping sticky message for {message.guild.name} - sticky messages is premium and no subscription")
                    return

            # Skip if sticky messages system is disabled
            if not self.db.is_system_enabled(message.guild.id, "sticky_messages"):
                logger.debug(f"Skipping sticky message for {message.guild.name} - system disabled")
                return

            # Check if this channel has a sticky message
            sticky_data = self.db.get_sticky_message(message.guild.id, message.channel.id)
            if not sticky_data:
                if message.author.bot:
                    logger.info(f"[STICKY DEBUG] No sticky message configured for {message.channel.name} in {message.guild.name}")
                return

            # Check if this specific sticky message is enabled
            if not sticky_data.get('enabled', True):  # Default to True for backward compatibility
                logger.debug(f"Skipping sticky message for {message.channel.name} - individually disabled")
                return

            if message.author.bot:
                logger.info(f"[STICKY DEBUG] Found sticky message config for {message.channel.name}, proceeding with repost logic")

            logger.debug(f"Processing sticky message for {message.author} (bot: {message.author.bot}) in {message.channel.name}: {message.content[:50]}")

            # Check if this channel is already being processed (prevent race conditions)
            channel_id = message.channel.id
            if channel_id in self.sticky_processing_locks:
                logger.debug(f"Skipping - channel {message.channel.name} is already processing a sticky message")
                return

            # Don't repost if this message IS the sticky message (prevent infinite loop)
            if sticky_data.get('message_id') == message.id:
                logger.debug(f"Skipping - this message IS the sticky message")
                return

            # Check if the sticky message is still the latest message in the channel
            if await self.is_sticky_message_latest(message.channel, sticky_data.get('message_id')):
                logger.debug(f"Skipping - sticky message is already the latest")
                return  # Sticky message is already the latest, no need to repost

            # Global rate limiting: prevent too many sticky messages across all channels
            current_time = datetime.now(timezone.utc)

            if self.global_sticky_last_action:
                global_time_diff = (current_time - self.global_sticky_last_action).total_seconds()
                if global_time_diff < 0.5:  # 0.5 second global cooldown (reduced from 1 second)
                    logger.debug(f"Global sticky message cooldown active, {0.5 - global_time_diff:.1f}s remaining")
                    return

            # Per-channel rate limiting: only repost sticky message once every 2 seconds per channel
            # This prevents Discord API rate limiting and ensures ToS compliance
            if channel_id in self.sticky_message_cooldowns:
                time_diff = (current_time - self.sticky_message_cooldowns[channel_id]).total_seconds()
                if time_diff < 2:  # 2 second cooldown to prevent duplicates (reduced from 3 seconds)
                    logger.debug(f"Sticky message cooldown active for {message.channel.name}, {2 - time_diff:.1f}s remaining")
                    return

            # Acquire processing lock to prevent race conditions
            self.sticky_processing_locks.add(channel_id)

            # Update cooldowns IMMEDIATELY to prevent race conditions
            self.sticky_message_cooldowns[channel_id] = current_time
            self.global_sticky_last_action = current_time

            # Try to delete the previous sticky message
            if sticky_data.get('message_id'):
                logger.debug(f"Attempting to delete old sticky message {sticky_data['message_id']} in {message.channel.name}")
                try:
                    old_message = await message.channel.fetch_message(sticky_data['message_id'])
                    # Triple-check this is actually the sticky message before deleting
                    if (old_message.author == self.bot.user and
                        old_message.embeds and
                        len(old_message.embeds) > 0):

                        embed = old_message.embeds[0]
                        if (embed.title == "📌 Stickied Message" and
                            embed.footer and
                            "Stickied message" in embed.footer.text):

                            await old_message.delete()
                            logger.debug(f"Successfully deleted old sticky message {sticky_data['message_id']}")
                        else:
                            logger.warning(f"Message {sticky_data['message_id']} doesn't match sticky message format (title: {embed.title}), skipping deletion")
                            # Clear the invalid message ID from database
                            self.db.update_sticky_message_id(message.guild.id, message.channel.id, None)
                    else:
                        logger.warning(f"Message {sticky_data['message_id']} is not from bot or has no embeds, skipping deletion")
                        # Clear the invalid message ID from database
                        self.db.update_sticky_message_id(message.guild.id, message.channel.id, None)

                    # Small delay to respect Discord rate limits
                    await asyncio.sleep(0.3)
                except discord.NotFound:
                    # Message was already deleted, that's fine - clear the ID from database
                    logger.debug(f"Old sticky message {sticky_data['message_id']} was already deleted")
                    self.db.update_sticky_message_id(message.guild.id, message.channel.id, None)
                except discord.Forbidden:
                    logger.warning(f"No permission to delete sticky message {sticky_data['message_id']} in {message.channel.name}")
                    return
                except discord.HTTPException as e:
                    if e.status == 429:  # Rate limited
                        logger.warning(f"Rate limited when deleting sticky message {sticky_data['message_id']} in {message.channel.name}")
                        return
                    logger.error(f"HTTP error deleting old sticky message {sticky_data['message_id']}: {e}")
                    return
                except Exception as e:
                    logger.error(f"Error deleting old sticky message: {e}")
                    return

            # Create and send new sticky message
            try:
                embed = discord.Embed(
                    title="📌 Stickied Message",
                    description=sticky_data['content'],
                    color=discord.Color.blue(),
                    timestamp=datetime.now(timezone.utc)
                )
                embed.set_footer(text=f"Stickied message | ryzuo.com")

                new_message = await message.channel.send(embed=embed)
                logger.info(f"Reposted sticky message in {message.channel.name} ({message.guild.name}) triggered by {message.author} (bot: {message.author.bot})")
                logger.debug(f"New sticky message ID: {new_message.id}, Old ID was: {sticky_data.get('message_id')}")

                if message.author.bot:
                    logger.info(f"[STICKY DEBUG] Successfully sent sticky message triggered by bot {message.author}")

                # Update the sticky message ID in database
                success = self.db.update_sticky_message_id(
                    message.guild.id,
                    message.channel.id,
                    new_message.id
                )
                logger.debug(f"Database update success: {success}")

                # Log the sticky message repost activity
                self.db.log_sticky_activity(
                    message.guild.id,
                    message.channel.id,
                    message.author.id,
                    f"{message.author.name}#{message.author.discriminator}",
                    "reposted",
                    sticky_data['content']
                )

            except discord.Forbidden:
                logger.warning(f"No permission to send sticky message in {message.channel.name}")
            except discord.HTTPException as e:
                if e.status == 429:  # Rate limited
                    logger.warning(f"Rate limited when sending sticky message in {message.channel.name}")
                    # Reset cooldown so we can try again later
                    if channel_id in self.sticky_message_cooldowns:
                        del self.sticky_message_cooldowns[channel_id]
                else:
                    logger.error(f"HTTP error sending sticky message: {e}")
            except Exception as e:
                logger.error(f"Error sending new sticky message: {e}")
            finally:
                # Always release the processing lock
                self.sticky_processing_locks.discard(channel_id)

        except Exception as e:
            logger.error(f"Error in handle_sticky_messages: {e}", exc_info=True)

    async def is_sticky_message_latest(self, channel, sticky_message_id):
        """Check if the sticky message is the latest message in the channel"""
        try:
            if not sticky_message_id:
                return False

            # Get the latest message in the channel
            async for message in channel.history(limit=1):
                return message.id == sticky_message_id

            return False
        except Exception as e:
            logger.error(f"Error checking if sticky message is latest: {e}")
            return False

    async def check_sticky_messages_on_startup(self):
        """Check all sticky messages on startup and repost if they're not the latest message"""
        try:
            logger.info("Checking sticky messages on startup...")

            for guild in self.bot.guilds:
                if guild.owner_id and not self.db.is_server_premium_for_user(guild.id, guild.owner_id):
                    continue

                # Get all sticky messages for this server
                sticky_messages = self.db.get_all_sticky_messages(guild.id)

                for sticky_data in sticky_messages:
                    try:
                        # Skip if this sticky message is disabled
                        if not sticky_data.get('enabled', True):
                            continue

                        channel = guild.get_channel(sticky_data['channel_id'])
                        if not channel:
                            logger.warning(f"Sticky message channel {sticky_data['channel_id']} not found in guild {guild.name}")
                            continue

                        # Check if the sticky message is still the latest
                        if not await self.is_sticky_message_latest(channel, sticky_data.get('message_id')):
                            logger.info(f"Reposting sticky message in {channel.name} ({guild.name}) after restart")

                            # Delete old sticky message if it exists
                            if sticky_data.get('message_id'):
                                try:
                                    old_message = await channel.fetch_message(sticky_data['message_id'])
                                    await old_message.delete()
                                except discord.NotFound:
                                    pass  # Already deleted
                                except Exception as e:
                                    logger.error(f"Error deleting old sticky message: {e}")

                            # Create new sticky message
                            embed = discord.Embed(
                                title="📌 Stickied Message",
                                description=sticky_data['content'],
                                color=discord.Color.blue(),
                                timestamp=datetime.now(timezone.utc)
                            )
                            embed.set_footer(text=f"Stickied message | ryzuo.com")

                            try:
                                new_message = await channel.send(embed=embed)

                                # Update database with new message ID
                                self.db.update_sticky_message_id(
                                    guild.id,
                                    channel.id,
                                    new_message.id
                                )

                                # Log the repost
                                self.db.log_sticky_activity(
                                    guild.id,
                                    channel.id,
                                    self.bot.user.id,
                                    f"{self.bot.user.name}#{self.bot.user.discriminator}",
                                    "reposted_on_startup",
                                    sticky_data['content']
                                )

                            except discord.Forbidden:
                                logger.warning(f"No permission to send sticky message in {channel.name}")
                            except Exception as e:
                                logger.error(f"Error sending sticky message: {e}")

                    except Exception as e:
                        logger.error(f"Error processing sticky message: {e}")

            logger.info("Sticky message startup check completed")

        except Exception as e:
            logger.error(f"Error in check_sticky_messages_on_startup: {e}", exc_info=True)


async def setup(bot):
    """Setup function for the cog"""
    await bot.add_cog(StickyMessagesSystem(bot))

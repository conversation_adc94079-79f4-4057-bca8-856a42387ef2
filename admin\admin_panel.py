#!/usr/bin/env python3
"""
ry<PERSON><PERSON> Bot Admin Panel - Comprehensive Administration Interface
Runs on separate port for enhanced security
"""

import asyncio
import json
import logging
import os
import subprocess
import sys
from datetime import datetime, timedelta, timezone
from functools import wraps
from typing import Optional, Dict, Any, List

from dotenv import load_dotenv
from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify, g
from flask_wtf.csrf import CSRFProtect, generate_csrf
import jwt as pyjwt
import requests
from bson import ObjectId

# Load environment variables
load_dotenv()

# Import local modules
try:
    from database import DatabaseManager
    from shard_communication import shard_comm
    from admin_database import AdminDatabaseManager
except ImportError as e:
    logging.error(f"Failed to import required modules: {e}")
    raise

# Configuration
MONGO_URL = os.getenv('MONGO_URL')
SECRET_KEY = os.getenv('SECRET_KEY')
ADMIN_USER_ID = os.getenv('ADMIN_USER_ID')
ADMIN_PORT = int(os.getenv('ADMIN_PORT', 5001))
DISCORD_CLIENT_ID = os.getenv('DISCORD_CLIENT_ID')
DISCORD_CLIENT_SECRET = os.getenv('DISCORD_CLIENT_SECRET')

if not all([MONGO_URL, SECRET_KEY, ADMIN_USER_ID]):
    raise ValueError("Missing required environment variables for admin panel")

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
admin_app = Flask(__name__, template_folder='templates')
admin_app.secret_key = SECRET_KEY

# Configure session settings
admin_app.config['SESSION_COOKIE_SECURE'] = os.getenv('FLASK_ENV') == 'production'
admin_app.config['SESSION_COOKIE_HTTPONLY'] = True
admin_app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
admin_app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=8)  # Shorter session for admin

# Initialize CSRF protection
csrf = CSRFProtect(admin_app)

# Add CSRF token to all templates
@admin_app.context_processor
def inject_csrf_token():
    return dict(csrf_token=generate_csrf)

# Add current time to all templates
@admin_app.context_processor
def inject_current_time():
    return dict(current_time=datetime.now(timezone.utc))

# Initialize database managers
db = DatabaseManager(MONGO_URL)
admin_db = AdminDatabaseManager(MONGO_URL)

def admin_required(f):
    """Decorator to require admin authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_token' not in session:
            return redirect(url_for('admin_login'))
        
        try:
            # Verify admin token
            payload = pyjwt.decode(session['admin_token'], SECRET_KEY, algorithms=['HS256'])
            
            # Check if user is admin
            if str(payload.get('user_id')) != str(ADMIN_USER_ID):
                session.clear()
                flash('Access denied. Admin privileges required.', 'error')
                return redirect(url_for('admin_login'))
            
            # Add admin info to g object
            g.admin = payload
            
        except pyjwt.ExpiredSignatureError:
            session.clear()
            flash('Session expired. Please log in again.', 'warning')
            return redirect(url_for('admin_login'))
        except Exception as e:
            logger.error(f"Admin authentication error: {e}")
            session.clear()
            flash('Authentication error. Please log in again.', 'error')
            return redirect(url_for('admin_login'))
        
        return f(*args, **kwargs)
    return decorated_function

def create_admin_jwt_token(user_data):
    """Create JWT token for admin session"""
    payload = {
        'user_id': str(user_data['id']),
        'username': user_data['username'],
        'discriminator': user_data.get('discriminator', '0'),
        'avatar': user_data.get('avatar'),
        'admin': True,
        'exp': datetime.utcnow() + timedelta(hours=8)
    }
    return pyjwt.encode(payload, SECRET_KEY, algorithm='HS256')

@admin_app.route('/')
def admin_index():
    """Admin panel home - redirect to login if not authenticated"""
    if 'admin_token' in session:
        return redirect(url_for('admin_dashboard'))
    return redirect(url_for('admin_login'))

@admin_app.route('/login')
def admin_login():
    """Admin login page"""
    if 'admin_token' in session:
        return redirect(url_for('admin_dashboard'))
    
    # Generate Discord OAuth URL for admin
    discord_auth_url = (
        f"https://discord.com/api/oauth2/authorize?"
        f"client_id={DISCORD_CLIENT_ID}&"
        f"redirect_uri={os.getenv('ADMIN_DOMAIN', 'http://localhost:5001')}/oauth/callback&"
        f"response_type=code&"
        f"scope=identify"
    )
    
    return render_template('admin/login.html', discord_auth_url=discord_auth_url)

@admin_app.route('/oauth/callback')
def admin_oauth_callback():
    """Handle Discord OAuth callback for admin"""
    code = request.args.get('code')
    if not code:
        flash('Authorization failed. Please try again.', 'error')
        return redirect(url_for('admin_login'))
    
    try:
        # Exchange code for token
        token_data = {
            'client_id': DISCORD_CLIENT_ID,
            'client_secret': DISCORD_CLIENT_SECRET,
            'grant_type': 'authorization_code',
            'code': code,
            'redirect_uri': f"{os.getenv('ADMIN_DOMAIN', 'http://localhost:5001')}/oauth/callback",
            'scope': 'identify'
        }
        
        token_response = requests.post(
            'https://discord.com/api/oauth2/token',
            data=token_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )
        token_response.raise_for_status()
        token_json = token_response.json()
        
        # Get user info
        user_response = requests.get(
            'https://discord.com/api/users/@me',
            headers={'Authorization': f"Bearer {token_json['access_token']}"}
        )
        user_response.raise_for_status()
        discord_user = user_response.json()
        
        # Check if user is admin
        if str(discord_user['id']) != str(ADMIN_USER_ID):
            flash('Access denied. Admin privileges required.', 'error')
            return redirect(url_for('admin_login'))
        
        # Create admin session
        admin_token = create_admin_jwt_token(discord_user)
        session['admin_token'] = admin_token
        session['admin_user'] = discord_user
        session.permanent = True
        
        # Log admin login
        admin_db.log_admin_action(
            admin_id=discord_user['id'],
            action='admin_login',
            details=f"Admin {discord_user['username']} logged in",
            ip_address=request.remote_addr
        )
        
        flash(f'Welcome, {discord_user["username"]}!', 'success')
        return redirect(url_for('admin_dashboard'))
        
    except Exception as e:
        logger.error(f"Admin OAuth error: {e}")
        flash('Authentication failed. Please try again.', 'error')
        return redirect(url_for('admin_login'))

@admin_app.route('/logout')
@admin_required
def admin_logout():
    """Admin logout"""
    admin_user = session.get('admin_user', {})

    # Log admin logout
    admin_db.log_admin_action(
        admin_id=admin_user.get('id'),
        action='admin_logout',
        details=f"Admin {admin_user.get('username')} logged out",
        ip_address=request.remote_addr
    )

    session.clear()
    flash('You have been logged out.', 'info')
    return redirect(url_for('admin_login'))

@admin_app.route('/dashboard')
@admin_required
def admin_dashboard():
    """Main admin dashboard"""
    try:
        # Get system statistics
        stats = {
            'total_users': db.db['ryzuo-users'].count_documents({}),
            'total_servers': db.db['ryzuo-server-configs'].count_documents({}),
            'active_subscriptions': db.db['ryzuo-subscriptions'].count_documents({
                'status': {'$in': ['active', 'past_due']},
                'disabled_by_admin': False
            }),
            'total_giveaways': db.db['ryzuo-giveaways'].count_documents({}),
            'blocked_ips': len(admin_db.get_blocked_ips()),
            'recent_admin_actions': len(admin_db.get_admin_logs(limit=10))
        }

        # Get recent admin logs
        recent_logs = admin_db.get_admin_logs(limit=10)

        # Get feature toggles
        feature_toggles = admin_db.get_feature_toggles()

        return render_template('admin/dashboard.html',
                             stats=stats,
                             recent_logs=recent_logs,
                             feature_toggles=feature_toggles)

    except Exception as e:
        logger.error(f"Error loading admin dashboard: {e}")
        flash('Error loading dashboard data.', 'error')
        return render_template('admin/dashboard.html', stats={}, recent_logs=[], feature_toggles={})

# ========== USER MANAGEMENT ROUTES ==========

@admin_app.route('/users')
@admin_required
def admin_users():
    """User management page"""
    page = int(request.args.get('page', 1))
    per_page = 50
    search = request.args.get('search', '')
    filter_type = request.args.get('filter', 'all')  # all, premium, disabled

    try:
        # Build query
        query = {}
        if search:
            query['$or'] = [
                {'username': {'$regex': search, '$options': 'i'}},
                {'user_id': {'$regex': search, '$options': 'i'}}
            ]

        # Get users from database
        users_collection = db.db['ryzuo-users']
        total_users = users_collection.count_documents(query)

        users = list(users_collection.find(query)
                    .skip((page - 1) * per_page)
                    .limit(per_page)
                    .sort('created_at', -1))

        # Get subscription info for each user
        for user in users:
            subscription = db.get_user_subscription(int(user['user_id']))
            user['subscription'] = subscription
            user['_id'] = str(user['_id'])

        # Filter by subscription status if needed
        if filter_type == 'premium':
            users = [u for u in users if u.get('subscription')]
        elif filter_type == 'disabled':
            users = [u for u in users if u.get('disabled_by_admin', False)]

        total_pages = (total_users + per_page - 1) // per_page

        return render_template('admin/users.html',
                             users=users,
                             page=page,
                             total_pages=total_pages,
                             search=search,
                             filter_type=filter_type)

    except Exception as e:
        logger.error(f"Error loading users: {e}")
        flash('Error loading users.', 'error')
        return render_template('admin/users.html', users=[], page=1, total_pages=1)

@admin_app.route('/api/user/<user_id>/subscription', methods=['POST'])
@admin_required
def manage_user_subscription(user_id):
    """Add or modify user subscription"""
    try:
        data = request.get_json()
        action = data.get('action')  # add, remove, modify
        tier = data.get('tier')  # weekly, monthly, yearly, lifetime

        admin_id = g.admin['user_id']

        if action == 'add':
            # Create manual subscription
            success = admin_db.create_manual_subscription(
                user_id=int(user_id),
                tier=tier,
                admin_id=admin_id
            )

            if success:
                admin_db.log_admin_action(
                    admin_id=admin_id,
                    action='subscription_add',
                    details=f"Added {tier} subscription to user {user_id}",
                    target_user_id=user_id
                )
                return jsonify({'success': True, 'message': f'{tier.title()} subscription added'})
            else:
                return jsonify({'success': False, 'error': 'Failed to add subscription'}), 500

        elif action == 'remove':
            # Remove subscription
            success = db.disable_subscription_admin(user_id)

            if success:
                admin_db.log_admin_action(
                    admin_id=admin_id,
                    action='subscription_remove',
                    details=f"Removed subscription from user {user_id}",
                    target_user_id=user_id
                )
                return jsonify({'success': True, 'message': 'Subscription removed'})
            else:
                return jsonify({'success': False, 'error': 'Failed to remove subscription'}), 500

        return jsonify({'success': False, 'error': 'Invalid action'}), 400

    except Exception as e:
        logger.error(f"Error managing user subscription: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_app.route('/api/user/<user_id>/disable', methods=['POST'])
@admin_required
def disable_user_account(user_id):
    """Disable a user account"""
    try:
        data = request.get_json()
        reason = data.get('reason', 'No reason provided')
        admin_id = g.admin['user_id']

        success = admin_db.disable_user_account(int(user_id), reason, admin_id)

        if success:
            return jsonify({'success': True, 'message': 'User account disabled'})
        else:
            return jsonify({'success': False, 'error': 'Failed to disable user account'}), 500

    except Exception as e:
        logger.error(f"Error disabling user account: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_app.route('/api/user/<user_id>/enable', methods=['POST'])
@admin_required
def enable_user_account(user_id):
    """Enable a user account"""
    try:
        admin_id = g.admin['user_id']

        success = admin_db.enable_user_account(int(user_id), admin_id)

        if success:
            return jsonify({'success': True, 'message': 'User account enabled'})
        else:
            return jsonify({'success': False, 'error': 'Failed to enable user account'}), 500

    except Exception as e:
        logger.error(f"Error enabling user account: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_app.route('/api/user/<user_id>/delete', methods=['DELETE'])
@admin_required
def delete_user_completely(user_id):
    """Completely delete a user and all their data"""
    try:
        admin_id = g.admin['user_id']

        success = admin_db.delete_user_completely(int(user_id), admin_id)

        if success:
            return jsonify({'success': True, 'message': 'User completely deleted'})
        else:
            return jsonify({'success': False, 'error': 'Failed to delete user'}), 500

    except Exception as e:
        logger.error(f"Error deleting user: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ========== FEATURE TOGGLE ROUTES ==========

@admin_app.route('/api/admin/feature-toggles', methods=['GET', 'POST'])
@admin_required
def manage_feature_toggles():
    """Get or update feature toggles"""
    if request.method == 'GET':
        try:
            toggles = admin_db.get_feature_toggles()
            return jsonify({'success': True, 'features': toggles})
        except Exception as e:
            logger.error(f"Error getting feature toggles: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    elif request.method == 'POST':
        try:
            data = request.get_json()
            features = data.get('features', {})
            admin_id = g.admin['user_id']

            success = admin_db.update_feature_toggles(features, admin_id)

            if success:
                return jsonify({'success': True, 'message': 'Feature toggles updated'})
            else:
                return jsonify({'success': False, 'error': 'Failed to update feature toggles'}), 500

        except Exception as e:
            logger.error(f"Error updating feature toggles: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

# ========== NOTIFICATION ROUTES ==========

@admin_app.route('/api/admin/send-notification', methods=['POST'])
@admin_required
def send_global_notification():
    """Send a global notification to all users"""
    try:
        data = request.get_json()
        title = data.get('title')
        message = data.get('message')
        notification_type = data.get('type', 'info')
        admin_id = g.admin['user_id']

        if not title or not message:
            return jsonify({'success': False, 'error': 'Title and message are required'}), 400

        # Create notification for all users
        success = admin_db.create_global_notification(title, message, notification_type, admin_id)

        if success:
            return jsonify({'success': True, 'message': 'Global notification sent'})
        else:
            return jsonify({'success': False, 'error': 'Failed to send notification'}), 500

    except Exception as e:
        logger.error(f"Error sending global notification: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_app.route('/api/send_notification', methods=['POST'])
@admin_required
def send_notification():
    """Send a notification to a user or all users"""
    data = request.get_json()
    if not data:
        return jsonify({"error": "No data provided"}), 400

    title = data.get('title')
    message = data.get('message')
    user_id = data.get('user_id')  # Optional, if None will send to all users
    notification_type = data.get('type', 'info')

    if not title or not message:
        return jsonify({"error": "Title and message are required"}), 400

    success = True
    if user_id:
        # Send to specific user
        success = db.add_notification(int(user_id), title, message, notification_type)
    else:
        # Send to all users
        # Get all users who have ever logged in
        users = db.get_all_users()  # You'll need to implement this method
        for user in users:
            success = success and db.add_notification(int(user['id']), title, message, notification_type)

    return jsonify({"success": success})

# ========== SERVER MANAGEMENT ROUTES ==========

@admin_app.route('/servers')
@admin_required
def admin_servers():
    """Server management page"""
    page = int(request.args.get('page', 1))
    per_page = 50
    search = request.args.get('search', '')

    try:
        # Build query
        query = {}
        if search:
            query['$or'] = [
                {'server_name': {'$regex': search, '$options': 'i'}},
                {'server_id': {'$regex': search, '$options': 'i'}}
            ]

        # Get servers from database
        servers_collection = db.db['ryzuo-server-configs']
        total_servers = servers_collection.count_documents(query)

        servers = list(servers_collection.find(query)
                      .skip((page - 1) * per_page)
                      .limit(per_page)
                      .sort('created_at', -1))

        # Get additional info for each server
        for server in servers:
            server['_id'] = str(server['_id'])
            # Get license info
            license_info = db.get_server_license_key(int(server['server_id']))
            server['license'] = license_info

            # Get owner subscription info
            if server.get('owner_id'):
                subscription = db.get_user_subscription(int(server['owner_id']))
                server['owner_subscription'] = subscription

        total_pages = (total_servers + per_page - 1) // per_page

        return render_template('admin/servers.html',
                             servers=servers,
                             page=page,
                             total_pages=total_pages,
                             search=search)

    except Exception as e:
        logger.error(f"Error loading servers: {e}")
        flash('Error loading servers.', 'error')
        return render_template('admin/servers.html', servers=[], page=1, total_pages=1)

@admin_app.route('/api/admin/server/<server_id>/details')
@admin_required
def get_server_details(server_id):
    """Get detailed server information"""
    try:
        server_details = admin_db.get_server_details(server_id)
        
        if server_details:
            return jsonify({'success': True, 'server': server_details})
        else:
            return jsonify({'success': False, 'error': 'Server not found'}), 404
    
    except Exception as e:
        logger.error(f"Error getting server details: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_app.route('/api/admin/server/<server_id>/reset', methods=['POST'])
@admin_required
def reset_server_config(server_id):
    """Reset server configuration to defaults"""
    try:
        admin_id = g.admin['user_id']
        
        success = admin_db.reset_server_config(server_id, admin_id)
        
        if success:
            return jsonify({'success': True, 'message': 'Server configuration reset to defaults'})
        else:
            return jsonify({'success': False, 'error': 'Failed to reset server configuration'}), 500
    
    except Exception as e:
        logger.error(f"Error resetting server config: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_app.route('/api/admin/server/<server_id>/delete', methods=['DELETE'])
@admin_required
def delete_server_completely(server_id):
    """Completely delete a server and all its data"""
    try:
        admin_id = g.admin['user_id']
        
        success = admin_db.delete_server_completely(server_id, admin_id)
        
        if success:
            return jsonify({'success': True, 'message': 'Server completely deleted'})
        else:
            return jsonify({'success': False, 'error': 'Failed to delete server'}), 500
    
    except Exception as e:
        logger.error(f"Error deleting server: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ========== ENVIRONMENT VARIABLE MANAGEMENT ==========

@admin_app.route('/environment')
@admin_required
def admin_environment():
    """Environment variable management page"""
    try:
        # Get current environment configurations
        website_config = admin_db.get_env_config('website') or {'env_vars': {}}
        bot_config = admin_db.get_env_config('bot') or {'env_vars': {}}

        return render_template('admin/environment.html',
                             website_config=website_config,
                             bot_config=bot_config)

    except Exception as e:
        logger.error(f"Error loading environment config: {e}")
        flash('Error loading environment configuration.', 'error')
        return render_template('admin/environment.html', website_config={}, bot_config={})

@admin_app.route('/api/admin/environment/<config_type>', methods=['POST'])
@admin_required
def update_environment_config(config_type):
    """Update environment configuration"""
    try:
        if config_type not in ['website', 'bot']:
            return jsonify({'success': False, 'error': 'Invalid config type'}), 400

        data = request.get_json()
        env_vars = data.get('env_vars', {})
        admin_id = g.admin['user_id']

        success = admin_db.update_env_config(config_type, env_vars, admin_id)

        if success:
            return jsonify({'success': True, 'message': f'{config_type.title()} environment updated'})
        else:
            return jsonify({'success': False, 'error': 'Failed to update environment'}), 500

    except Exception as e:
        logger.error(f"Error updating environment config: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ========== PROCESS MANAGEMENT ==========

@admin_app.route('/processes')
@admin_required
def admin_processes():
    """Process management page"""
    try:
        # Get process statuses
        processes = admin_db.get_process_status()

        return render_template('admin/processes.html', processes=processes)

    except Exception as e:
        logger.error(f"Error loading process status: {e}")
        flash('Error loading process status.', 'error')
        return render_template('admin/processes.html', processes={})

@admin_app.route('/api/admin/process/<process_name>/restart', methods=['POST'])
@admin_required
def restart_process(process_name):
    """Restart a specific process"""
    try:
        admin_id = g.admin['user_id']

        success = admin_db.restart_process(process_name, admin_id)

        if success:
            return jsonify({'success': True, 'message': f'Restart requested for {process_name}'})
        else:
            return jsonify({'success': False, 'error': 'Failed to request restart'}), 500

    except Exception as e:
        logger.error(f"Error restarting process: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_app.route('/api/admin/process/<process_name>/logs')
@admin_required
def get_process_logs(process_name):
    """Get logs for a specific process"""
    try:
        lines = int(request.args.get('lines', 100))
        logs = admin_db.get_process_logs(process_name, lines)

        return jsonify({'success': True, 'logs': logs})

    except Exception as e:
        logger.error(f"Error getting process logs: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ========== SECURITY AND BLOCKING ==========

@admin_app.route('/security')
@admin_required
def admin_security():
    """Security and blocking management page"""
    try:
        # Get blocked IPs
        blocked_ips = admin_db.get_blocked_ips()

        # Get users with blocked commands
        command_blocks_collection = db.db['ryzuo-command-blocks']
        blocked_users = list(command_blocks_collection.find({}))

        for user in blocked_users:
            user['_id'] = str(user['_id'])

        return render_template('admin/security.html',
                             blocked_ips=blocked_ips,
                             blocked_users=blocked_users)

    except Exception as e:
        logger.error(f"Error loading security data: {e}")
        flash('Error loading security data.', 'error')
        return render_template('admin/security.html', blocked_ips=[], blocked_users=[])

@admin_app.route('/api/admin/block-ip', methods=['POST'])
@admin_required
def block_ip_address():
    """Block an IP address"""
    try:
        data = request.get_json()
        ip_address = data.get('ip_address')
        reason = data.get('reason', 'No reason provided')
        admin_id = g.admin['user_id']

        if not ip_address:
            return jsonify({'success': False, 'error': 'IP address is required'}), 400

        success = admin_db.block_ip(ip_address, reason, admin_id)

        if success:
            return jsonify({'success': True, 'message': f'IP {ip_address} blocked'})
        else:
            return jsonify({'success': False, 'error': 'Failed to block IP'}), 500

    except Exception as e:
        logger.error(f"Error blocking IP: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_app.route('/api/admin/unblock-ip', methods=['POST'])
@admin_required
def unblock_ip_address():
    """Unblock an IP address"""
    try:
        data = request.get_json()
        ip_address = data.get('ip_address')
        admin_id = g.admin['user_id']

        if not ip_address:
            return jsonify({'success': False, 'error': 'IP address is required'}), 400

        success = admin_db.unblock_ip(ip_address, admin_id)

        if success:
            return jsonify({'success': True, 'message': f'IP {ip_address} unblocked'})
        else:
            return jsonify({'success': False, 'error': 'Failed to unblock IP'}), 500

    except Exception as e:
        logger.error(f"Error unblocking IP: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_app.route('/premium-features')
@admin_required
def admin_premium_features():
    """Premium features management page"""
    try:
        # Get all features and their premium status
        features = db.get_features()

        # Organize features with their sub-features
        feature_config = {}
        for feature in features:
            feature_config[feature['name']] = {
                'is_premium': feature.get('is_premium', False),
                'premium_subfeatures': feature.get('premium_subfeatures', []),
                'description': feature.get('description', ''),
                'enabled': feature.get('enabled', True)
            }

        # Add default features if not in database
        default_features = [
            'repping', 'auto_roling', 'vent', 'tempvoice', 'music',
            'sticky_messages', 'giveaways', 'dm_support', 'statistics', 'on-site-logs'
        ]

        for feature_name in default_features:
            if feature_name not in feature_config:
                feature_config[feature_name] = {
                    'is_premium': False,
                    'premium_subfeatures': [],
                    'description': '',
                    'enabled': True
                }

        return render_template('admin/premium_features.html', features=feature_config)

    except Exception as e:
        logger.error(f"Error loading premium features: {e}")
        flash('Error loading premium features configuration.', 'error')
        return render_template('admin/premium_features.html', features={})

def disable_premium_features_for_non_premium_users(features):
    """Disable premium features for users who don't have premium subscriptions"""
    disabled_count = 0

    try:
        # Get all servers from the database
        all_servers = admin_db.get_all_servers()
        logger.info(f"Found {len(all_servers)} servers to check for premium feature disabling")

        for server in all_servers:
            server_id = server.get('server_id')
            if not server_id:
                continue

            # For each premium feature, disable it for servers without premium
            for feature_name, feature_data in features.items():
                if feature_data.get('is_premium', False):
                    # Get server owner from Discord API or database
                    try:
                        # Try to get owner info from server config
                        server_config = admin_db.get_server_config(server_id)
                        owner_id = server_config.get('owner_id') if server_config else None

                        if owner_id:
                            has_premium = admin_db.is_server_premium_for_user(server_id, owner_id)

                            if not has_premium:
                                disabled = disable_feature_for_server(server_id, feature_name)
                                if disabled:
                                    disabled_count += 1
                                    logger.info(f"Disabled premium feature '{feature_name}' for server {server_id} (owner {owner_id} has no premium)")
                        else:
                            # If no owner info, disable premium features as a safety measure
                            disabled = disable_feature_for_server(server_id, feature_name)
                            if disabled:
                                disabled_count += 1
                                logger.info(f"Disabled premium feature '{feature_name}' for server {server_id} (no owner info)")

                    except Exception as e:
                        logger.error(f"Error checking premium status for server {server_id}: {e}")
                        continue

        return disabled_count

    except Exception as e:
        logger.error(f"Error disabling premium features for non-premium users: {e}")
        return 0

def disable_feature_for_server(server_id, feature_name):
    """Disable a specific feature for a server by removing/resetting its configuration"""
    try:
        disabled = False

        if feature_name == 'repping':
            # Remove repping configuration completely
            if admin_db.remove_repping_config(server_id):
                disabled = True
            # Also disable in server config
            if admin_db.update_server_config_field(server_id, 'repping_enabled', False):
                disabled = True

        elif feature_name == 'tempvoice':
            # Remove tempvoice configuration completely
            if admin_db.remove_tempvoice_config(server_id):
                disabled = True

        elif feature_name == 'music':
            # Remove music configuration completely
            if admin_db.remove_music_config(server_id):
                disabled = True

        elif feature_name == 'dm_support':
            # Remove DM support configuration completely
            if admin_db.remove_dm_support_config(server_id):
                disabled = True

        elif feature_name == 'sticky_messages':
            # Remove sticky messages configuration completely
            if admin_db.remove_sticky_messages_config(server_id):
                disabled = True

        elif feature_name == 'auto_roling':
            # Remove auto-roling configuration completely
            if admin_db.remove_auto_roling_config(server_id):
                disabled = True
            # Also disable in server config
            if admin_db.update_server_config_field(server_id, 'auto_roling_enabled', False):
                disabled = True

        elif feature_name == 'vent':
            # Remove vent configuration completely
            if admin_db.remove_vent_config(server_id):
                disabled = True
            # Also disable in server config
            if admin_db.update_server_config_field(server_id, 'vent_enabled', False):
                disabled = True

        elif feature_name == 'statistics':
            # Statistics access is handled by route-level enforcement
            return True

        elif feature_name == 'on-site-logs':
            # Logs access is handled by route-level enforcement
            return True

        return disabled

    except Exception as e:
        logger.error(f"Error disabling feature {feature_name} for server {server_id}: {e}")
        return False

@admin_app.route('/api/admin/premium-features', methods=['POST'])
@admin_required
def update_premium_features():
    """Update premium features configuration"""
    try:
        data = request.get_json()
        features_config = data.get('features', {})
        admin_id = g.admin['user_id']

        # Update each feature's premium status and sub-features
        for feature_name, config in features_config.items():
            # Update feature in database
            admin_db.update_feature_premium_status(
                feature_name,
                config.get('is_premium', False),
                config.get('premium_subfeatures', [])
            )

        # Log admin action
        admin_db.log_admin_action(
            admin_id,
            'premium_features_updated',
            f'Updated premium features configuration'
        )

        # Clear any caches that might be storing premium feature status
        try:
            # If you have Redis or other caching, clear it here
            logger.info("Premium features updated - caches cleared")
        except Exception as e:
            logger.warning(f"Could not clear caches: {e}")

        # Automatically disable premium features for non-premium users
        try:
            # Get the newly marked premium features
            newly_premium_features = []
            for feature_name, config in features_config.items():
                if config.get('is_premium', False):
                    newly_premium_features.append(feature_name)

            if newly_premium_features:
                logger.info(f"Features marked as premium: {newly_premium_features}")
                disabled_count = disable_premium_features_for_non_premium_users(features_config)
                logger.info(f"Automatically disabled {disabled_count} premium features for non-premium users")
            else:
                logger.info("No features marked as premium, skipping enforcement")
        except Exception as e:
            logger.error(f"Error disabling premium features: {e}")

        return jsonify({'success': True, 'message': 'Premium features updated successfully'})

    except Exception as e:
        logger.error(f"Error updating premium features: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_app.route('/api/admin/auto-role-limits', methods=['GET', 'POST'])
@admin_required
def auto_role_limits():
    """Get or set auto-role limits configuration"""
    try:
        if request.method == 'GET':
            # Get current auto-role limits
            limits = admin_db.get_auto_role_limits()
            return jsonify({'success': True, 'limits': limits})

        elif request.method == 'POST':
            # Update auto-role limits
            limits_data = request.get_json()

            if not limits_data:
                return jsonify({'success': False, 'error': 'No limits data provided'}), 400

            # Validate limits data
            required_fields = [
                'free_join_roles', 'free_reaction_roles', 'free_embed_dropdowns',
                'premium_join_roles', 'premium_reaction_roles_per_message',
                'premium_reaction_messages', 'premium_embed_dropdowns', 'premium_roles_per_dropdown'
            ]

            for field in required_fields:
                if field not in limits_data:
                    return jsonify({'success': False, 'error': f'Missing field: {field}'}), 400

                # Ensure values are integers and within reasonable bounds
                try:
                    value = int(limits_data[field])
                    if value < 0 or value > 100:  # Reasonable bounds
                        return jsonify({'success': False, 'error': f'Invalid value for {field}: must be 0-100'}), 400
                    limits_data[field] = value
                except (ValueError, TypeError):
                    return jsonify({'success': False, 'error': f'Invalid value for {field}: must be a number'}), 400

            # Save limits to database
            success = admin_db.set_auto_role_limits(limits_data)

            if success:
                # Log admin action
                admin_id = session.get('admin_user_id')
                admin_db.log_admin_action(
                    admin_id,
                    'auto_role_limits_updated',
                    f'Updated auto-role limits configuration'
                )

                return jsonify({'success': True, 'message': 'Auto-role limits updated successfully'})
            else:
                return jsonify({'success': False, 'error': 'Failed to save auto-role limits'}), 500

    except Exception as e:
        logger.error(f"Error handling auto-role limits: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ========== PRICING MANAGEMENT ==========

@admin_app.route('/pricing')
@admin_required
def admin_pricing():
    """Pricing management page"""
    try:
        # Get current pricing configuration
        pricing_config = admin_db.get_pricing_config()

        return render_template('admin/pricing.html', pricing_config=pricing_config)

    except Exception as e:
        logger.error(f"Error loading pricing config: {e}")
        flash('Error loading pricing configuration.', 'error')
        return render_template('admin/pricing.html', pricing_config={})

@admin_app.route('/api/admin/pricing', methods=['POST'])
@admin_required
def update_pricing():
    """Update pricing configuration"""
    try:
        data = request.get_json()
        pricing_data = data.get('pricing', {})
        admin_id = g.admin['user_id']

        success = admin_db.update_pricing_config(pricing_data, admin_id)

        if success:
            return jsonify({'success': True, 'message': 'Pricing updated successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to update pricing'}), 500

    except Exception as e:
        logger.error(f"Error updating pricing: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ========== COMMAND BLOCKING ==========

@admin_app.route('/api/admin/block-commands', methods=['POST'])
@admin_required
def block_user_commands():
    """Block specific commands for a user"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        commands = data.get('commands', [])
        reason = data.get('reason', 'No reason provided')
        admin_id = g.admin['user_id']

        if not user_id or not commands:
            return jsonify({'success': False, 'error': 'User ID and commands are required'}), 400

        success = admin_db.block_user_commands(int(user_id), commands, reason, admin_id)

        if success:
            return jsonify({'success': True, 'message': f'Commands blocked for user {user_id}'})
        else:
            return jsonify({'success': False, 'error': 'Failed to block commands'}), 500

    except Exception as e:
        logger.error(f"Error blocking user commands: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_app.route('/api/admin/unblock-commands', methods=['POST'])
@admin_required
def unblock_user_commands():
    """Unblock all commands for a user"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        admin_id = g.admin['user_id']

        if not user_id:
            return jsonify({'success': False, 'error': 'User ID is required'}), 400

        success = admin_db.unblock_user_commands(int(user_id), admin_id)

        if success:
            return jsonify({'success': True, 'message': f'Commands unblocked for user {user_id}'})
        else:
            return jsonify({'success': False, 'error': 'Failed to unblock commands'}), 500

    except Exception as e:
        logger.error(f"Error unblocking user commands: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ========== ADMIN LOGS ==========

@admin_app.route('/logs')
@admin_required
def admin_logs():
    """Admin logs page"""
    try:
        page = int(request.args.get('page', 1))
        per_page = 50
        action_filter = request.args.get('action', '')

        # Get admin logs
        skip = (page - 1) * per_page
        logs = admin_db.get_admin_logs(limit=per_page, skip=skip, action=action_filter)

        # Get total count for pagination
        logs_collection = db.db['ryzuo-admin-logs']
        query = {}
        if action_filter:
            query['action'] = action_filter

        total_logs = logs_collection.count_documents(query)
        total_pages = (total_logs + per_page - 1) // per_page

        # Get unique actions for filter dropdown
        unique_actions = logs_collection.distinct('action')

        return render_template('admin/logs.html',
                             logs=logs,
                             page=page,
                             total_pages=total_pages,
                             action_filter=action_filter,
                             unique_actions=unique_actions)

    except Exception as e:
        logger.error(f"Error loading admin logs: {e}")
        flash('Error loading admin logs.', 'error')
        return render_template('admin/logs.html', logs=[], page=1, total_pages=1, unique_actions=[])

# ========== SYSTEM STATISTICS API ==========

@admin_app.route('/api/admin/statistics')
@admin_required
def get_system_statistics():
    """Get comprehensive system statistics"""
    try:
        stats = admin_db.get_system_statistics()
        return jsonify({'success': True, 'statistics': stats})

    except Exception as e:
        logger.error(f"Error getting system statistics: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_app.route('/notifications')
@admin_required
def admin_notifications():
    """Admin notifications page"""
    return render_template('admin/notifications.html')

if __name__ == '__main__':
    admin_app.run(debug=False, host='0.0.0.0', port=ADMIN_PORT)

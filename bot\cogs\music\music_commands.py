"""
Music Commands for Discord Bot
Implements all slash commands for the music system
"""

import discord
from discord.ext import commands
from discord import app_commands
import logging
from typing import Optional
from .music_system import MusicManager, LoopMode
from .lyrics_service import lyrics_service
from ..core.utilities import CoreUtilities

logger = logging.getLogger(__name__)

def setup_music_commands(bot):
    """Setup all music-related slash commands"""
    
    music_manager = MusicManager(bot)
    
    # Add music manager to bot for access from other parts
    bot.music_manager = music_manager
    
    async def check_music_permissions(interaction: discord.Interaction) -> bool:
        """Check if user has permission to use DJ commands"""
        try:
            # Check if music system is enabled
            if not bot.db.is_system_enabled(interaction.guild.id, "music"):
                await interaction.response.send_message("❌ Music system is disabled for this server.", ephemeral=True)
                return False

            # Get music settings
            music_settings = bot.db.get_music_settings(interaction.guild.id)

            # Check if music is explicitly disabled (for premium enforcement)
            if music_settings and music_settings.get('enabled') is False:
                await interaction.response.send_message("❌ Music system is currently disabled. Premium subscription required.", ephemeral=True)
                return False
            dj_role_id = music_settings.get('dj_role_id') if music_settings else None
            
            # Check DJ role
            if dj_role_id:
                dj_role = interaction.guild.get_role(dj_role_id)
                if dj_role and dj_role in interaction.user.roles:
                    return True
                    
            # Check if user is temp channel owner
            temp_channel = bot.db.get_user_temp_channel(interaction.guild.id, interaction.user.id)
            if temp_channel and interaction.user.voice and interaction.user.voice.channel:
                if interaction.user.voice.channel.id == temp_channel['channel_id']:
                    return True
                    
            # Fallback to manage channels permission
            if interaction.user.guild_permissions.manage_channels:
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"Error checking music permissions: {e}")
            return False

    async def check_basic_music_access(interaction: discord.Interaction) -> bool:
        """Check if user can use basic music commands"""
        try:
            # Check if music system is enabled
            if not bot.db.is_system_enabled(interaction.guild.id, "music"):
                await interaction.response.send_message("❌ Music system is disabled for this server.", ephemeral=True)
                return False
                
            # Check if user is in a voice channel
            if not interaction.user.voice or not interaction.user.voice.channel:
                await interaction.response.send_message("❌ You must be in a voice channel to use music commands.", ephemeral=True)
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Error checking basic music access: {e}")
            return False

    @bot.tree.command(name="play", description="Play a song from YouTube")
    @app_commands.describe(song_title="The song title or YouTube URL to play")
    @CoreUtilities.require_premium_feature(bot.db, "music")
    async def play_command(interaction: discord.Interaction, song_title: str):
        """Play a song command"""
        try:
            if not await check_basic_music_access(interaction):
                return
                
            await interaction.response.defer()
            
            user_channel = interaction.user.voice.channel
            guild_id = interaction.guild.id
            
            # Clean up any disconnected clients first
            await music_manager.cleanup_disconnected_clients()

            # Join voice channel if not connected
            if not music_manager.is_connected(guild_id):
                # Send initial status message
                status_msg = await interaction.followup.send("🔄 Connecting to voice channel...")

                success = await music_manager.join_voice_channel(user_channel, guild_id)
                if not success:
                    status = music_manager.get_voice_client_status(guild_id)
                    await status_msg.edit(content=f"❌ Failed to join voice channel. Status: {status}\n\n**Troubleshooting:**\n• Make sure the bot has permission to join your voice channel\n• Try again in a few seconds\n• If the issue persists, try moving to a different voice channel")
                    return
                else:
                    await status_msg.edit(content="✅ Connected to voice channel!")
            else:
                # Check if bot is in same channel as user
                voice_client = music_manager.voice_clients[guild_id]
                if voice_client.channel != user_channel:
                    # If music is playing, don't allow channel switching
                    if music_manager.is_guild_playing(guild_id):
                        await interaction.followup.send("❌ Cannot switch channels while music is playing.")
                        return
                    else:
                        # Move to user's channel
                        try:
                            await voice_client.move_to(user_channel)
                        except Exception as e:
                            logger.error(f"Failed to move to channel: {e}")
                            await interaction.followup.send("❌ Failed to move to your voice channel. Please try again.")
                            return
                        
            # Search for song
            song = await music_manager.search_youtube(song_title)
            if not song:
                await interaction.followup.send(f"❌ Could not find: **{song_title}**")
                return
                
            # Set requested by
            song.requested_by = interaction.user
            
            # Add to playlist and play
            success = await music_manager.play_song(guild_id, song)
            if success:
                embed = discord.Embed(
                    title="🎵 Added to Queue",
                    description=f"**{song.title}**\nDuration: {song.duration}\nRequested by: {interaction.user.mention}",
                    color=0x00ff00
                )
                embed.set_thumbnail(url=song.thumbnail)
                
                # Show queue position if not playing immediately
                playlist = music_manager.get_playlist(guild_id)
                if music_manager.is_guild_playing(guild_id):
                    embed.add_field(name="Queue Position", value=f"{len(playlist) + 1}", inline=True)
                else:
                    embed.add_field(name="Status", value="Now Playing", inline=True)
                    
                await interaction.followup.send(embed=embed)
                
                # Log to dashboard
                bot.db.log_bot_activity_with_discord(
                    guild_id,
                    interaction.user.id,
                    f"{interaction.user.name}#{interaction.user.discriminator}",
                    f"Added song to queue: {song.title}",
                    f"Duration: {song.duration}",
                    "music",
                    interaction.channel.id
                )
            else:
                await interaction.followup.send("❌ Failed to play song.")
                
        except Exception as e:
            logger.error(f"Error in play command: {e}")
            await interaction.followup.send("❌ An error occurred while trying to play the song.")

    @bot.tree.command(name="playtop", description="Add a song to the top of the queue")
    @app_commands.describe(song_title="The song title or YouTube URL to add to top of queue")
    @CoreUtilities.require_premium_feature(bot.db, "music", "playlist_management")
    async def playtop_command(interaction: discord.Interaction, song_title: str):
        """Add song to top of queue command"""
        try:
            if not await check_basic_music_access(interaction):
                return
                
            # Check DJ permissions
            if not await check_music_permissions(interaction):
                await interaction.response.send_message("❌ You need DJ permissions to use this command.", ephemeral=True)
                return
                
            await interaction.response.defer()
            
            user_channel = interaction.user.voice.channel
            guild_id = interaction.guild.id
            
            # Join voice channel if not connected
            if not music_manager.is_connected(guild_id):
                success = await music_manager.join_voice_channel(user_channel, guild_id)
                if not success:
                    await interaction.followup.send("❌ Failed to join voice channel.")
                    return
                    
            # Search for song
            song = await music_manager.search_youtube(song_title)
            if not song:
                await interaction.followup.send(f"❌ Could not find: **{song_title}**")
                return
                
            # Set requested by
            song.requested_by = interaction.user
            
            # Add to top of playlist
            success = await music_manager.add_to_playlist(guild_id, song, position="top")
            if success:
                embed = discord.Embed(
                    title="⏫ Added to Top of Queue",
                    description=f"**{song.title}**\nDuration: {song.duration}\nRequested by: {interaction.user.mention}",
                    color=0xff9900
                )
                embed.set_thumbnail(url=song.thumbnail)
                embed.add_field(name="Queue Position", value="Next", inline=True)
                
                await interaction.followup.send(embed=embed)
                
                # Log to dashboard
                bot.db.log_bot_activity_with_discord(
                    guild_id,
                    interaction.user.id,
                    f"{interaction.user.name}#{interaction.user.discriminator}",
                    f"Added song to top of queue: {song.title}",
                    f"Duration: {song.duration}",
                    "music",
                    interaction.channel.id
                )
            else:
                await interaction.followup.send("❌ Failed to add song to queue.")
                
        except Exception as e:
            logger.error(f"Error in playtop command: {e}")
            await interaction.followup.send("❌ An error occurred while trying to add the song.")

    @bot.tree.command(name="skip", description="Skip the current song")
    async def skip_command(interaction: discord.Interaction):
        """Skip current song command"""
        try:
            if not await check_basic_music_access(interaction):
                return
                
            # Check DJ permissions
            if not await check_music_permissions(interaction):
                await interaction.response.send_message("❌ You need DJ permissions to use this command.", ephemeral=True)
                return
                
            guild_id = interaction.guild.id
            
            if not music_manager.is_connected(guild_id):
                await interaction.response.send_message("❌ Bot is not connected to a voice channel.", ephemeral=True)
                return
                
            current_song = music_manager.get_current_song(guild_id)
            if not current_song:
                await interaction.response.send_message("❌ No song is currently playing.", ephemeral=True)
                return
                
            success = await music_manager.skip_song(guild_id)
            if success:
                await interaction.response.send_message(f"⏭️ Skipped: **{current_song.title}**")
                
                # Log to dashboard
                bot.db.log_bot_activity_with_discord(
                    guild_id,
                    interaction.user.id,
                    f"{interaction.user.name}#{interaction.user.discriminator}",
                    f"Skipped song: {current_song.title}",
                    None,
                    "music",
                    interaction.channel.id
                )
            else:
                await interaction.response.send_message("❌ Failed to skip song.", ephemeral=True)
                
        except Exception as e:
            logger.error(f"Error in skip command: {e}")
            await interaction.response.send_message("❌ An error occurred while trying to skip the song.", ephemeral=True)

    @bot.tree.command(name="pause", description="Pause the current song")
    async def pause_command(interaction: discord.Interaction):
        """Pause current song command"""
        try:
            if not await check_basic_music_access(interaction):
                return

            # Check DJ permissions
            if not await check_music_permissions(interaction):
                await interaction.response.send_message("❌ You need DJ permissions to use this command.", ephemeral=True)
                return

            guild_id = interaction.guild.id

            if not music_manager.is_connected(guild_id):
                await interaction.response.send_message("❌ Bot is not connected to a voice channel.", ephemeral=True)
                return

            if music_manager.is_guild_paused(guild_id):
                await interaction.response.send_message("❌ Music is already paused.", ephemeral=True)
                return

            if not music_manager.is_guild_playing(guild_id):
                await interaction.response.send_message("❌ No music is currently playing.", ephemeral=True)
                return

            success = await music_manager.pause_song(guild_id)
            if success:
                current_song = music_manager.get_current_song(guild_id)
                await interaction.response.send_message(f"⏸️ Paused: **{current_song.title if current_song else 'Current song'}**")

                # Log to dashboard
                bot.db.log_bot_activity_with_discord(
                    guild_id,
                    interaction.user.id,
                    f"{interaction.user.name}#{interaction.user.discriminator}",
                    "Paused music",
                    current_song.title if current_song else None,
                    "music",
                    interaction.channel.id
                )
            else:
                await interaction.response.send_message("❌ Failed to pause music.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error in pause command: {e}")
            await interaction.response.send_message("❌ An error occurred while trying to pause the music.", ephemeral=True)

    @bot.tree.command(name="unpause", description="Resume the current song")
    async def unpause_command(interaction: discord.Interaction):
        """Resume current song command"""
        try:
            if not await check_basic_music_access(interaction):
                return

            # Check DJ permissions
            if not await check_music_permissions(interaction):
                await interaction.response.send_message("❌ You need DJ permissions to use this command.", ephemeral=True)
                return

            guild_id = interaction.guild.id

            if not music_manager.is_connected(guild_id):
                await interaction.response.send_message("❌ Bot is not connected to a voice channel.", ephemeral=True)
                return

            if not music_manager.is_guild_paused(guild_id):
                await interaction.response.send_message("❌ Music is not paused.", ephemeral=True)
                return

            success = await music_manager.unpause_song(guild_id)
            if success:
                current_song = music_manager.get_current_song(guild_id)
                await interaction.response.send_message(f"▶️ Resumed: **{current_song.title if current_song else 'Current song'}**")

                # Log to dashboard
                bot.db.log_bot_activity_with_discord(
                    guild_id,
                    interaction.user.id,
                    f"{interaction.user.name}#{interaction.user.discriminator}",
                    "Resumed music",
                    current_song.title if current_song else None,
                    "music",
                    interaction.channel.id
                )
            else:
                await interaction.response.send_message("❌ Failed to resume music.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error in unpause command: {e}")
            await interaction.response.send_message("❌ An error occurred while trying to resume the music.", ephemeral=True)

    @bot.tree.command(name="loop", description="Set loop mode for music")
    @app_commands.describe(mode="Loop mode: off, song, or playlist")
    @app_commands.choices(mode=[
        app_commands.Choice(name="Off", value="off"),
        app_commands.Choice(name="Current Song", value="song"),
        app_commands.Choice(name="Playlist", value="playlist")
    ])
    async def loop_command(interaction: discord.Interaction, mode: str):
        """Set loop mode command"""
        try:
            if not await check_basic_music_access(interaction):
                return

            # Check DJ permissions
            if not await check_music_permissions(interaction):
                await interaction.response.send_message("❌ You need DJ permissions to use this command.", ephemeral=True)
                return

            guild_id = interaction.guild.id

            # Convert string to enum
            loop_mode = LoopMode(mode)
            music_manager.set_loop_mode(guild_id, loop_mode)

            mode_names = {
                LoopMode.OFF: "Off",
                LoopMode.SONG: "Current Song",
                LoopMode.PLAYLIST: "Playlist"
            }

            mode_emojis = {
                LoopMode.OFF: "🔁",
                LoopMode.SONG: "🔂",
                LoopMode.PLAYLIST: "🔁"
            }

            await interaction.response.send_message(
                f"{mode_emojis[loop_mode]} Loop mode set to: **{mode_names[loop_mode]}**"
            )

            # Log to dashboard
            bot.db.log_bot_activity_with_discord(
                guild_id,
                interaction.user.id,
                f"{interaction.user.name}#{interaction.user.discriminator}",
                f"Set loop mode to: {mode_names[loop_mode]}",
                None,
                "music",
                interaction.channel.id
            )

        except Exception as e:
            logger.error(f"Error in loop command: {e}")
            await interaction.response.send_message("❌ An error occurred while setting loop mode.", ephemeral=True)

    @bot.tree.command(name="shuffle", description="Shuffle the current playlist")
    async def shuffle_command(interaction: discord.Interaction):
        """Shuffle playlist command"""
        try:
            if not await check_basic_music_access(interaction):
                return

            # Check DJ permissions
            if not await check_music_permissions(interaction):
                await interaction.response.send_message("❌ You need DJ permissions to use this command.", ephemeral=True)
                return

            guild_id = interaction.guild.id
            playlist = music_manager.get_playlist(guild_id)

            if len(playlist) < 2:
                await interaction.response.send_message("❌ Need at least 2 songs in queue to shuffle.", ephemeral=True)
                return

            success = music_manager.shuffle_playlist(guild_id)
            if success:
                await interaction.response.send_message(f"🔀 Shuffled playlist with **{len(playlist)}** songs!")

                # Log to dashboard
                bot.db.log_bot_activity_with_discord(
                    guild_id,
                    interaction.user.id,
                    f"{interaction.user.name}#{interaction.user.discriminator}",
                    f"Shuffled playlist ({len(playlist)} songs)",
                    None,
                    "music",
                    interaction.channel.id
                )
            else:
                await interaction.response.send_message("❌ Failed to shuffle playlist.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error in shuffle command: {e}")
            await interaction.response.send_message("❌ An error occurred while shuffling the playlist.", ephemeral=True)

    @bot.tree.command(name="lyrics", description="Get lyrics for the current song")
    async def lyrics_command(interaction: discord.Interaction):
        """Get lyrics command"""
        try:
            if not await check_basic_music_access(interaction):
                return

            guild_id = interaction.guild.id
            current_song = music_manager.get_current_song(guild_id)

            if not current_song:
                await interaction.response.send_message("❌ No song is currently playing.", ephemeral=True)
                return

            await interaction.response.defer()

            # Get lyrics
            lyrics_text = await lyrics_service.get_lyrics(current_song.title)

            if lyrics_text:
                # Split into multiple messages if too long
                if len(lyrics_text) > 2000:
                    # Send first part
                    await interaction.followup.send(lyrics_text[:2000])
                    # Send remaining parts
                    remaining = lyrics_text[2000:]
                    while remaining:
                        chunk = remaining[:2000]
                        remaining = remaining[2000:]
                        await interaction.followup.send(chunk)
                else:
                    await interaction.followup.send(lyrics_text)

                # Log to dashboard
                bot.db.log_bot_activity_with_discord(
                    guild_id,
                    interaction.user.id,
                    f"{interaction.user.name}#{interaction.user.discriminator}",
                    f"Requested lyrics for: {current_song.title}",
                    None,
                    "music",
                    interaction.channel.id
                )
            else:
                await interaction.followup.send(f"❌ Could not find lyrics for: **{current_song.title}**")

        except Exception as e:
            logger.error(f"Error in lyrics command: {e}")
            await interaction.followup.send("❌ An error occurred while fetching lyrics.")

    @bot.tree.command(name="playlist", description="Show the current playlist")
    async def playlist_command(interaction: discord.Interaction):
        """Show playlist command"""
        try:
            if not await check_basic_music_access(interaction):
                return

            guild_id = interaction.guild.id
            current_song = music_manager.get_current_song(guild_id)
            playlist = music_manager.get_playlist(guild_id)
            loop_mode = music_manager.get_loop_mode(guild_id)

            embed = discord.Embed(title="🎵 Current Playlist", color=0x0099ff)

            # Current song
            if current_song:
                status = "⏸️ Paused" if music_manager.is_guild_paused(guild_id) else "▶️ Playing"
                embed.add_field(
                    name="Now Playing",
                    value=f"{status} **{current_song.title}**\nRequested by: {current_song.requested_by.mention}",
                    inline=False
                )
            else:
                embed.add_field(name="Now Playing", value="Nothing", inline=False)

            # Queue
            if playlist:
                queue_text = ""
                for i, song in enumerate(playlist[:10], 1):  # Show max 10 songs
                    queue_text += f"{i}. **{song.title}** ({song.duration})\n   Requested by: {song.requested_by.mention}\n"

                if len(playlist) > 10:
                    queue_text += f"\n... and {len(playlist) - 10} more songs"

                embed.add_field(name=f"Queue ({len(playlist)} songs)", value=queue_text, inline=False)
            else:
                embed.add_field(name="Queue", value="Empty", inline=False)

            # Loop mode
            loop_names = {
                LoopMode.OFF: "Off",
                LoopMode.SONG: "Current Song",
                LoopMode.PLAYLIST: "Playlist"
            }
            embed.add_field(name="Loop Mode", value=loop_names[loop_mode], inline=True)

            # Total duration
            if playlist:
                total_seconds = 0
                for song in playlist:
                    # Parse duration (MM:SS or HH:MM:SS)
                    try:
                        time_parts = song.duration.split(':')
                        if len(time_parts) == 2:  # MM:SS
                            total_seconds += int(time_parts[0]) * 60 + int(time_parts[1])
                        elif len(time_parts) == 3:  # HH:MM:SS
                            total_seconds += int(time_parts[0]) * 3600 + int(time_parts[1]) * 60 + int(time_parts[2])
                    except:
                        pass  # Skip if duration parsing fails

                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                if hours > 0:
                    total_duration = f"{hours}:{minutes:02d}:00"
                else:
                    total_duration = f"{minutes}:00"

                embed.add_field(name="Total Duration", value=total_duration, inline=True)

            await interaction.response.send_message(embed=embed)

        except Exception as e:
            logger.error(f"Error in playlist command: {e}")
            await interaction.response.send_message("❌ An error occurred while showing the playlist.", ephemeral=True)

    @bot.tree.command(name="music-status", description="Show music system status and diagnostics")
    async def music_status_command(interaction: discord.Interaction):
        """Show music system status command"""
        try:
            if not await check_basic_music_access(interaction):
                return

            guild_id = interaction.guild.id

            embed = discord.Embed(title="🎵 Music System Status", color=0x0099ff)

            # Voice connection status
            is_connected = music_manager.is_connected(guild_id)
            voice_status = music_manager.get_voice_client_status(guild_id)
            embed.add_field(name="Voice Connection", value=f"**Status:** {voice_status}", inline=False)

            if is_connected:
                voice_client = music_manager.voice_clients[guild_id]
                embed.add_field(name="Connected Channel", value=voice_client.channel.mention, inline=True)
                embed.add_field(name="Latency", value=f"{voice_client.latency:.2f}ms", inline=True)

            # Current song
            current_song = music_manager.get_current_song(guild_id)
            if current_song:
                status = "⏸️ Paused" if music_manager.is_guild_paused(guild_id) else "▶️ Playing"
                embed.add_field(name="Current Song", value=f"{status} **{current_song.title}**", inline=False)
            else:
                embed.add_field(name="Current Song", value="None", inline=False)

            # Queue info
            playlist = music_manager.get_playlist(guild_id)
            embed.add_field(name="Queue Length", value=f"{len(playlist)} songs", inline=True)

            # Loop mode
            loop_mode = music_manager.get_loop_mode(guild_id)
            loop_names = {
                LoopMode.OFF: "Off",
                LoopMode.SONG: "Current Song",
                LoopMode.PLAYLIST: "Playlist"
            }
            embed.add_field(name="Loop Mode", value=loop_names[loop_mode], inline=True)

            # Music settings
            music_settings = bot.db.get_music_settings(guild_id)
            if music_settings:
                dj_role_id = music_settings.get('dj_role_id')
                if dj_role_id:
                    dj_role = interaction.guild.get_role(dj_role_id)
                    embed.add_field(name="DJ Role", value=dj_role.mention if dj_role else "Role not found", inline=True)
                else:
                    embed.add_field(name="DJ Role", value="Manage Channels Permission", inline=True)

            await interaction.response.send_message(embed=embed)

        except Exception as e:
            logger.error(f"Error in music status command: {e}")
            await interaction.response.send_message("❌ An error occurred while getting music status.", ephemeral=True)

    @bot.tree.command(name="music-help", description="Get help with music system troubleshooting")
    async def music_help_command(interaction: discord.Interaction):
        """Music help and troubleshooting command"""
        try:
            embed = discord.Embed(
                title="🎵 Music System Help & Troubleshooting",
                color=0x0099ff,
                description="Having issues with the music system? Here are some common solutions:"
            )

            embed.add_field(
                name="🔧 Voice Connection Issues",
                value="• Make sure the bot has **Connect** and **Speak** permissions in your voice channel\n"
                      "• Try moving to a different voice channel\n"
                      "• Wait 30 seconds and try again\n"
                      "• Use `/music-status` to check connection status",
                inline=False
            )

            embed.add_field(
                name="🎮 Command Permissions",
                value="• **All users:** `/play`, `/lyrics`, `/playlist`\n"
                      "• **DJ role/Temp channel owners:** `/skip`, `/pause`, `/unpause`, `/loop`, `/shuffle`, `/playtop`\n"
                      "• **Fallback:** Users with 'Manage Channels' permission",
                inline=False
            )

            embed.add_field(
                name="⚙️ Configuration",
                value="• Enable music system in dashboard settings\n"
                      "• Set a DJ role (optional) in music configuration\n"
                      "• Ensure log channel is configured for activity logging",
                inline=False
            )

            embed.add_field(
                name="🎵 Music Features",
                value="• **Auto-deafen:** Bot deafens itself when joining voice\n"
                      "• **Auto-disconnect:** Leaves after 30s if no users in channel\n"
                      "• **Channel lock:** Can't switch channels while playing music\n"
                      "• **Loop modes:** Off, Current Song, or Playlist",
                inline=False
            )

            embed.add_field(
                name="🆘 Still Having Issues?",
                value="1. Check `/music-status` for diagnostics\n"
                      "2. Try restarting the music system (leave and rejoin voice)\n"
                      "3. Verify bot permissions in voice channels\n"
                      "4. Contact server administrators",
                inline=False
            )

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error in music help command: {e}")
            await interaction.response.send_message("❌ An error occurred while showing help.", ephemeral=True)

    @bot.tree.command(name="music-reset", description="Reset the music system connection")
    async def music_reset_command(interaction: discord.Interaction):
        """Reset music system connection command"""
        try:
            if not await check_basic_music_access(interaction):
                return

            # Check DJ permissions for reset command
            if not await check_music_permissions(interaction):
                await interaction.response.send_message("❌ You need DJ permissions to reset the music system.", ephemeral=True)
                return

            guild_id = interaction.guild.id

            await interaction.response.defer()

            # Force disconnect and cleanup
            if guild_id in music_manager.voice_clients:
                await music_manager.leave_voice_channel(guild_id, "Manual reset")

            # Clean up any remaining data
            await music_manager.cleanup_disconnected_clients()

            await interaction.followup.send("🔄 Music system has been reset. You can now use `/play` to start fresh.")

            # Log the reset
            bot.db.log_bot_activity_with_discord(
                guild_id,
                interaction.user.id,
                f"{interaction.user.name}#{interaction.user.discriminator}",
                "Reset music system",
                "Forced disconnect and cleanup",
                "music",
                interaction.channel.id
            )

        except Exception as e:
            logger.error(f"Error in music reset command: {e}")
            await interaction.followup.send("❌ An error occurred while resetting the music system.")

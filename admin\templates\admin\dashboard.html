{% extends "admin/base.html" %}

{% block title %}Dashboard{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-tachometer-alt me-2"></i>
        Admin Dashboard
    </h1>
    <div class="text-muted">
        <i class="fas fa-clock me-1"></i>
        Last updated: <span id="lastUpdated">{{ current_time.strftime('%Y-%m-%d %H:%M:%S') if current_time else 'Loading...' }}</span>
    </div>
</div>

<!-- System Statistics -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6">
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_users or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-users me-1"></i>
                Total Users
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6">
        <div class="stat-card success">
            <div class="stat-number">{{ stats.total_servers or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-server me-1"></i>
                Total Servers
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6">
        <div class="stat-card warning">
            <div class="stat-number">{{ stats.active_subscriptions or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-crown me-1"></i>
                Active Subscriptions
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6">
        <div class="stat-card danger">
            <div class="stat-number">{{ stats.blocked_ips or 0 }}</div>
            <div class="stat-label">
                <i class="fas fa-ban me-1"></i>
                Blocked IPs
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4" id="notifications">
    <div class="col-12">
        <div class="card admin-card">
            <div class="card-header admin-card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ url_for('admin_users') }}" class="btn btn-admin w-100">
                            <i class="fas fa-user-plus me-2"></i>
                            Manage Users
                        </a>
                    </div>

                    <div class="col-md-3 col-sm-6 mb-3">
                        <button class="btn btn-admin w-100" onclick="showNotificationModal()">
                            <i class="fas fa-bullhorn me-2"></i>
                            Send Notification
                        </button>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <button class="btn btn-admin-danger w-100" onclick="showEmergencyActions()">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Emergency Actions
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


    
    <div class="col-md-6">
        <div class="card admin-card">
            <div class="card-header admin-card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    System Health
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Database Connection</span>
                    <span class="status-badge status-active">
                        <i class="fas fa-check-circle me-1"></i>Online
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Shard Communication</span>
                    <span class="status-badge status-active">
                        <i class="fas fa-check-circle me-1"></i>Active
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Admin Panel</span>
                    <span class="status-badge status-active">
                        <i class="fas fa-check-circle me-1"></i>Running
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Security Status</span>
                    <span class="status-badge status-active">
                        <i class="fas fa-shield-alt me-1"></i>Secure
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Admin Actions -->
<div class="row">
    <div class="col-12">
        <div class="card admin-card">
            <div class="card-header admin-card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    Recent Admin Actions
                </h5>
            </div>
            <div class="card-body">
                {% if recent_logs %}
                <div class="table-responsive">
                    <table class="table table-admin">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Action</th>
                                <th>Details</th>
                                <th>Admin</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in recent_logs %}
                            <tr>
                                <td>
                                    <small class="text-muted">
                                        {{ log.timestamp.strftime('%Y-%m-%d %H:%M:%S') if log.timestamp else 'N/A' }}
                                    </small>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ log.action }}</span>
                                </td>
                                <td>{{ log.details or 'No details' }}</td>
                                <td>{{ log.admin_id }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-history fa-3x mb-3"></i>
                    <p>No recent admin actions</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>



<!-- Notification Modal -->
<div class="modal fade" id="notificationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-bullhorn me-2"></i>
                    Send Global Notification
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="notificationForm">
                    <div class="mb-3">
                        <label for="notificationTitle" class="form-label">Title</label>
                        <input type="text" class="form-control" id="notificationTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="notificationMessage" class="form-label">Message</label>
                        <textarea class="form-control" id="notificationMessage" rows="4" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="notificationType" class="form-label">Type</label>
                        <select class="form-select" id="notificationType">
                            <option value="info">Info</option>
                            <option value="warning">Warning</option>
                            <option value="success">Success</option>
                            <option value="error">Error</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-admin" onclick="sendNotification()">Send Notification</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showNotificationModal() {
    new bootstrap.Modal(document.getElementById('notificationModal')).show();
}

function sendNotification() {
    const title = document.getElementById('notificationTitle').value;
    const message = document.getElementById('notificationMessage').value;
    const type = document.getElementById('notificationType').value;
    
    if (!title || !message) {
        alert('Please fill in all fields');
        return;
    }
    
    fetch('/api/admin/send-notification', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            title: title,
            message: message,
            type: type
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('notificationModal')).hide();
            alert('Notification sent successfully!');
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while sending notification');
    });
}

function showEmergencyActions() {
    if (confirm('Are you sure you want to access emergency actions? This should only be used in critical situations.')) {
        // Redirect to emergency actions page or show modal
        alert('Emergency actions would be implemented here');
    }
}

// Auto-refresh dashboard every 30 seconds
setInterval(() => {
    document.getElementById('lastUpdated').textContent = new Date().toLocaleString();
}, 30000);
</script>
{% endblock %}

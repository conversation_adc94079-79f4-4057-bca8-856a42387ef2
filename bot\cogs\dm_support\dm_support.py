"""
DM Support System Cog for Discord Bot
Handles DM-based support tickets
"""

import discord
from discord.ext import commands, tasks
from discord import ui, ButtonStyle, Embed, Color, Interaction
import asyncio
import logging
import io
from datetime import datetime, timezone, timedelta
from .views import DMSupportConfirmView

logger = logging.getLogger(__name__)

class DMSupportSystem(commands.Cog):
    """Cog for handling the DM support system functionality"""
    
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
        
    async def cog_load(self):
        """Called when the cog is loaded"""
        logger.info("Loading DM Support System cog...")
        
        # Add persistent views for DM support only
        
        # Start cleanup task
        if not self.cleanup_old_tickets.is_running():
            self.cleanup_old_tickets.start()
        
        logger.info("DM Support System cog loaded successfully")
        
    async def cog_unload(self):
        """Called when the cog is unloaded"""
        logger.info("Unloading DM Support System cog...")
        
        # Stop cleanup task
        if self.cleanup_old_tickets.is_running():
            self.cleanup_old_tickets.cancel()
            
        logger.info("DM Support System cog unloaded")

    @commands.Cog.listener()
    async def on_message(self, message):
        """Handle DM messages for support tickets"""
        # Only handle DMs from users (not bots)
        if not isinstance(message.channel, discord.DMChannel) or message.author.bot:
            return

        try:
            # Check if user already has an open ticket
            existing_ticket = self.db.get_user_open_dm_ticket(message.author.id)
            if existing_ticket:
                logger.info(f"User {message.author.id} has existing ticket, forwarding message")
                # Add message to existing ticket and forward to support channel
                await self.handle_dm_message_to_ticket(message, existing_ticket)
                return

            # Start new ticket process
            logger.info(f"Starting new ticket process for user {message.author.id}")
            await self.start_dm_support_process(message)
        except Exception as e:
            logger.error(f"Error handling DM from {message.author.id}: {e}", exc_info=True)
            try:
                await message.channel.send("❌ An error occurred while processing your message. Please try again later.")
            except:
                pass

    @commands.Cog.listener()
    async def on_channel_delete(self, channel):
        """Handle channel deletion for ticket cleanup"""
        try:
            # Check DM support tickets
            dm_collection = self.db.db['ryzuo-dm-support-tickets']
            dm_tickets = list(dm_collection.find({
                "channel_id": channel.id,
                "status": "open"
            }))

            for ticket in dm_tickets:
                try:
                    success = self.db.close_dm_support_ticket(str(ticket['_id']), None, "Channel deleted")
                    if success:
                        closed_count += 1
                        logger.info(f"Auto-closed DM support ticket {ticket['_id']} due to channel deletion")
                except Exception as e:
                    logger.error(f"Error closing DM support ticket {ticket.get('_id', 'unknown')}: {e}")

            if closed_count > 0:
                logger.info(f"Auto-closed {closed_count} tickets due to channel deletion")

        except Exception as e:
            logger.error(f"Error handling channel deletion for tickets: {e}")

    async def start_dm_support_process(self, message):
        """Start the DM support ticket creation process"""
        user = message.author
        logger.info(f"Starting DM support process for user {user.id} ({user})")

        try:
            # Get all servers the user shares with the bot
            user_servers = []
            for guild in self.bot.guilds:
                if guild.get_member(user.id):
                    # Check if server has DM support enabled and licensed
                    if self.db.is_server_licensed(guild.id):
                        # Check if DM support is a premium feature
                        if self.db.is_premium_feature("dm_support"):
                            # Skip if server owner doesn't have subscription
                            if guild.owner_id and not self.db.is_server_premium_for_user(guild.id, guild.owner_id):
                                continue

                        dm_settings = self.db.get_dm_support_settings(guild.id)
                        if dm_settings and dm_settings.get('enabled'):
                            user_servers.append(guild)

            if not user_servers:
                embed = discord.Embed(
                    title="❌ No Support Available",
                    description="You are not in any servers that have DM support enabled, or you don't share any servers with this bot.",
                    color=discord.Color.red()
                )
                await message.channel.send(embed=embed)
                return

            if len(user_servers) == 1:
                # Only one server, create ticket directly
                guild = user_servers[0]
                embed = discord.Embed(
                    title="🎫 Create Support Ticket",
                    description=f"Would you like to create a support ticket for **{guild.name}**?\n\n"
                               f"**Your message:**\n{message.content}",
                    color=discord.Color.blue()
                )
                view = DMSupportConfirmView(guild.id, message.content)
                await message.channel.send(embed=embed, view=view)
            else:
                # Multiple servers, let user choose
                from .views import ServerSelectionView
                await self.show_server_selection(message, user_servers)

        except Exception as e:
            logger.error(f"Error in start_dm_support_process for user {user.id}: {e}", exc_info=True)
            try:
                await message.channel.send("❌ An error occurred while processing your request. Please try again later.")
            except:
                pass

    async def show_server_selection(self, message, user_servers):
        """Show server selection for DM support"""
        embed = discord.Embed(
            title="🎫 Select Server for Support",
            description="You are in multiple servers with DM support. Please select which server you need help with:",
            color=discord.Color.blue()
        )

        # Add server list
        server_list = []
        for i, server in enumerate(user_servers[:5], 1):  # Limit to 5 servers
            server_list.append(f"{i}. **{server.name}** ({server.member_count} members)")

        embed.add_field(
            name="Available Servers",
            value="\n".join(server_list),
            inline=False
        )

        embed.set_footer(text="Click a number button to select a server, or ❌ to cancel")

        # Create view with numbered buttons
        from .views import ServerSelectionView
        view = ServerSelectionView(user_servers, message.content)
        await message.channel.send(embed=embed, view=view)

    async def handle_dm_message_to_ticket(self, message, ticket):
        """Handle DM message for existing ticket"""
        # Add message to ticket database
        self.db.add_message_to_dm_ticket(
            str(ticket['_id']),
            message.author.id,
            str(message.author),
            message.content,
            is_staff=False,
            attachment_urls=[attachment.url for attachment in message.attachments]
        )

        # Get the support channel
        guild = self.bot.get_guild(ticket['server_id'])
        if not guild:
            return

        channel = guild.get_channel(ticket['channel_id'])
        if not channel:
            return

        # Create embed for the message
        embed = discord.Embed(
            title="💬 User Message",
            description=message.content if message.content else "*(No text content)*",
            color=discord.Color.blue(),
            timestamp=message.created_at
        )
        embed.set_author(
            name=str(message.author),
            icon_url=message.author.display_avatar.url
        )

        # Add attachments if any
        if message.attachments:
            attachment_list = []
            for attachment in message.attachments:
                attachment_list.append(f"[{attachment.filename}]({attachment.url})")
            embed.add_field(
                name="📎 Attachments",
                value="\n".join(attachment_list),
                inline=False
            )

        await channel.send(embed=embed)

        # Confirm to user
        await message.add_reaction("✅")

    async def handle_support_response(self, message, ticket):
        """Handle support staff response to ticket"""
        # Add message to ticket database
        self.db.add_message_to_dm_ticket(
            str(ticket['_id']),
            message.author.id,
            str(message.author),
            message.content,
            is_staff=True,
            attachment_urls=[attachment.url for attachment in message.attachments]
        )

        # Get the user
        user = self.bot.get_user(ticket['user_id'])
        if not user:
            await message.channel.send("❌ Could not find the user to send the message to.")
            return

        # Create embed for the response
        embed = discord.Embed(
            title="💬 Support Response",
            description=message.content if message.content else "*(No text content)*",
            color=discord.Color.green(),
            timestamp=message.created_at
        )
        embed.set_author(
            name=f"{message.author} (Support)",
            icon_url=message.author.display_avatar.url
        )
        embed.set_footer(text=f"From: {message.guild.name}")

        # Add attachments if any
        if message.attachments:
            attachment_list = []
            for attachment in message.attachments:
                attachment_list.append(f"[{attachment.filename}]({attachment.url})")
            embed.add_field(
                name="📎 Attachments",
                value="\n".join(attachment_list),
                inline=False
            )

        try:
            await user.send(embed=embed)
            await message.add_reaction("✅")
        except discord.Forbidden:
            await message.channel.send("❌ Could not send message to user (DMs disabled or blocked).")
        except Exception as e:
            logger.error(f"Error sending DM to user {user.id}: {e}")
            await message.channel.send("❌ Failed to send message to user.")

    @tasks.loop(hours=1)
    async def cleanup_old_tickets(self):
        """Clean up old tickets every hour"""
        try:
            logger.info("Starting ticket cleanup task...")

            # Gender verification cleanup removed

            # Run cleanup for DM support tickets
            try:
                dm_count = await self.cleanup_dm_support_tickets()
                if dm_count > 0:
                    logger.info(f"Successfully cleaned up {dm_count} old DM support tickets")
            except Exception as e:
                logger.error(f"Error during DM support ticket cleanup: {e}", exc_info=True)

            # Check for deleted channels and auto-close tickets
            try:
                deleted_count = await self.cleanup_deleted_channel_tickets()
                if deleted_count > 0:
                    logger.info(f"Auto-closed {deleted_count} tickets due to deleted channels")
            except Exception as e:
                logger.error(f"Error during deleted channel cleanup: {e}", exc_info=True)

        except Exception as e:
            logger.error(f"Error in cleanup_old_tickets task: {e}", exc_info=True)

    @cleanup_old_tickets.before_loop
    async def before_cleanup_old_tickets(self):
        """Wait for bot to be ready before starting cleanup task"""
        await self.bot.wait_until_ready()

    async def cleanup_dm_support_tickets(self):
        """Clean up DM support tickets that are inactive for more than 24 hours"""
        try:
            collection = self.db.db['ryzuo-dm-support-tickets']
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)

            # Find tickets that are open but haven't had activity in 24 hours
            old_tickets = list(collection.find({
                "status": "open",
                "created_at": {"$lt": cutoff_time}
            }))

            closed_count = 0
            for ticket in old_tickets:
                try:
                    # Check if there are recent messages (activity)
                    messages = ticket.get('messages', [])
                    if messages:
                        # Check last message timestamp
                        last_message_time = max(msg.get('timestamp', datetime.min.replace(tzinfo=timezone.utc)) for msg in messages)
                        if last_message_time > cutoff_time:
                            continue  # Skip if there's recent activity

                    # Close the ticket
                    success = self.db.close_dm_support_ticket(str(ticket['_id']), None, "Auto-closed due to inactivity (24 hours)")
                    if success:
                        closed_count += 1
                        logger.info(f"Auto-closed DM support ticket {ticket['_id']} due to inactivity")

                        # Try to delete the channel if it exists
                        if ticket.get('channel_id'):
                            try:
                                channel = self.bot.get_channel(ticket['channel_id'])
                                if channel:
                                    await channel.delete(reason="Auto-closed due to inactivity")
                            except Exception as e:
                                logger.error(f"Error deleting channel for ticket {ticket['_id']}: {e}")

                except Exception as e:
                    logger.error(f"Error processing DM support ticket {ticket.get('_id', 'unknown')}: {e}")

            return closed_count

        except Exception as e:
            logger.error(f"Error in cleanup_dm_support_tickets: {e}")
            return 0



    async def cleanup_deleted_channel_tickets(self):
        """Check for tickets with deleted channels and auto-close them"""
        try:
            closed_count = 0

            # Check DM support tickets
            dm_collection = self.db.db['ryzuo-dm-support-tickets']
            open_dm_tickets = list(dm_collection.find({
                "status": "open",
                "channel_id": {"$ne": None}
            }))

            for ticket in open_dm_tickets:
                try:
                    channel_id = ticket.get('channel_id')
                    if channel_id:
                        channel = self.bot.get_channel(channel_id)
                        if not channel:
                            # Channel doesn't exist, close the ticket
                            success = self.db.close_dm_support_ticket(str(ticket['_id']), None, "Channel deleted")
                            if success:
                                closed_count += 1
                                logger.info(f"Auto-closed DM support ticket {ticket['_id']} due to deleted channel")
                except Exception as ticket_error:
                    logger.error(f"Error checking DM support ticket {ticket.get('_id', 'unknown')}: {ticket_error}")

            return closed_count

        except Exception as e:
            logger.error(f"Error in cleanup_deleted_channel_tickets: {e}")
            return 0


async def setup(bot):
    """Setup function for the cog"""
    await bot.add_cog(DMSupportSystem(bot))

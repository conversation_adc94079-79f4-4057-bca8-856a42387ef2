#!/usr/bin/env python3
"""
Fix script to remove the auto_roling_enabled: False from server config
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'website'))

from database import DatabaseManager
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_auto_roling_config():
    """Fix auto-roling configuration by removing the blocking field"""
    
    # Initialize database (you'll need to provide your MongoDB URL)
    mongo_url = "mongodb://localhost:27017"  # Update this with your actual MongoDB URL
    db = DatabaseManager(mongo_url)
    
    server_id = 1393129436974551050  # Your test server ID
    
    print("Fixing Auto-Roling Configuration")
    print("=" * 50)
    
    # Get current server config
    config = db.get_server_config(server_id)
    print(f"Current server config: {config}")
    
    # Remove the auto_roling_enabled field that's blocking the system
    try:
        collection = db.db['ryzuo-server-configs']
        result = collection.update_one(
            {"server_id": str(server_id)},
            {"$unset": {"auto_roling_enabled": ""}}
        )
        
        print(f"Removed auto_roling_enabled field: modified_count={result.modified_count}")
        
        # Verify the fix
        config_after = db.get_server_config(server_id)
        print(f"Server config after fix: {config_after}")
        
        # Check auto-roling settings
        auto_settings = db.get_auto_roling_settings(server_id)
        print(f"Auto-roling settings: {auto_settings}")
        
        print("\n" + "=" * 50)
        print("Fix completed! Auto-roling should now work.")
        
    except Exception as e:
        print(f"Error fixing configuration: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        fix_auto_roling_config()
    except Exception as e:
        print(f"Fix failed with error: {e}")
        import traceback
        traceback.print_exc()

import pymongo
import asyncio
import logging
from typing import Optional, List, Dict, Any, Tuple
from pymongo import MongoClient
from datetime import datetime, timedelta, timezone
from bson import ObjectId
try:
    import discord
except ImportError:
    discord = None

logger = logging.getLogger(__name__ + ".website_database")

def ensure_timezone_aware(dt):
    """Ensure a datetime object is timezone-aware (UTC if naive)"""
    if dt is None:
        return None
    if dt.tzinfo is None:
        return dt.replace(tzinfo=timezone.utc)
    return dt

class DatabaseManager:
    def __init__(self, mongo_url: str):
        self.mongo_url = mongo_url
        self.client = None
        self.db = None
        self._ensure_connected()
        
    # Server Configuration Methods
    def get_server_config(self, server_id: int) -> dict:
        """
        Get server configuration

        Args:
            server_id: Discord server ID

        Returns:
            dict: Server configuration or empty dict if not found
        """
        self._ensure_connected()
        collection = self.db['ryzuo-server-configs']
        # Try both string and integer server_id for backward compatibility
        result = collection.find_one({"server_id": str(server_id)})
        if not result:
            result = collection.find_one({"server_id": server_id})
        return result or {}
        
    def update_server_config(self, server_id: int, updates: dict) -> bool:
        """
        Update server configuration
        
        Args:
            server_id: Discord server ID
            updates: Dictionary of fields to update
            
        Returns:
            bool: True if update was successful
        """
        self._ensure_connected()
        collection = self.db['ryzuo-server-configs']
        
        # Add timestamp for the update
        updates['updated_at'] = datetime.now(timezone.utc)

        result = collection.update_one(
            {"server_id": str(server_id)},
            {"$set": updates, "$setOnInsert": {"created_at": datetime.now(timezone.utc)}},
            upsert=True
        )
        
        return result.modified_count > 0 or result.upserted_id is not None

    def update_server_config_field(self, server_id: int, field: str, value: any) -> bool:
        """
        Update a single field in server configuration

        Args:
            server_id: Discord server ID
            field: Field name to update
            value: New value for the field

        Returns:
            bool: True if successful, False otherwise
        """
        return self.update_server_config(server_id, {field: value})

    def is_system_enabled(self, server_id: int, system_name: str) -> bool:
        """
        Check if a specific system is enabled for a server

        Args:
            server_id: Discord server ID
            system_name: Name of the system (repping, vent, tempvoice, etc.)

        Returns:
            bool: True if system is enabled, False otherwise
        """
        try:
            config = self.get_server_config(server_id)
            if not config:
                return False

            # Check if the system is explicitly disabled
            enabled_field = f"{system_name}_enabled"
            if enabled_field in config:
                return config[enabled_field]

            # For backward compatibility, check if system has required configuration
            if system_name == "repping":
                return bool(config.get("repping_role_id") and config.get("repping_trigger_word"))
            elif system_name == "vent":
                return bool(config.get("vent_channel_id"))
            elif system_name == "tempvoice":
                # Check tempvoice settings in separate collection
                tempvoice_settings = self.get_tempvoice_settings(server_id)
                if not tempvoice_settings:
                    return False
                # Check if explicitly disabled
                if "enabled" in tempvoice_settings and not tempvoice_settings["enabled"]:
                    return False
                # Check if properly configured
                return bool(tempvoice_settings.get("interface_channel_id") and
                           tempvoice_settings.get("creator_channel_id"))
            elif system_name == "auto_roling":
                # Check auto-roling settings in separate collection
                auto_roling_settings = self.get_auto_roling_settings(server_id)
                if not auto_roling_settings:
                    return False
                # Check if explicitly disabled or has permission errors
                if not auto_roling_settings.get("enabled", False) or auto_roling_settings.get("permission_error", False):
                    return False
                # Check if properly configured
                return bool(auto_roling_settings.get("role_id"))
            elif system_name == "sticky_messages":
                # Check if there are any enabled sticky messages configured for this server
                sticky_messages = self.get_all_sticky_messages(server_id)
                enabled_sticky_messages = [msg for msg in sticky_messages if msg.get('enabled', True)]
                return len(enabled_sticky_messages) > 0
            elif system_name == "dm_support":
                # Check DM support settings in separate collection
                dm_support_settings = self.get_dm_support_settings(server_id)
                if not dm_support_settings:
                    return False
                # Check if explicitly disabled
                if "enabled" in dm_support_settings and not dm_support_settings["enabled"]:
                    return False
                # Check if properly configured
                return bool(dm_support_settings.get("category_id") and dm_support_settings.get("support_role_id"))
            elif system_name == "gender_verification":
                # Check gender verification settings in separate collection
                gender_verification_settings = self.get_gender_verification_settings(server_id)
                if not gender_verification_settings:
                    return False
                # Check if explicitly disabled
                if "enabled" in gender_verification_settings and not gender_verification_settings["enabled"]:
                    return False
                # Check if properly configured
                return bool(gender_verification_settings.get("channel_id") and gender_verification_settings.get("category_id"))
            elif system_name == "music":
                # Check music settings in separate collection
                music_settings = self.get_music_settings(server_id)
                if not music_settings:
                    return False
                # Check if explicitly disabled
                if "enabled" in music_settings and not music_settings["enabled"]:
                    return False
                # Music system only needs to be enabled, no specific configuration required
                return True

            return False
        except Exception as e:
            logger.error(f"Error checking if {system_name} is enabled for server {server_id}: {e}")
            return False

    def _ensure_connected(self):
        """Ensure we have an active database connection"""
        if self.client is None:
            self.connect()
            
    def connect(self):
        """Connect to MongoDB"""
        try:
            if self.client is not None:
                try:
                    # Test existing connection
                    self.client.admin.command('ping')
                    return  # Already connected
                except:
                    # Connection failed, will create a new one
                    self.disconnect()
                    
            self.client = MongoClient(self.mongo_url)
            self.db = self.client['ryzuo']  # Use the ryzuo database
            # Test connection
            self.client.admin.command('ping')
            logger.info("Successfully connected to MongoDB")
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            self.client = None
            self.db = None
            raise e
    
    def disconnect(self):
        """Disconnect from MongoDB"""
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")

    # Global Statistics Operations
    def save_global_statistics(self, stats: dict) -> bool:
        """Save aggregated global statistics to database"""
        try:
            self._ensure_connected()

            # Add timestamp if not present
            if 'last_updated' not in stats:
                stats['last_updated'] = datetime.now(timezone.utc)

            # Insert new statistics record
            result = self.db.global_statistics.insert_one(stats)

            # Keep only the last 100 records to prevent database bloat
            total_count = self.db.global_statistics.count_documents({})
            if total_count > 100:
                # Delete oldest records
                oldest_records = self.db.global_statistics.find().sort('last_updated', 1).limit(total_count - 100)
                oldest_ids = [record['_id'] for record in oldest_records]
                self.db.global_statistics.delete_many({'_id': {'$in': oldest_ids}})

            logger.info(f"Saved global statistics with ID: {result.inserted_id}")
            return True

        except Exception as e:
            logger.error(f"Error saving global statistics: {e}")
            return False

    def get_latest_global_statistics(self) -> Optional[dict]:
        """Get the most recent global statistics"""
        try:
            self._ensure_connected()

            # Get the most recent statistics
            result = self.db.global_statistics.find().sort('last_updated', -1).limit(1)
            stats = list(result)

            if stats:
                # Remove MongoDB _id field
                stats[0].pop('_id', None)
                return stats[0]
            else:
                logger.warning("No global statistics found in database")
                return None

        except Exception as e:
            logger.error(f"Error getting latest global statistics: {e}")
            return None

    def get_global_statistics_history(self, hours: int = 24) -> List[dict]:
        """Get global statistics history for the specified number of hours"""
        try:
            self._ensure_connected()

            # Calculate cutoff time
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)

            # Get statistics since cutoff time
            cursor = self.db.global_statistics.find({
                'last_updated': {'$gte': cutoff_time}
            }).sort('last_updated', 1)

            stats_list = []
            for stat in cursor:
                stat.pop('_id', None)  # Remove MongoDB _id field
                stats_list.append(stat)

            logger.info(f"Retrieved {len(stats_list)} statistics records from last {hours} hours")
            return stats_list

        except Exception as e:
            logger.error(f"Error getting global statistics history: {e}")
            return []
    
    # License Key Operations
    def add_license_keys(self, keys: List[str]) -> int:
        """Add multiple license keys to the database"""
        collection = self.db['ryzuo-license-keys']
        
        key_docs = []
        for key in keys:
            # Check if key already exists
            if not collection.find_one({"key": key}):
                key_docs.append({
                    "key": key,
                    "redeemed": False,
                    "redeemed_by": None,
                    "redeemed_at": None,
                    "server_id": None,
                    "created_at": datetime.now(timezone.utc)
                })
        
        if key_docs:
            result = collection.insert_many(key_docs)
            logger.info(f"Added {len(result.inserted_ids)} new license keys")
            return len(result.inserted_ids)
        return 0
    
    def redeem_license_key(self, key: str, user_id: int, server_id: int) -> bool:
        """Redeem a license key for a user and server"""
        collection = self.db['ryzuo-license-keys']

        # Find unredeemed key
        key_doc = collection.find_one({"key": key, "redeemed": False})
        if not key_doc:
            return False

        # Ensure user_id is stored as integer for consistency
        user_id_int = int(user_id)
        server_id_int = int(server_id)

        # Update key as redeemed
        result = collection.update_one(
            {"_id": key_doc["_id"]},
            {
                "$set": {
                    "redeemed": True,
                    "redeemed_by": user_id_int,
                    "redeemed_at": datetime.utcnow(),
                    "server_id": server_id_int
                }
            }
        )

        return result.modified_count > 0
    
    def get_user_license_keys(self, user_id: int, include_disabled: bool = False) -> List[Dict[str, Any]]:
        """Get all license keys owned by a user

        Args:
            user_id: The Discord user ID to get license keys for
            include_disabled: Whether to include disabled license keys (default: False)

        Returns:
            List of license key documents with server info and disabled status
        """
        try:
            # Convert user_id to both string and int for querying
            user_id_int = int(user_id)
            user_id_str = str(user_id)
            logger.info(f"Querying license keys for user_id: {user_id_int} (int) and {user_id_str} (str)")

            collection = self.db['ryzuo-license-keys']

            # Query for both string and integer user_id to handle type mismatches
            base_query = {
                "$or": [
                    {"redeemed_by": user_id_int},
                    {"redeemed_by": user_id_str}
                ],
                "redeemed": True
            }

            if not include_disabled:
                base_query["$and"] = [
                    {"$or": [
                        {"disabled": {"$exists": False}},
                        {"disabled": False}
                    ]}
                ]

            # Log the query for debugging
            logger.info(f"Running query: {base_query}")

            # Execute the query
            results = list(collection.find(base_query))
            logger.info(f"Found {len(results)} license keys for user {user_id}")

            # Log first few results for debugging
            if results:
                logger.info(f"Sample license key: {results[0]}")

            return results

        except Exception as e:
            logger.error(f"Error in get_user_license_keys: {str(e)}", exc_info=True)
            return []
    
    def get_server_license_key(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get the active license key for a server (only if not disabled)"""
        collection = self.db['ryzuo-license-keys']
        return collection.find_one({
            "server_id": server_id,
            "redeemed": True,
            "$or": [
                {"disabled": {"$exists": False}},
                {"disabled": False}
            ]
        })
    
    def transfer_key_to_server(self, key: str, user_id: int, new_server_id: int) -> bool:
        """Transfer a license key to a different server with complete cleanup of old server's data"""
        collection = self.db['ryzuo-license-keys']

        # First get the current server_id to clean up
        current_key = collection.find_one({"key": key, "redeemed_by": user_id})
        if not current_key:
            return False

        old_server_id = current_key.get('server_id')

        # Update the server_id
        result = collection.update_one(
            {"key": key, "redeemed_by": user_id},
            {"$set": {"server_id": new_server_id}}
        )

        # If transfer was successful and it's a different server, completely reset all license data
        if result.modified_count > 0 and old_server_id and old_server_id != new_server_id:
            self._cleanup_server_license_data(old_server_id)
            logger.info(f"Transferred key {key} from server {old_server_id} to {new_server_id}, completely reset old server data")

        return result.modified_count > 0
    
    def transfer_key_to_user(self, key: str, current_user_id: int, new_user_id: int) -> bool:
        """Transfer ownership of a license key to another user with complete cleanup"""
        collection = self.db['ryzuo-license-keys']

        # First get the server_id to clean up
        current_key = collection.find_one({"key": key, "redeemed_by": current_user_id})
        if not current_key:
            return False

        server_id = current_key.get('server_id')

        # Update the owner
        result = collection.update_one(
            {"key": key, "redeemed_by": current_user_id},
            {"$set": {"redeemed_by": new_user_id}}
        )

        # If transfer was successful, completely reset all license data since new owner needs to reconfigure
        if result.modified_count > 0 and server_id:
            self._cleanup_server_license_data(server_id)
            logger.info(f"Transferred key {key} from user {current_user_id} to {new_user_id} for server {server_id}, completely reset server data")

        return result.modified_count > 0

    def _cleanup_server_license_data(self, server_id: int) -> None:
        """Completely clean up all license-related data for a server"""
        try:
            # Clean up main server configuration
            config_collection = self.db['ryzuo-server-configs']
            config_collection.delete_many({"server_id": {"$in": [server_id, str(server_id)]}})

            # Clean up TempVoice settings and active channels
            tempvoice_settings_collection = self.db['ryzuo-tempvoice-settings']
            tempvoice_settings_collection.delete_many({"server_id": server_id})

            temp_channels_collection = self.db['ryzuo-temp-channels']
            temp_channels_collection.delete_many({"server_id": server_id})

            # Clean up Vent system settings
            vent_settings_collection = self.db['ryzuo-vent-settings']
            vent_settings_collection.delete_many({"server_id": server_id})

            # Clean up Sticky messages and logs
            sticky_messages_collection = self.db['ryzuo-sticky-messages']
            sticky_messages_collection.delete_many({"server_id": server_id})

            sticky_logs_collection = self.db['ryzuo-sticky-logs']
            sticky_logs_collection.delete_many({"server_id": server_id})

            # Clean up Giveaways
            giveaways_collection = self.db['ryzuo-giveaways']
            giveaways_collection.delete_many({"server_id": server_id})

            # Clean up DM Support settings and tickets
            dm_support_settings_collection = self.db['ryzuo-dm-support-settings']
            dm_support_settings_collection.delete_many({"server_id": server_id})

            dm_support_tickets_collection = self.db['ryzuo-dm-support-tickets']
            dm_support_tickets_collection.delete_many({"server_id": server_id})

            # Clean up Gender Verification settings and tickets
            gender_verification_collection = self.db['ryzuo-gender-verification']
            gender_verification_collection.delete_many({"server_id": server_id})

            gender_tickets_collection = self.db['ryzuo-gender-verification-tickets']
            gender_tickets_collection.delete_many({"server_id": server_id})

            # Clean up any dashboard logs for this server
            dashboard_logs_collection = self.db['ryzuo-dashboard-logs']
            dashboard_logs_collection.delete_many({"server_id": server_id})

            logger.info(f"Completely cleaned up all license data for server {server_id}")

        except Exception as e:
            logger.error(f"Error cleaning up server license data for server {server_id}: {e}")

    # Server Configuration Operations
    def save_server_config(self, server_id: int, config: Dict[str, Any]) -> bool:
        """Save server configuration"""
        try:
            collection = self.db['ryzuo-server-configs']

            # Ensure server_id is stored as string for consistency
            server_id_str = str(server_id)
            logger.info(f"Saving config for server {server_id_str}: {config}")

            result = collection.update_one(
                {"server_id": server_id_str},
                {"$set": {**config, "updated_at": datetime.utcnow()}},
                upsert=True
            )

            success = result.upserted_id is not None or result.modified_count > 0
            logger.info(f"Save result - upserted_id: {result.upserted_id}, modified_count: {result.modified_count}, success: {success}")

            # Verify the save by reading it back
            saved_config = collection.find_one({"server_id": server_id_str})
            logger.info(f"Verification - saved config: {saved_config}")

            return success
        except Exception as e:
            logger.error(f"Error saving server config: {e}", exc_info=True)
            return False
    
    def get_server_config_alt(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get server configuration (alternative method - deprecated, use get_server_config instead)"""
        collection = self.db['ryzuo-server-configs']
        # Try both string and integer server_id for backward compatibility
        result = collection.find_one({"server_id": str(server_id)})
        if not result:
            result = collection.find_one({"server_id": server_id})
        return result
    
    def update_server_config_field(self, server_id: int, field: str, value: Any) -> bool:
        """Update a specific field in server configuration"""
        collection = self.db['ryzuo-server-configs']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {field: value, "updated_at": datetime.utcnow()}},
            upsert=True
        )
        
        return result.upserted_id is not None or result.modified_count > 0
    
    def add_ignored_user(self, server_id: int, user_id: int) -> bool:
        """Add a user to the ignored list for a server"""
        collection = self.db['ryzuo-server-configs']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$addToSet": {"ignored_users": user_id}},
            upsert=True
        )
        
        return result.upserted_id is not None or result.modified_count > 0
    
    def remove_ignored_user(self, server_id: int, user_id: int) -> bool:
        """Remove a user from the ignored list for a server"""
        collection = self.db['ryzuo-server-configs']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$pull": {"ignored_users": user_id}}
        )
        
        return result.modified_count > 0
    
    def is_server_licensed(self, server_id: int) -> bool:
        """Check if a server has a valid license (DEPRECATED - use subscription system)"""
        license_key = self.get_server_license_key(server_id)
        return license_key is not None and not license_key.get('disabled', False)

    def is_server_premium_for_user(self, server_id: int, user_id: int) -> bool:
        """Check if server has premium access for a specific user (subscription + ownership)"""
        # Check if user has active subscription
        if not self.is_user_subscribed(user_id):
            return False

        # Note: Server ownership verification should be done at the application level
        # This method assumes ownership has been verified
        return True

    def get_all_licensed_servers(self) -> List[int]:
        """Get all server IDs that have valid licenses"""
        collection = self.db['ryzuo-license-keys']

        # Find all redeemed, non-disabled license keys with server_id
        licensed_keys = collection.find({
            "redeemed": True,
            "server_id": {"$exists": True, "$ne": None},
            "$or": [
                {"disabled": {"$exists": False}},
                {"disabled": False}
            ]
        }, {"server_id": 1})

        # Extract unique server IDs
        server_ids = list(set(key['server_id'] for key in licensed_keys))
        return server_ids

    def get_all_subscription_servers(self) -> List[int]:
        """Get all server IDs from users with active subscriptions"""
        try:
            subscription_collection = self.db['ryzuo-subscriptions']

            # Find all active subscriptions
            active_subscriptions = subscription_collection.find({
                "status": "active"
            }, {"user_id": 1})

            server_ids = set()

            # For each user with active subscription, get their owned servers
            for subscription in active_subscriptions:
                user_id = subscription.get('user_id')
                if user_id:
                    try:
                        # Get user's guilds from session or Discord API
                        # Note: This is a simplified approach - in production you might want to cache this
                        # For now, we'll get servers from the user_servers session data or guild cache
                        pass  # Implementation depends on how you want to handle this
                    except Exception as e:
                        logger.warning(f"Error getting servers for user {user_id}: {e}")
                        continue

            return list(server_ids)

        except Exception as e:
            logger.error(f"Error getting subscription servers: {e}")
            return []

    def disable_license_key(self, key: str) -> bool:
        """Disable a license key and all its features"""
        collection = self.db['ryzuo-license-keys']

        result = collection.update_one(
            {"key": key},
            {"$set": {"disabled": True, "disabled_at": datetime.utcnow()}}
        )

        return result.modified_count > 0

    def enable_license_key(self, key: str) -> bool:
        """Enable a license key"""
        collection = self.db['ryzuo-license-keys']

        result = collection.update_one(
            {"key": key},
            {"$set": {"disabled": False}, "$unset": {"disabled_at": ""}}
        )

        return result.modified_count > 0

    def get_license_key_by_key(self, key: str) -> Optional[Dict[str, Any]]:
        """Get license key information by key string"""
        collection = self.db['ryzuo-license-keys']
        return collection.find_one({"key": key})

    def get_server_license_key_full(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get the full license key information for a server"""
        collection = self.db['ryzuo-license-keys']
        return collection.find_one({"server_id": server_id, "redeemed": True})

    # ========== STRIPE SUBSCRIPTION OPERATIONS ==========

    def create_subscription(self, user_id: int, stripe_customer_id: str, stripe_subscription_id: str,
                          subscription_tier: str, status: str = "active") -> bool:
        """Create a new subscription record"""
        collection = self.db['ryzuo-subscriptions']

        subscription_doc = {
            "user_id": int(user_id),
            "stripe_customer_id": stripe_customer_id,
            "stripe_subscription_id": stripe_subscription_id,
            "subscription_tier": subscription_tier,  # weekly, monthly, yearly
            "status": status,  # active, cancelled, past_due, unpaid
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "cancelled_at": None,
            "disabled_by_admin": False
        }

        try:
            result = collection.insert_one(subscription_doc)
            logger.info(f"Created subscription for user {user_id}: {stripe_subscription_id}")
            return bool(result.inserted_id)
        except Exception as e:
            logger.error(f"Error creating subscription: {e}")
            return False

    def update_subscription_status(self, stripe_subscription_id: str, status: str,
                                 cancelled_at: Optional[datetime] = None) -> bool:
        """Update subscription status"""
        collection = self.db['ryzuo-subscriptions']

        update_data = {
            "status": status,
            "updated_at": datetime.now(timezone.utc)
        }

        if cancelled_at:
            update_data["cancelled_at"] = cancelled_at
        elif status == "cancelled":
            update_data["cancelled_at"] = datetime.now(timezone.utc)

        try:
            result = collection.update_one(
                {"stripe_subscription_id": stripe_subscription_id},
                {"$set": update_data}
            )
            logger.info(f"Updated subscription {stripe_subscription_id} status to {status}")
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating subscription status: {e}")
            return False

    def get_user_subscription(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get active subscription for a user"""
        collection = self.db['ryzuo-subscriptions']
        return collection.find_one({
            "user_id": int(user_id),
            "status": {"$in": ["active", "past_due"]},
            "disabled_by_admin": False
        })

    def get_subscription_by_stripe_id(self, stripe_subscription_id: str) -> Optional[Dict[str, Any]]:
        """Get subscription by Stripe subscription ID"""
        collection = self.db['ryzuo-subscriptions']
        return collection.find_one({"stripe_subscription_id": stripe_subscription_id})

    def disable_subscription_admin(self, stripe_subscription_id: str) -> bool:
        """Disable subscription by admin action"""
        collection = self.db['ryzuo-subscriptions']

        try:
            result = collection.update_one(
                {"stripe_subscription_id": stripe_subscription_id},
                {
                    "$set": {
                        "disabled_by_admin": True,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"Admin disabled subscription: {stripe_subscription_id}")
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error disabling subscription: {e}")
            return False

    def enable_subscription_admin(self, stripe_subscription_id: str) -> bool:
        """Enable subscription by admin action"""
        collection = self.db['ryzuo-subscriptions']

        try:
            result = collection.update_one(
                {"stripe_subscription_id": stripe_subscription_id},
                {
                    "$set": {
                        "disabled_by_admin": False,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"Admin enabled subscription: {stripe_subscription_id}")
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error enabling subscription: {e}")
            return False

    def is_user_subscribed(self, user_id: int) -> bool:
        """Check if user has an active subscription"""
        subscription = self.get_user_subscription(user_id)
        return subscription is not None

    def is_server_premium(self, server_id: int, user_id: int) -> bool:
        """Check if server has premium access (user must own server and have subscription)"""
        # First check if user has active subscription
        if not self.is_user_subscribed(user_id):
            return False

        # Server ownership will be validated at the application level
        # This method assumes ownership has already been verified
        return True
    
    def is_server_configured(self, server_id: int) -> tuple[bool, List[str]]:
        """Check if server is properly configured"""
        config = self.get_server_config(server_id)
        if not config:
            return False, ["No configuration found"]
        
        required_fields = ['role_id', 'channel_id', 'trigger_word']
        missing_fields = []
        
        for field in required_fields:
            if field not in config or config[field] is None:
                missing_fields.append(field)
        
        return len(missing_fields) == 0, missing_fields
        
    def get_shard_status(self) -> List[Dict[str, Any]]:
        """Get the status of all shards"""
        # This is a placeholder. In a real application, you would get the status from a process manager or a health check endpoint.
        return []

    def block_ip(self, ip: str) -> bool:
        """Block an IP address from accessing the dashboard"""
        collection = self.db['ryzuo-blocked-ips']
        result = collection.update_one({"ip_address": ip}, {"$set": {"ip_address": ip}}, upsert=True)
        return result.modified_count > 0 or result.upserted_id is not None

    def unblock_ip(self, ip: str) -> bool:
        """Unblock an IP address"""
        collection = self.db['ryzuo-blocked-ips']
        result = collection.delete_one({"ip_address": ip})
        return result.deleted_count > 0

    def get_blocked_ips(self) -> List[str]:
        """Get a list of all blocked IP addresses"""
        collection = self.db['ryzuo-blocked-ips']
        return [doc['ip_address'] for doc in collection.find()]

    def get_features(self) -> List[Dict[str, Any]]:
        """Get a list of all features and their premium status"""
        collection = self.db['ryzuo-features']
        return list(collection.find())

    def set_premium_features(self, premium_features: List[str]) -> bool:
        """Set which features are premium"""
        collection = self.db['ryzuo-features']
        collection.update_many({}, {"$set": {"is_premium": False}})
        collection.update_many({"name": {"$in": premium_features}}, {"$set": {"is_premium": True}})
        return True

    def set_premium_subfeatures(self, feature_name: str, premium_subfeatures: List[str]) -> bool:
        """Set which sub-features within a feature are premium"""
        collection = self.db['ryzuo-features']
        result = collection.update_one(
            {"name": feature_name},
            {"$set": {"premium_subfeatures": premium_subfeatures}},
            upsert=True
        )
        return result.modified_count > 0 or result.upserted_id is not None

    def get_feature_config(self, feature_name: str) -> Dict[str, Any]:
        """Get detailed configuration for a specific feature"""
        collection = self.db['ryzuo-features']
        feature = collection.find_one({"name": feature_name})
        if feature:
            return {
                'name': feature['name'],
                'is_premium': feature.get('is_premium', False),
                'premium_subfeatures': feature.get('premium_subfeatures', []),
                'description': feature.get('description', ''),
                'enabled': feature.get('enabled', True)
            }
        return {
            'name': feature_name,
            'is_premium': False,
            'premium_subfeatures': [],
            'description': '',
            'enabled': True
        }

    def get_prices(self) -> Dict[str, str]:
        """Get the prices for premium subscriptions"""
        collection = self.db['ryzuo-prices']
        doc = collection.find_one()
        if doc:
            return doc['prices']
        return {}

    def set_prices(self, prices: Dict[str, str]) -> bool:
        """Set the prices for premium subscriptions"""
        collection = self.db['ryzuo-prices']
        collection.update_one({}, {"$set": {"prices": prices}}, upsert=True)
        return True

    def get_all_servers(self, search_query=None) -> List[Dict[str, Any]]:
        """Get all servers from the database"""
        collection = self.db['ryzuo-server-configs']
        query = {}
        if search_query:
            query = {
                "$or": [
                    {"server_name": {"$regex": search_query, "$options": "i"}},
                    {"server_id": search_query}
                ]
            }
        return list(collection.find(query))

    def delete_server(self, server_id: int) -> bool:
        """Delete a server and all its data"""
        self._cleanup_server_license_data(server_id)
        return True

    def get_all_users(self, search_query=None) -> List[Dict[str, Any]]:
        """Get all users from the database"""
        collection = self.db['ryzuo-users']
        query = {}
        if search_query:
            query = {
                "$or": [
                    {"username": {"$regex": search_query, "$options": "i"}},
                    {"user_id": search_query}
                ]
            }
        return list(collection.find(query))

    def add_premium_to_user(self, user_id: int, duration: str) -> bool:
        """Add premium to a user"""
        collection = self.db['ryzuo-users']
        
        # Calculate expiry date
        if duration == 'weekly':
            expiry_date = datetime.now(timezone.utc) + timedelta(weeks=1)
        elif duration == 'monthly':
            expiry_date = datetime.now(timezone.utc) + timedelta(days=30)
        elif duration == 'yearly':
            expiry_date = datetime.now(timezone.utc) + timedelta(days=365)
        elif duration == 'lifetime':
            expiry_date = None
        else:
            return False

        result = collection.update_one(
            {"user_id": user_id},
            {"$set": {"is_premium": True, "premium_expiry": expiry_date}},
            upsert=True
        )
        return result.modified_count > 0 or result.upserted_id is not None

    def enable_user(self, user_id: int) -> bool:
        """Enable a user account"""
        collection = self.db['ryzuo-users']
        result = collection.update_one(
            {"user_id": user_id},
            {"$set": {"is_disabled": False}}
        )
        return result.modified_count > 0

    def disable_user(self, user_id: int) -> bool:
        """Disable a user account"""
        collection = self.db['ryzuo-users']
        result = collection.update_one(
            {"user_id": user_id},
            {"$set": {"is_disabled": True}}
        )
        return result.modified_count > 0

    def delete_user(self, user_id: int) -> bool:
        """Delete a user and all their data"""
        # Delete from users collection
        users_collection = self.db['ryzuo-users']
        users_collection.delete_one({"user_id": user_id})

        # Delete user notifications
        notifications_collection = self.db['ryzuo-user-notifications']
        notifications_collection.delete_many({"user_id": str(user_id)})

        # Delete owned servers and related data
        servers_collection = self.db['ryzuo-server-configs']
        owned_servers = servers_collection.find({"owner_id": user_id})
        for server in owned_servers:
            self._cleanup_server_license_data(server['server_id'])
        
        return True

    # ========== USER MANAGEMENT METHODS ==========
    
    def create_or_update_user(self, user_id: int, username: str, avatar: str = None, email: str = None) -> bool:
        """Create or update user account in database"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-users']
            
            user_data = {
                "user_id": str(user_id),
                "username": username,
                "last_login": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }
            
            if avatar:
                user_data["avatar"] = avatar
            if email:
                user_data["email"] = email
            
            result = collection.update_one(
                {"user_id": str(user_id)},
                {
                    "$set": user_data,
                    "$setOnInsert": {
                        "created_at": datetime.now(timezone.utc),
                        "is_premium": False,
                        "is_disabled": False
                    }
                },
                upsert=True
            )
            
            logger.info(f"User {user_id} ({username}) created/updated in database")
            return result.upserted_id is not None or result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error creating/updating user {user_id}: {e}")
            return False
    
    def get_user(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user data from database"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-users']
            
            # Try both string and int user_id for compatibility
            result = collection.find_one({"user_id": str(user_id)})
            if not result:
                result = collection.find_one({"user_id": user_id})
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting user {user_id}: {e}")
            return None

    # ========== NOTIFICATION METHODS ==========
    
    def create_notification(self, user_id: int, title: str, message: str, notification_type: str = 'info') -> bool:
        """Create a notification for a specific user"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-user-notifications']
            
            notification = {
                "user_id": str(user_id),
                "title": title,
                "message": message,
                "type": notification_type,
                "read": False,
                "created_at": datetime.now(timezone.utc)
            }
            
            logger.info(f"Creating notification for user {user_id}: {title}")
            result = collection.insert_one(notification)
            success = bool(result.inserted_id)
            
            if success:
                logger.info(f"Successfully created notification {result.inserted_id} for user {user_id}")
            else:
                logger.error(f"Failed to create notification for user {user_id}")
            
            return success
        except Exception as e:
            logger.error(f"Error creating notification: {e}", exc_info=True)
            return False
    
    def get_user_notifications(self, user_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """Get notifications for a specific user"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-user-notifications']
            
            logger.info(f"Fetching notifications for user {user_id}")
            
            notifications = list(collection.find(
                {"user_id": str(user_id)},
                sort=[("created_at", -1)],
                limit=limit
            ))
            
            # Convert ObjectId to string for JSON serialization
            for notif in notifications:
                notif['_id'] = str(notif['_id'])
            
            logger.info(f"Found {len(notifications)} notifications for user {user_id}")
            logger.debug(f"Notifications: {notifications}")
            
            return notifications
        except Exception as e:
            logger.error(f"Error getting user notifications: {e}", exc_info=True)
            return []
    
    def get_unread_notification_count(self, user_id: int) -> int:
        """Get count of unread notifications for a user"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-user-notifications']

            logger.info(f"Counting unread notifications for user {user_id}")

            count = collection.count_documents({
                "user_id": str(user_id),
                "read": False
            })

            logger.info(f"Found {count} unread notifications for user {user_id}")
            return count
        except Exception as e:
            logger.error(f"Error getting unread notification count: {e}", exc_info=True)
            return 0

    def mark_notification_read(self, notification_id: str, user_id: int) -> bool:
        """Mark a notification as read"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-user-notifications']

            result = collection.update_one(
                {
                    "_id": ObjectId(notification_id),
                    "user_id": str(user_id)
                },
                {"$set": {"read": True}}
            )

            logger.info(f"Marked notification {notification_id} as read for user {user_id}")
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error marking notification as read: {e}", exc_info=True)
            return False

    def mark_all_notifications_read(self, user_id: int) -> bool:
        """Mark all notifications as read for a user"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-user-notifications']

            result = collection.update_many(
                {
                    "user_id": str(user_id),
                    "read": False
                },
                {"$set": {"read": True}}
            )

            logger.info(f"Marked {result.modified_count} notifications as read for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error marking all notifications as read: {e}", exc_info=True)
            return False

    def dismiss_notification(self, notification_id: str, user_id: int) -> bool:
        """Delete a single notification"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-user-notifications']

            result = collection.delete_one({
                "_id": ObjectId(notification_id),
                "user_id": str(user_id)
            })

            logger.info(f"Dismissed notification {notification_id} for user {user_id}")
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error dismissing notification: {e}", exc_info=True)
            return False

    def clear_all_notifications(self, user_id: int) -> bool:
        """Clear all notifications for a user"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-user-notifications']

            result = collection.delete_many({
                "user_id": str(user_id)
            })

            logger.info(f"Cleared all notifications for user {user_id}, count: {result.deleted_count}")
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error clearing notifications: {e}", exc_info=True)
            return False
    
    def dismiss_notification(self, notification_id: str, user_id: int) -> bool:
        """Delete a single notification"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-user-notifications']
            
            result = collection.delete_one({
                "_id": ObjectId(notification_id),
                "user_id": str(user_id)
            })
            
            logger.info(f"Dismissed notification {notification_id} for user {user_id}")
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error dismissing notification: {e}")
            return False
    
    def clear_all_notifications(self, user_id: int) -> bool:
        """Clear all notifications for a user"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-user-notifications']
            
            result = collection.delete_many({
                "user_id": str(user_id)
            })
            
            logger.info(f"Cleared all notifications for user {user_id}, count: {result.deleted_count}")
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error clearing notifications: {e}")
            return False
    
    def _cleanup_user_notifications(self, user_id: int) -> None:
        """Keep only the last 5 notifications for a user"""
        try:
            collection = self.db['ryzuo-user-notifications']
            
            # Get all notifications for user, sorted by creation date (newest first)
            notifications = list(collection.find(
                {"user_id": str(user_id)}
            ).sort("created_at", -1))
            
            # If more than 5 notifications, delete the oldest ones
            if len(notifications) > 5:
                notifications_to_delete = notifications[5:]  # Keep first 5, delete rest
                notification_ids = [notif['_id'] for notif in notifications_to_delete]
                
                collection.delete_many({
                    "_id": {"$in": notification_ids}
                })
                
                logger.debug(f"Cleaned up {len(notification_ids)} old notifications for user {user_id}")
                
        except Exception as e:
            logger.error(f"Error cleaning up notifications for user {user_id}: {e}")

    # Gender Verification Methods
    def set_gender_verification_settings(self, server_id: int, channel_id: int, category_id: int,
                                       support_role_id: int, paper_text: str) -> bool:
        """Save gender verification settings for a server"""
        collection = self.db['ryzuo-gender-verification']

        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "channel_id": channel_id,
                "category_id": category_id,
                "support_role_id": support_role_id,
                "paper_text": paper_text,
                "enabled": True,  # Enable by default when configuring
                "updated_at": datetime.now(timezone.utc)
            }},
            upsert=True
        )

        return result.upserted_id is not None or result.modified_count > 0
    
    def get_gender_verification_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get gender verification settings for a server"""
        collection = self.db['ryzuo-gender-verification']
        # Try both string and int server_id for compatibility
        result = collection.find_one({"server_id": str(server_id)})
        if not result:
            result = collection.find_one({"server_id": server_id})
        return result

    def update_gender_verification_settings_enabled(self, server_id: int, enabled: bool) -> bool:
        """Update gender verification system enabled status"""
        collection = self.db['ryzuo-gender-verification']

        # Try both string and int server_id for compatibility
        result = collection.update_one(
            {"server_id": str(server_id)},
            {"$set": {
                "enabled": enabled,
                "updated_at": datetime.now(timezone.utc)
            }}
        )

        if result.modified_count == 0:
            result = collection.update_one(
                {"server_id": server_id},
                {"$set": {
                    "enabled": enabled,
                    "updated_at": datetime.now(timezone.utc)
                }}
            )

        return result.modified_count > 0
    
    def create_gender_verification_ticket(self, server_id: int, user_id: int) -> str:
        """Create a new gender verification ticket with atomic duplicate prevention"""
        collection = self.db['ryzuo-gender-tickets']

        try:
            # Use atomic findOneAndUpdate to prevent race conditions
            # First check if user already has an open ticket
            existing_ticket = collection.find_one({
                "server_id": server_id,
                "user_id": user_id,
                "status": "open"
            })

            if existing_ticket:
                return "existing"

            # Check if user had a ticket closed in the last 12 hours
            recent_ticket = collection.find_one({
                "server_id": server_id,
                "user_id": user_id,
                "status": "closed",
                "closed_at": {"$gt": datetime.now(timezone.utc) - timedelta(hours=12)}
            })

            if recent_ticket:
                return "recent"

            # Create new ticket atomically
            ticket = {
                "server_id": server_id,
                "user_id": user_id,
                "channel_id": None,
                "status": "open",
                "created_at": datetime.now(timezone.utc),
                "closed_at": None,
                "created_by": "user_request"  # Add identifier to track creation source
            }

            # Double-check for existing ticket right before insertion
            existing_check = collection.find_one({
                "server_id": server_id,
                "user_id": user_id,
                "status": "open"
            })

            if existing_check:
                return "existing"

            result = collection.insert_one(ticket)
            return str(result.inserted_id)

        except Exception as e:
            logger.error(f"Error creating gender verification ticket: {e}")
            return "error"
    
    def close_gender_verification_ticket(self, ticket_id: str, channel_id: int) -> bool:
        """Close a gender verification ticket"""
        collection = self.db['ryzuo-gender-tickets']
        
        result = collection.update_one(
            {"_id": ObjectId(ticket_id)},
            {"$set": {
                "status": "closed",
                "closed_at": datetime.utcnow(),
                "channel_id": channel_id
            }}
        )
        
        return result.modified_count > 0
    
    def get_user_open_ticket(self, server_id: int, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user's open gender verification ticket"""
        collection = self.db['ryzuo-gender-tickets']
        return collection.find_one({
            "server_id": server_id,
            "user_id": user_id,
            "status": "open"
        })
    
    def cleanup_old_tickets(self):
        """Clean up tickets that have been inactive for more than 24 hours - DISABLED for gender verification tickets"""
        try:
            # Check if database is initialized by trying to access a collection
            try:
                collection = self.db.get_collection('ryzuo-gender-tickets')
            except Exception as e:
                logger.error(f"Database error: {e}")
                return 0

            # DISABLED: Gender verification tickets should NEVER automatically close
            # This function is kept for potential future use with other ticket types
            # but gender verification tickets will only close manually

            logger.debug("Gender verification ticket auto-cleanup is disabled - tickets will only close manually")
            return 0

        except Exception as e:
            logger.error(f"Error in cleanup_old_tickets: {e}", exc_info=True)
            return 0

    # Music System Methods
    def set_music_settings(self, server_id: int, dj_role_id: Optional[int] = None, enabled: bool = True) -> bool:
        """Save music system settings for a server"""
        try:
            collection = self.db['ryzuo-music-settings']

            # Ensure server_id is stored as string for consistency
            server_id_str = str(server_id)
            logger.info(f"Saving music settings for server {server_id_str}")

            settings = {
                "enabled": enabled,
                "updated_at": datetime.utcnow()
            }

            if dj_role_id is not None:
                settings["dj_role_id"] = dj_role_id

            result = collection.update_one(
                {"server_id": server_id_str},
                {"$set": settings},
                upsert=True
            )

            success = result.upserted_id is not None or result.modified_count > 0
            logger.info(f"Music settings save result: {success}")
            return success
        except Exception as e:
            logger.error(f"Error saving music settings: {e}", exc_info=True)
            return False

    def get_music_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get music system settings for a server"""
        try:
            collection = self.db['ryzuo-music-settings']
            # Try both string and int server_id for compatibility
            result = collection.find_one({"server_id": str(server_id)})
            if not result:
                result = collection.find_one({"server_id": server_id})
            return result
        except Exception as e:
            logger.error(f"Error getting music settings: {e}")
            return None

    def update_music_settings_enabled(self, server_id: int, enabled: bool) -> bool:
        """Update music system enabled status"""
        try:
            collection = self.db['ryzuo-music-settings']

            # Ensure server_id is stored as string for consistency
            server_id_str = str(server_id)

            result = collection.update_one(
                {"server_id": server_id_str},
                {"$set": {
                    "enabled": enabled,
                    "updated_at": datetime.utcnow()
                }},
                upsert=True
            )

            return result.upserted_id is not None or result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating music settings enabled status: {e}")
            return False

    def update_music_dj_role(self, server_id: int, dj_role_id: Optional[int]) -> bool:
        """Update music system DJ role"""
        try:
            collection = self.db['ryzuo-music-settings']

            # Ensure server_id is stored as string for consistency
            server_id_str = str(server_id)

            if dj_role_id is not None:
                # Set the DJ role
                result = collection.update_one(
                    {"server_id": server_id_str},
                    {"$set": {
                        "dj_role_id": dj_role_id,
                        "updated_at": datetime.utcnow()
                    }},
                    upsert=True
                )
            else:
                # Remove the DJ role
                result = collection.update_one(
                    {"server_id": server_id_str},
                    {
                        "$unset": {"dj_role_id": ""},
                        "$set": {"updated_at": datetime.utcnow()}
                    },
                    upsert=True
                )

            return result.upserted_id is not None or result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating music DJ role: {e}")
            return False

    # TempVoice Methods
    def set_tempvoice_settings(self, server_id: int, interface_channel_id: int, creator_channel_id: int, default_user_limit: Optional[int] = None) -> bool:
        """Save TempVoice settings for a server"""
        try:
            collection = self.db['ryzuo-tempvoice-settings']

            # Ensure server_id is stored as string for consistency
            server_id_str = str(server_id)
            logger.info(f"Saving tempvoice settings for server {server_id_str}")

            result = collection.update_one(
                {"server_id": server_id_str},
                {"$set": {
                    "interface_channel_id": interface_channel_id,
                    "creator_channel_id": creator_channel_id,
                    "default_user_limit": default_user_limit,
                    "enabled": True,  # Enable the system when configured
                    "updated_at": datetime.utcnow()
                }},
                upsert=True
            )

            success = result.upserted_id is not None or result.modified_count > 0
            logger.info(f"Tempvoice settings save result: {success}")
            return success
        except Exception as e:
            logger.error(f"Error saving tempvoice settings: {e}", exc_info=True)
            return False

    def get_tempvoice_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get TempVoice settings for a server"""
        collection = self.db['ryzuo-tempvoice-settings']
        # Try both string and int formats for backward compatibility
        result = collection.find_one({"server_id": str(server_id)})
        if not result:
            result = collection.find_one({"server_id": server_id})
        return result

    def update_tempvoice_settings_enabled(self, server_id: int, enabled: bool) -> bool:
        """Update TempVoice system enabled status"""
        collection = self.db['ryzuo-tempvoice-settings']

        # Use string format for consistency
        result = collection.update_one(
            {"server_id": str(server_id)},
            {"$set": {
                "enabled": enabled,
                "updated_at": datetime.now(timezone.utc)
            }}
        )

        return result.modified_count > 0

    def create_temp_channel(self, server_id: int, user_id: int, channel_id: int) -> bool:
        """Create a temporary voice channel record"""
        collection = self.db['ryzuo-temp-channels']

        # Check if user already has a channel
        existing = collection.find_one({
            "server_id": server_id,
            "owner_id": user_id,
            "active": True
        })

        if existing:
            return False

        channel_doc = {
            "server_id": server_id,
            "channel_id": channel_id,
            "owner_id": user_id,
            "active": True,
            "user_limit": None,
            "blocked_users": [],
            "locked": False,
            "created_at": datetime.utcnow()
        }

        result = collection.insert_one(channel_doc)
        return result.inserted_id is not None

    def get_temp_channel(self, channel_id: int) -> Optional[Dict[str, Any]]:
        """Get temporary channel data"""
        collection = self.db['ryzuo-temp-channels']
        return collection.find_one({"channel_id": channel_id, "active": True})

    def get_user_temp_channel(self, server_id: int, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user's active temporary channel"""
        collection = self.db['ryzuo-temp-channels']
        return collection.find_one({
            "server_id": server_id,
            "owner_id": user_id,
            "active": True
        })

    def delete_temp_channel(self, channel_id: int) -> bool:
        """Mark temporary channel as inactive"""
        collection = self.db['ryzuo-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id},
            {"$set": {"active": False, "deleted_at": datetime.utcnow()}}
        )

        return result.modified_count > 0

    def update_temp_channel_owner(self, channel_id: int, new_owner_id: int) -> bool:
        """Transfer ownership of a temporary channel"""
        collection = self.db['ryzuo-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id, "active": True},
            {"$set": {"owner_id": new_owner_id}}
        )

        return result.modified_count > 0

    def set_temp_channel_limit(self, channel_id: int, user_limit: Optional[int]) -> bool:
        """Set user limit for temporary channel"""
        collection = self.db['ryzuo-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id, "active": True},
            {"$set": {"user_limit": user_limit}}
        )

        return result.modified_count > 0

    def block_user_from_temp_channel(self, channel_id: int, user_id: int) -> bool:
        """Block a user from a temporary channel"""
        collection = self.db['ryzuo-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id, "active": True},
            {"$addToSet": {"blocked_users": user_id}}
        )

        return result.modified_count > 0

    def unblock_user_from_temp_channel(self, channel_id: int, user_id: int) -> bool:
        """Unblock a user from a temporary channel"""
        collection = self.db['ryzuo-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id, "active": True},
            {"$pull": {"blocked_users": user_id}}
        )

        return result.modified_count > 0

    def set_temp_channel_lock(self, channel_id: int, locked: bool) -> bool:
        """Lock or unlock a temporary channel"""
        collection = self.db['ryzuo-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id, "active": True},
            {"$set": {"locked": locked}}
        )

        return result.modified_count > 0

    def get_all_active_temp_channels(self) -> List[Dict[str, Any]]:
        """Get all active temporary channels across all servers"""
        collection = self.db['ryzuo-temp-channels']
        return list(collection.find({"active": True}))

    # Vent System Methods
    def set_vent_settings(self, server_id: int, vent_channel_id: int) -> bool:
        """Save vent system settings for a server"""
        try:
            collection = self.db['ryzuo-vent-settings']

            # Ensure server_id is stored as string for consistency
            server_id_str = str(server_id)
            logger.info(f"Saving vent settings for server {server_id_str}, channel {vent_channel_id}")

            result = collection.update_one(
                {"server_id": server_id_str},
                {"$set": {
                    "vent_channel_id": vent_channel_id,
                    "updated_at": datetime.utcnow()
                }},
                upsert=True
            )

            success = result.upserted_id is not None or result.modified_count > 0
            logger.info(f"Vent settings save result - upserted_id: {result.upserted_id}, modified_count: {result.modified_count}, success: {success}")

            # Verify the save by reading it back
            saved_settings = collection.find_one({"server_id": server_id_str})
            logger.info(f"Verification - saved vent settings: {saved_settings}")

            return success
        except Exception as e:
            logger.error(f"Error saving vent settings: {e}", exc_info=True)
            return False

    def get_vent_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get vent system settings for a server"""
        collection = self.db['ryzuo-vent-settings']
        return collection.find_one({"server_id": server_id})

    def update_vent_settings_enabled(self, server_id: int, enabled: bool) -> bool:
        """Update vent system enabled status"""
        collection = self.db['ryzuo-vent-settings']

        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "enabled": enabled,
                "updated_at": datetime.now(timezone.utc)
            }}
        )

        return result.modified_count > 0

    def log_vent_message(self, server_id: int, user_id: int, username: str, message: str) -> bool:
        """Log a vent message for moderation purposes"""
        collection = self.db['ryzuo-vent-logs']

        log_doc = {
            "server_id": server_id,
            "user_id": user_id,
            "username": username,
            "message": message,
            "timestamp": datetime.utcnow()
        }

        result = collection.insert_one(log_doc)
        return result.inserted_id is not None

    # Sticky Message Methods
    def set_sticky_message(self, server_id: int, channel_id: int, content: str, created_by: int) -> bool:
        """Set or update a sticky message for a channel"""
        try:
            collection = self.db['ryzuo-sticky-messages']

            # Ensure server_id is stored as string for consistency
            server_id_str = str(server_id)
            logger.info(f"Setting sticky message for server {server_id_str}, channel {channel_id}")

            result = collection.update_one(
                {"server_id": server_id_str, "channel_id": channel_id},
                {"$set": {
                    "content": content,
                    "created_by": created_by,
                    "message_id": None,  # Will be set when message is posted
                    "enabled": True,  # Enable by default when creating/updating
                    "updated_at": datetime.now(timezone.utc)
                }},
                upsert=True
            )

            success = result.upserted_id is not None or result.modified_count > 0
            logger.info(f"Sticky message save result: {success}")
            return success
        except Exception as e:
            logger.error(f"Error setting sticky message: {e}", exc_info=True)
            return False

    def get_sticky_message(self, server_id: int, channel_id: int) -> Optional[Dict[str, Any]]:
        """Get sticky message settings for a specific channel"""
        collection = self.db['ryzuo-sticky-messages']
        # Try both string and int server_id for compatibility
        result = collection.find_one({"server_id": str(server_id), "channel_id": channel_id})
        if not result:
            result = collection.find_one({"server_id": server_id, "channel_id": channel_id})
        return result

    def get_all_sticky_messages(self, server_id: int) -> List[Dict[str, Any]]:
        """Get all sticky messages for a server"""
        collection = self.db['ryzuo-sticky-messages']
        # Try both string and int server_id for compatibility
        results = list(collection.find({"server_id": str(server_id)}))
        if not results:
            results = list(collection.find({"server_id": server_id}))
        return results

    def update_sticky_messages_enabled(self, server_id: int, enabled: bool) -> bool:
        """Update all sticky messages enabled status for a server"""
        collection = self.db['ryzuo-sticky-messages']

        # Try both string and int server_id for compatibility
        result = collection.update_many(
            {"server_id": str(server_id)},
            {"$set": {
                "enabled": enabled,
                "updated_at": datetime.now(timezone.utc)
            }}
        )

        if result.modified_count == 0:
            result = collection.update_many(
                {"server_id": server_id},
                {"$set": {
                    "enabled": enabled,
                    "updated_at": datetime.now(timezone.utc)
                }}
            )

        return result.modified_count > 0

    def update_sticky_message_enabled(self, server_id: int, channel_id: int, enabled: bool) -> bool:
        """Update individual sticky message enabled status"""
        collection = self.db['ryzuo-sticky-messages']

        # Try both string and int server_id for compatibility
        result = collection.update_one(
            {"server_id": str(server_id), "channel_id": channel_id},
            {"$set": {
                "enabled": enabled,
                "updated_at": datetime.now(timezone.utc)
            }}
        )

        if result.modified_count == 0:
            result = collection.update_one(
                {"server_id": server_id, "channel_id": channel_id},
                {"$set": {
                    "enabled": enabled,
                    "updated_at": datetime.now(timezone.utc)
                }}
            )

        return result.modified_count > 0

    def update_sticky_message_id(self, server_id: int, channel_id: int, message_id: int = None) -> bool:
        """Update the message ID for a sticky message (None to clear)"""
        collection = self.db['ryzuo-sticky-messages']

        # Try both string and int server_id for compatibility
        result = collection.update_one(
            {"server_id": str(server_id), "channel_id": channel_id},
            {"$set": {"message_id": message_id, "updated_at": datetime.now(timezone.utc)}}
        )

        if result.modified_count == 0:
            result = collection.update_one(
                {"server_id": server_id, "channel_id": channel_id},
                {"$set": {"message_id": message_id, "updated_at": datetime.now(timezone.utc)}}
            )

        logger.debug(f"Updated sticky message ID for server {server_id}, channel {channel_id}: {message_id} (success: {result.modified_count > 0})")
        return result.modified_count > 0

    def create_sticky_message(self, server_id: int, channel_id: int, content: str) -> bool:
        """Alias for set_sticky_message for web dashboard compatibility"""
        return self.set_sticky_message(server_id, channel_id, content, 0)  # Use 0 for web dashboard user

    def remove_sticky_message(self, server_id: int, channel_id: int) -> bool:
        """Remove a sticky message from a channel"""
        try:
            collection = self.db['ryzuo-sticky-messages']

            # Ensure server_id is stored as string for consistency
            server_id_str = str(server_id)
            logger.info(f"Removing sticky message for server {server_id_str}, channel {channel_id}")

            result = collection.delete_one({"server_id": server_id_str, "channel_id": channel_id})
            success = result.deleted_count > 0
            logger.info(f"Sticky message removal result: {success}")
            return success
        except Exception as e:
            logger.error(f"Error removing sticky message: {e}", exc_info=True)
            return False

    def log_sticky_activity(self, server_id: int, channel_id: int, user_id: int, username: str, action: str, content: str = None) -> bool:
        """Log sticky message activity for moderation purposes"""
        collection = self.db['ryzuo-sticky-logs']

        log_doc = {
            "server_id": server_id,
            "channel_id": channel_id,
            "user_id": user_id,
            "username": username,
            "action": action,  # 'created', 'removed', 'reposted'
            "content": content,
            "timestamp": datetime.now(timezone.utc)
        }

        result = collection.insert_one(log_doc)
        return result.inserted_id is not None

    # Comprehensive Logging System
    def log_bot_activity(self, server_id: int, user_id: int, username: str, action: str,
                        details: str = None, category: str = "general", channel_id: int = None) -> bool:
        """Log all bot activity for dashboard viewing (max 100 logs per server)"""

        # Map categories to log types for the new logging system
        category_to_log_type = {
            "config": "dashboard_updates",
            "license": "license_key_updates",
            "moderation": "moderator_logs",
            "general": "ryzuo_logs",
            "vent": "vent_logs",
            "dm_support": "ticket_logs",
            "sticky": "sticky_created",  # Default sticky category
            "sticky_deleted": "sticky_deleted",  # Specific sticky deletion category
            "giveaway": "ryzuo_logs",
            "repping": "ryzuo_logs",  # Add repping category
            "message_sent": "ryzuo_logs",  # Add message categories
            "message_deleted": "ryzuo_logs",
            "tempvoice": "ryzuo_logs",  # Add tempvoice category
            "gender_verification": "ryzuo_logs"  # Add gender verification
        }

        log_type = category_to_log_type.get(category, "ryzuo_logs")

        # Check if dashboard logging is enabled globally and for this log type
        logging_config = self.get_logging_config(server_id)

        if not logging_config.get('dashboard_enabled', True):
            return False

        # Check if this specific log type is enabled for dashboard
        log_types = logging_config.get('log_types', {})
        if log_type in log_types:
            log_config = log_types[log_type]
            if not (log_config.get('enabled', False) and log_config.get('dashboard', True)):
                return False
        else:
            # For dashboard_updates, allow by default if not configured
            if log_type != "dashboard_updates":
                return False

        collection = self.db['ryzuo-bot-logs']

        log_doc = {
            "server_id": server_id,
            "user_id": user_id,
            "username": username,
            "action": action,
            "details": details,
            "category": category,  # 'repping', 'vent', 'tempvoice', 'sticky', 'dm_support', 'gender_verification', 'config', 'general'
            "log_type": log_type,  # Add the mapped log type
            "channel_id": channel_id,
            "timestamp": datetime.now(timezone.utc)
        }

        # Insert the new log
        result = collection.insert_one(log_doc)

        if result.inserted_id:
            # Clean up old logs - keep only the most recent 100 logs per server
            self._cleanup_old_logs(server_id)
            return True

        return False

    def log_bot_activity_with_discord(self, server_id: int, user_id: int, username: str, action: str,
                                    details: str = None, category: str = "general", channel_id: int = None) -> bool:
        """Log bot activity and also queue it for Discord logging if enabled"""
        try:
            # Map category to log type for Discord logging
            category_to_log_type = {
                "config": "dashboard_updates",
                "license": "license_key_updates",
                "moderation": "moderator_logs",
                "general": "ryzuo_logs",
                "vent": "vent_logs",
                "dm_support": "ticket_logs",
                "sticky": "sticky_created",
                "sticky_deleted": "sticky_deleted",
                "giveaway": "ryzuo_logs",
                "repping": "ryzuo_logs",
                "message_sent": "ryzuo_logs",
                "message_deleted": "ryzuo_logs",
                "tempvoice": "ryzuo_logs",
                "gender_verification": "ryzuo_logs"
            }

            log_type = category_to_log_type.get(category, "ryzuo_logs")

            # Check if Discord logging is enabled for this log type
            if self.is_log_type_enabled(server_id, log_type, "discord"):
                # Store in a special collection for Discord processing
                collection = self.db['ryzuo-discord-queue']

                queue_doc = {
                    "server_id": server_id,
                    "log_type": log_type,
                    "user_id": user_id,
                    "username": username,
                    "action": action,
                    "details": details,
                    "channel_id": channel_id,
                    "category": category,
                    "timestamp": datetime.now(timezone.utc),
                    "processed": False
                }

                collection.insert_one(queue_doc)
                logger.info(f"[DEBUG] Queued Discord log for {log_type} on server {server_id}")

            return True

        except Exception as e:
            logger.error(f"Error queuing Discord bot activity log: {e}")
            return False

    def _cleanup_old_logs(self, server_id: int, max_logs: int = 100):
        """Clean up old logs, keeping only the most recent max_logs entries per server"""
        collection = self.db['ryzuo-bot-logs']

        try:
            # Count current logs for this server
            total_logs = collection.count_documents({"server_id": server_id})

            if total_logs > max_logs:
                # Find the oldest logs to delete
                logs_to_delete = total_logs - max_logs

                # Get the oldest logs
                oldest_logs = list(collection.find(
                    {"server_id": server_id}
                ).sort("timestamp", 1).limit(logs_to_delete))

                if oldest_logs:
                    # Delete the oldest logs
                    oldest_ids = [log["_id"] for log in oldest_logs]
                    collection.delete_many({"_id": {"$in": oldest_ids}})

                    logger.info(f"Cleaned up {len(oldest_ids)} old log entries for server {server_id}")

        except Exception as e:
            logger.error(f"Error cleaning up old logs for server {server_id}: {e}")

    def get_bot_logs(self, server_id: int, limit: int = 100, category: str = None,
                    start_date: datetime = None, end_date: datetime = None) -> List[Dict[str, Any]]:
        """Get bot activity logs for dashboard"""
        collection = self.db['ryzuo-bot-logs']

        query = {"server_id": server_id}

        if category:
            query["category"] = category

        if start_date or end_date:
            date_query = {}
            if start_date:
                date_query["$gte"] = start_date
            if end_date:
                date_query["$lte"] = end_date
            query["timestamp"] = date_query

        return list(collection.find(query).sort("timestamp", -1).limit(limit))

    # Comprehensive Logging Configuration System
    def get_logging_config(self, server_id: int) -> Dict[str, Any]:
        """Get logging configuration for a server"""
        self._ensure_connected()
        collection = self.db['ryzuo-logging-config']
        result = collection.find_one({"server_id": str(server_id)})

        # Return default configuration if none exists
        if not result:
            return self._get_default_logging_config()

        return result

    def _get_default_logging_config(self) -> Dict[str, Any]:
        """Get default logging configuration"""
        return {
            "log_channel_id": None,
            "dashboard_enabled": True,
            "discord_enabled": False,
            "log_types": {
                # Bot Activity Logs
                "dashboard_updates": {"enabled": True, "discord": False, "dashboard": True, "color": "#3498db"},
                "license_key_updates": {"enabled": False, "discord": False, "dashboard": True, "color": "#f39c12"},
                "moderator_logs": {"enabled": False, "discord": False, "dashboard": True, "color": "#e74c3c"},
                "ryzuo_logs": {"enabled": False, "discord": False, "dashboard": True, "color": "#9b59b6"},
                "vent_logs": {"enabled": False, "discord": False, "dashboard": True, "color": "#e91e63"},
                "ticket_logs": {"enabled": False, "discord": False, "dashboard": True, "color": "#00bcd4"},
                "sticky_created": {"enabled": False, "discord": False, "dashboard": True, "color": "#4caf50"},
                "sticky_deleted": {"enabled": False, "discord": False, "dashboard": True, "color": "#ff5722"},

                # Discord Server Events - Enable commonly used events by default
                "guild_update": {"enabled": True, "discord": False, "dashboard": True, "color": "#607d8b"},
                "channel_create": {"enabled": True, "discord": False, "dashboard": True, "color": "#4caf50"},
                "channel_update": {"enabled": True, "discord": False, "dashboard": True, "color": "#ff9800"},
                "channel_delete": {"enabled": True, "discord": False, "dashboard": True, "color": "#f44336"},
                "channel_overwrite_create": {"enabled": True, "discord": False, "dashboard": True, "color": "#8bc34a"},
                "channel_overwrite_update": {"enabled": True, "discord": False, "dashboard": True, "color": "#ffc107"},
                "channel_overwrite_delete": {"enabled": True, "discord": False, "dashboard": True, "color": "#e91e63"},
                "message_pin": {"enabled": True, "discord": False, "dashboard": True, "color": "#673ab7"},
                "message_unpin": {"enabled": True, "discord": False, "dashboard": True, "color": "#9c27b0"},
                "message_delete": {"enabled": True, "discord": False, "dashboard": True, "color": "#f44336"},
                "message_delete_bulk": {"enabled": True, "discord": False, "dashboard": True, "color": "#d32f2f"},
                "message_update": {"enabled": True, "discord": False, "dashboard": True, "color": "#ff5722"},
                "member_join": {"enabled": True, "discord": False, "dashboard": True, "color": "#4caf50"},
                "member_leave": {"enabled": True, "discord": False, "dashboard": True, "color": "#f44336"},
                "member_kick": {"enabled": False, "discord": False, "dashboard": True, "color": "#e91e63"},
                "member_prune": {"enabled": False, "discord": False, "dashboard": True, "color": "#ad1457"},
                "member_ban_add": {"enabled": True, "discord": False, "dashboard": True, "color": "#b71c1c"},
                "member_ban_remove": {"enabled": True, "discord": False, "dashboard": True, "color": "#388e3c"},
                "member_update": {"enabled": True, "discord": False, "dashboard": True, "color": "#1976d2"},
                "member_role_update": {"enabled": True, "discord": False, "dashboard": True, "color": "#7b1fa2"},
                "member_move": {"enabled": False, "discord": False, "dashboard": True, "color": "#00796b"},
                "member_disconnect": {"enabled": False, "discord": False, "dashboard": True, "color": "#455a64"},
                "bot_add": {"enabled": True, "discord": False, "dashboard": True, "color": "#2e7d32"},
                "role_create": {"enabled": True, "discord": False, "dashboard": True, "color": "#4caf50"},
                "role_update": {"enabled": True, "discord": False, "dashboard": True, "color": "#ff9800"},
                "role_delete": {"enabled": True, "discord": False, "dashboard": True, "color": "#f44336"},
                "invite_create": {"enabled": True, "discord": False, "dashboard": True, "color": "#8bc34a"},
                "invite_update": {"enabled": False, "discord": False, "dashboard": True, "color": "#ffc107"},
                "invite_delete": {"enabled": True, "discord": False, "dashboard": True, "color": "#e91e63"},
                "webhook_create": {"enabled": True, "discord": False, "dashboard": True, "color": "#00bcd4"},
                "webhook_update": {"enabled": True, "discord": False, "dashboard": True, "color": "#009688"},
                "webhook_delete": {"enabled": True, "discord": False, "dashboard": True, "color": "#795548"},
                "emoji_create": {"enabled": True, "discord": False, "dashboard": True, "color": "#ffeb3b"},
                "emoji_update": {"enabled": True, "discord": False, "dashboard": True, "color": "#ffc107"},
                "emoji_delete": {"enabled": True, "discord": False, "dashboard": True, "color": "#ff5722"},
                "sticker_create": {"enabled": True, "discord": False, "dashboard": True, "color": "#e1bee7"},
                "sticker_update": {"enabled": True, "discord": False, "dashboard": True, "color": "#ce93d8"},
                "sticker_delete": {"enabled": True, "discord": False, "dashboard": True, "color": "#ba68c8"},
                "scheduled_event_create": {"enabled": True, "discord": False, "dashboard": True, "color": "#3f51b5"},
                "scheduled_event_update": {"enabled": True, "discord": False, "dashboard": True, "color": "#5c6bc0"},
                "scheduled_event_delete": {"enabled": True, "discord": False, "dashboard": True, "color": "#7986cb"},
                "thread_create": {"enabled": True, "discord": False, "dashboard": True, "color": "#26a69a"},
                "thread_update": {"enabled": True, "discord": False, "dashboard": True, "color": "#4db6ac"},
                "thread_delete": {"enabled": True, "discord": False, "dashboard": True, "color": "#80cbc4"}
            }
        }

    def update_logging_config(self, server_id: int, config: Dict[str, Any]) -> bool:
        """Update logging configuration for a server"""
        self._ensure_connected()
        collection = self.db['ryzuo-logging-config']

        # Add timestamp for the update
        config['updated_at'] = datetime.now(timezone.utc)
        config['server_id'] = str(server_id)

        # Debug logging
        logger.info(f"[DEBUG] Updating logging config for server {server_id}")
        logger.info(f"[DEBUG] Config being saved: {config}")

        result = collection.update_one(
            {"server_id": str(server_id)},
            {"$set": config},
            upsert=True
        )

        success = result.modified_count > 0 or result.upserted_id is not None
        logger.info(f"[DEBUG] Update result: modified={result.modified_count}, upserted={result.upserted_id}, success={success}")

        # Verify the save by reading it back
        saved_config = collection.find_one({"server_id": str(server_id)})
        logger.info(f"[DEBUG] Verification - saved config: {saved_config}")

        return success

    def migrate_logging_config_to_new_defaults(self, server_id: int) -> bool:
        """Migrate existing logging configuration to enable new default events"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-logging-config']

            # Get current config
            current_config = collection.find_one({"server_id": str(server_id)})
            if not current_config:
                # No existing config, will use new defaults automatically
                return True

            # Get new defaults
            new_defaults = self._get_default_logging_config()

            # Update existing config with new enabled defaults, but preserve user customizations
            current_log_types = current_config.get('log_types', {})
            new_log_types = new_defaults['log_types']

            updated_log_types = {}
            for log_type, new_config in new_log_types.items():
                if log_type in current_log_types:
                    # Keep existing configuration but update enabled status if it was False and new default is True
                    existing_config = current_log_types[log_type]
                    updated_config = existing_config.copy()

                    # Only enable if it wasn't explicitly disabled by user and new default is enabled
                    if not existing_config.get('enabled', False) and new_config.get('enabled', False):
                        updated_config['enabled'] = True
                        logger.info(f"[MIGRATION] Enabling {log_type} for server {server_id}")

                    updated_log_types[log_type] = updated_config
                else:
                    # New log type, use new default
                    updated_log_types[log_type] = new_config
                    if new_config.get('enabled', False):
                        logger.info(f"[MIGRATION] Adding new enabled log type {log_type} for server {server_id}")

            # Update the configuration
            updated_config = current_config.copy()
            updated_config['log_types'] = updated_log_types
            updated_config['migrated_at'] = datetime.now(timezone.utc)

            result = collection.update_one(
                {"server_id": str(server_id)},
                {"$set": updated_config}
            )

            success = result.modified_count > 0
            if success:
                logger.info(f"[MIGRATION] Successfully migrated logging config for server {server_id}")

            return success

        except Exception as e:
            logger.error(f"Error migrating logging config for server {server_id}: {e}")
            return False

    def is_log_type_enabled(self, server_id: int, log_type: str, destination: str = "dashboard") -> bool:
        """Check if a specific log type is enabled for a server and destination"""
        config = self.get_logging_config(server_id)

        # Debug logging
        logger.info(f"[DEBUG] Checking if {log_type} is enabled for {destination} on server {server_id}")
        logger.info(f"[DEBUG] Full config: {config}")

        # Check global settings first
        if destination == "dashboard" and not config.get("dashboard_enabled", True):
            logger.info(f"[DEBUG] Dashboard globally disabled")
            return False
        elif destination == "discord" and not config.get("discord_enabled", False):
            logger.info(f"[DEBUG] Discord globally disabled: {config.get('discord_enabled', False)}")
            return False

        log_types = config.get("log_types", {})
        logger.info(f"[DEBUG] Available log types: {list(log_types.keys())}")

        if log_type not in log_types:
            logger.info(f"[DEBUG] Log type {log_type} not found in config")
            return False

        log_config = log_types[log_type]
        logger.info(f"[DEBUG] Log config for {log_type}: {log_config}")

        enabled = log_config.get("enabled", False)
        destination_enabled = log_config.get(destination, False)
        result = enabled and destination_enabled

        logger.info(f"[DEBUG] enabled={enabled}, {destination}_enabled={destination_enabled}, result={result}")
        return result

    def get_log_type_color(self, server_id: int, log_type: str) -> str:
        """Get the color for a specific log type"""
        config = self.get_logging_config(server_id)
        log_types = config.get("log_types", {})

        if log_type not in log_types:
            return "#6c757d"  # Default gray color

        return log_types[log_type].get("color", "#6c757d")

    def log_discord_event(self, server_id: int, event_type: str, user_id: int, username: str,
                         action: str, details: str = None, channel_id: int = None,
                         target_id: int = None, extra_data: Dict[str, Any] = None) -> bool:
        """Log Discord events with enhanced data structure"""
        # Check if this event type is enabled for dashboard logging
        if not self.is_log_type_enabled(server_id, event_type, "dashboard"):
            return False

        collection = self.db['ryzuo-discord-logs']

        log_doc = {
            "server_id": server_id,
            "event_type": event_type,
            "user_id": user_id,
            "username": username,
            "action": action,
            "details": details,
            "channel_id": channel_id,
            "target_id": target_id,
            "extra_data": extra_data or {},
            "timestamp": datetime.now(timezone.utc)
        }

        # Insert the new log
        result = collection.insert_one(log_doc)

        if result.inserted_id:
            # Clean up old logs - keep only the most recent 100 logs per server per event type
            self._cleanup_old_discord_logs(server_id, event_type)
            return True

        return False

    def _cleanup_old_discord_logs(self, server_id: int, event_type: str, max_logs: int = 100):
        """Clean up old Discord event logs, keeping only the most recent max_logs entries per server per event type"""
        collection = self.db['ryzuo-discord-logs']

        try:
            # Count current logs for this server and event type
            total_logs = collection.count_documents({"server_id": server_id, "event_type": event_type})

            if total_logs > max_logs:
                # Find the oldest logs to delete
                logs_to_delete = total_logs - max_logs

                # Get the oldest logs
                oldest_logs = list(collection.find(
                    {"server_id": server_id, "event_type": event_type}
                ).sort("timestamp", 1).limit(logs_to_delete))

                if oldest_logs:
                    # Delete the oldest logs
                    oldest_ids = [log["_id"] for log in oldest_logs]
                    collection.delete_many({"_id": {"$in": oldest_ids}})

                    logger.info(f"Cleaned up {len(oldest_ids)} old {event_type} log entries for server {server_id}")

        except Exception as e:
            logger.error(f"Error cleaning up old {event_type} logs for server {server_id}: {e}")

    def get_discord_logs(self, server_id: int, event_type: str = None, limit: int = 100,
                        start_date: datetime = None, end_date: datetime = None) -> List[Dict[str, Any]]:
        """Get Discord event logs for dashboard"""
        collection = self.db['ryzuo-discord-logs']

        query = {"server_id": server_id}

        if event_type:
            query["event_type"] = event_type

        if start_date or end_date:
            date_query = {}
            if start_date:
                date_query["$gte"] = start_date
            if end_date:
                date_query["$lte"] = end_date
            query["timestamp"] = date_query

        return list(collection.find(query).sort("timestamp", -1).limit(limit))

    def get_log_statistics(self, server_id: int, days: int = 7) -> Dict[str, Any]:
        """Get comprehensive log statistics for dashboard with multiple datasets"""
        collection = self.db['ryzuo-bot-logs']
        now = datetime.now(timezone.utc)

        # Handle "all time" stats (when days is 0 or very large)
        if days == 0 or days > 36500:  # 0 or more than 100 years = all time
            start_date = datetime(2020, 1, 1, tzinfo=timezone.utc)  # Bot creation era
            period_description = "all time"
            total_logs = collection.count_documents({"server_id": server_id})
        else:
            # For specific time period, filter by date
            start_date = now - timedelta(days=days)
            period_description = f"last {days} days"
            total_logs = collection.count_documents({
                "server_id": server_id,
                "timestamp": {"$gte": start_date}
            })

        # Get activity counts by category
        category_pipeline = [
            {"$match": {
                "server_id": server_id,
                "timestamp": {"$gte": start_date}
            }},
            {"$group": {
                "_id": "$category",
                "count": {"$sum": 1}
            }},
            {"$sort": {"count": -1}}
        ]

        category_stats_dict = {}
        category_stats_list = []
        for result in collection.aggregate(category_pipeline):
            if result['_id']:  # Skip null categories
                category_stats_dict[result['_id']] = result['count']
                category_stats_list.append({
                    '_id': result['_id'],
                    'count': result['count']
                })

        # Get daily activity for chart (overall)
        daily_pipeline = [
            {"$match": {
                "server_id": server_id,
                "timestamp": {"$gte": start_date}
            }},
            {"$group": {
                "_id": {
                    "$dateToString": {
                        "format": "%Y-%m-%d",
                        "date": "$timestamp"
                    }
                },
                "count": {"$sum": 1}
            }},
            {"$sort": {"_id": 1}}
        ]

        daily_activity = {}
        for result in collection.aggregate(daily_pipeline):
            daily_activity[result['_id']] = result['count']

        # Get specific activity metrics by date
        specific_metrics_pipeline = [
            {"$match": {
                "server_id": server_id,
                "timestamp": {"$gte": start_date}
            }},
            {"$group": {
                "_id": {
                    "date": {
                        "$dateToString": {
                            "format": "%Y-%m-%d",
                            "date": "$timestamp"
                        }
                    },
                    "metric_type": {
                        "$switch": {
                            "branches": [
                                {
                                    "case": {"$and": [
                                        {"$eq": ["$category", "repping"]},
                                        {"$regexMatch": {"input": "$action", "regex": "Role assigned"}}
                                    ]},
                                    "then": "users_repping"
                                },
                                {
                                    "case": {"$eq": ["$category", "tempvoice"]},
                                    "then": "voice_channels_created"
                                },
                                {
                                    "case": {"$or": [
                                        {"$eq": ["$category", "message_sent"]},
                                        {"$regexMatch": {"input": "$action", "regex": "^Message sent$"}},
                                        {"$regexMatch": {"input": "$action", "regex": "^Sent message$"}}
                                    ]},
                                    "then": "messages_sent"
                                },
                                {
                                    "case": {"$or": [
                                        {"$eq": ["$category", "message_deleted"]},
                                        {"$regexMatch": {"input": "$action", "regex": "^Message deleted$"}},
                                        {"$regexMatch": {"input": "$action", "regex": "^Deleted message$"}}
                                    ]},
                                    "then": "messages_deleted"
                                }
                            ],
                            "default": "other"
                        }
                    }
                },
                "count": {"$sum": 1}
            }},
            {"$match": {"_id.metric_type": {"$ne": "other"}}},
            {"$sort": {"_id.date": 1}}
        ]

        daily_by_category = {}
        for result in collection.aggregate(specific_metrics_pipeline):
            date = result['_id']['date']
            metric_type = result['_id']['metric_type']
            if date not in daily_by_category:
                daily_by_category[date] = {}
            daily_by_category[date][metric_type] = result['count']

        return {
            "total_logs": total_logs,
            "daily_activity": daily_activity,
            "daily_by_category": daily_by_category,
            "category_stats": category_stats_list,  # List format for template slicing
            "category_stats_dict": category_stats_dict,  # Dict format for other uses
            "period_days": days if days > 0 and days <= 36500 else 0,
            "period_description": period_description
        }

    def get_server_statistics(self, server_id: int, bot=None) -> Dict[str, Any]:
        """Get comprehensive server statistics for dashboard"""
        stats = {}

        # Initialize collections that will be used throughout the method
        logs_collection = self.db['ryzuo-bot-logs']

        # Discord server statistics (live from Discord API if bot is available)
        if bot:
            guild = bot.get_guild(server_id)
            if guild:
                # Basic server info
                stats['member_count'] = guild.member_count
                stats['channel_count'] = len(guild.channels)
                stats['premium_subscription_count'] = guild.premium_subscription_count or 0

                # Online member count (requires presence intent)
                try:
                    if discord:
                        online_count = len([member for member in guild.members if member.status != discord.Status.offline])
                        stats['online_count'] = online_count
                    else:
                        stats['online_count'] = 0
                except:
                    # Fallback if presence intent is not available
                    stats['online_count'] = 0

                # Server creation date
                stats['server_created'] = guild.created_at
                stats['server_name'] = guild.name
                stats['server_icon'] = str(guild.icon.url) if guild.icon else None
            else:
                # Guild not found or bot not in guild
                stats['member_count'] = 0
                stats['channel_count'] = 0
                stats['premium_subscription_count'] = 0
                stats['online_count'] = 0
                stats['server_created'] = None
                stats['server_name'] = 'Unknown Server'
                stats['server_icon'] = None
        else:
            # Bot not available - use placeholder values
            stats['member_count'] = 0
            stats['channel_count'] = 0
            stats['premium_subscription_count'] = 0
            stats['online_count'] = 0
            stats['server_created'] = None
            stats['server_name'] = 'Unknown Server'
            stats['server_icon'] = None

        # Repping statistics (live from Discord if bot is available)
        if bot:
            config = self.get_server_config(server_id)
            if config and config.get('role_id'):
                guild = bot.get_guild(server_id)
                if guild:
                    role = guild.get_role(config['role_id'])
                    if role:
                        # Count non-bot members who have the role
                        stats['repping_users'] = len([member for member in role.members if not member.bot])
                    else:
                        stats['repping_users'] = 0
                else:
                    stats['repping_users'] = 0
            else:
                stats['repping_users'] = 0
        else:
            # Fallback to log-based method
            config_collection = self.db['ryzuo-server-config']
            config = config_collection.find_one({"server_id": server_id})

            if config and config.get('role_id'):
                # Count users who currently have repping role assignments (live count)
                # Get users who have had role assignments more recently than removals
                pipeline = [
                    {"$match": {
                        "server_id": server_id,
                        "category": "repping",
                        "$or": [
                            {"action": {"$regex": "^Role assigned:"}},
                            {"action": {"$regex": "^Role removed:"}}
                        ]
                    }},
                    {"$sort": {"timestamp": -1}},
                    {"$group": {
                        "_id": "$user_id",
                        "latest_action": {"$first": "$action"}
                    }},
                    {"$match": {"latest_action": {"$regex": "^Role assigned:"}}},
                    {"$count": "active_reppers"}
                ]

                result = list(logs_collection.aggregate(pipeline))
                stats['repping_users'] = result[0]['active_reppers'] if result else 0
            else:
                stats['repping_users'] = 0

        # Temp voice statistics
        temp_channels_collection = self.db['ryzuo-temp-channels']
        active_temp_channels = temp_channels_collection.count_documents({
            "server_id": server_id,
            "active": True
        })
        stats['active_temp_channels'] = active_temp_channels

        # Total temp channels created (last 30 days)
        total_temp_channels = temp_channels_collection.count_documents({
            "server_id": server_id,
            "created_at": {"$gte": datetime.now(timezone.utc) - timedelta(days=30)}
        })
        stats['total_temp_channels_30d'] = total_temp_channels

        # Sticky messages (live count)
        sticky_collection = self.db['ryzuo-sticky-messages']
        active_sticky_messages = sticky_collection.count_documents({
            "server_id": server_id
        })
        stats['active_sticky_messages'] = active_sticky_messages

        # Gender verification tickets (last 30 days)
        gender_tickets_collection = self.db['ryzuo-gender-tickets']
        gender_tickets = gender_tickets_collection.count_documents({
            "server_id": server_id,
            "created_at": {"$gte": datetime.now(timezone.utc) - timedelta(days=30)}
        })
        stats['gender_tickets_30d'] = gender_tickets

        # DM Support tickets (last 30 days)
        dm_support_collection = self.db['ryzuo-dm-support-tickets']
        dm_support_tickets = dm_support_collection.count_documents({
            "server_id": server_id,
            "created_at": {"$gte": datetime.now(timezone.utc) - timedelta(days=30)}
        })
        stats['dm_support_tickets_30d'] = dm_support_tickets

        # Bot activity (live/all time)
        total_activity = logs_collection.count_documents({
            "server_id": server_id
        })
        stats['total_bot_activity'] = total_activity
        stats['total_actions'] = total_activity  # Alias for template compatibility

        # Active users (users who have interacted with bot in last 30 days)
        active_users_count = len(logs_collection.distinct("user_id", {
            "server_id": server_id,
            "timestamp": {"$gte": datetime.now(timezone.utc) - timedelta(days=30)}
        }))
        stats['active_users'] = active_users_count

        return stats

    def get_global_statistics(self, bot=None) -> Dict[str, Any]:
        """Get global statistics across all servers"""
        stats = {}

        # Initialize collections
        logs_collection = self.db['ryzuo-bot-logs']
        temp_channels_collection = self.db['ryzuo-temp-channels']

        # Total servers and members (from bot if available)
        if bot:
            licensed_servers = [guild for guild in bot.guilds if self.is_server_licensed(guild.id)]
            stats['total_servers'] = len(licensed_servers)
            stats['total_members'] = sum(guild.member_count for guild in licensed_servers)
        else:
            # Fallback to database count
            config_collection = self.db['ryzuo-server-configs']
            stats['total_servers'] = config_collection.count_documents({})
            stats['total_members'] = 0  # Can't get accurate count without bot

        # Total temp voice channels created
        stats['total_temp_channels'] = temp_channels_collection.count_documents({})

        # Total commands/actions executed
        stats['total_commands'] = logs_collection.count_documents({})

        # Active temp channels
        stats['active_temp_channels'] = temp_channels_collection.count_documents({"active": True})

        # Recent activity (last 30 days)
        from datetime import datetime, timedelta, timezone
        thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)
        stats['recent_activity'] = logs_collection.count_documents({
            "timestamp": {"$gte": thirty_days_ago}
        })

        return stats

    def get_top_repping_users(self, server_id: int, limit: int = 6, bot=None) -> List[Dict[str, Any]]:
        """Get top repping users by total hours spent repping"""
        try:
            logs_collection = self.db['ryzuo-bot-logs']

            # Get all role assignment and removal events for repping
            repping_events = list(logs_collection.find({
                "server_id": server_id,
                "category": "repping",
                "$or": [
                    {"action": {"$regex": "^Role assigned:"}},
                    {"action": {"$regex": "^Role removed:"}}
                ]
            }).sort([("user_id", 1), ("timestamp", 1)]))

            # Calculate total repping hours for each user
            user_hours = {}
            user_info = {}

            for event in repping_events:
                user_id = event['user_id']
                timestamp = event['timestamp']
                action = event['action']
                username = event.get('username', f'User {user_id}')

                # Ensure timestamp is timezone-aware
                timestamp = ensure_timezone_aware(timestamp)

                if user_id not in user_hours:
                    user_hours[user_id] = {'total_seconds': 0, 'current_start': None}
                    user_info[user_id] = {'username': username, 'last_activity': timestamp}

                # Ensure last_activity is timezone-aware for comparison
                user_info[user_id]['last_activity'] = ensure_timezone_aware(user_info[user_id]['last_activity'])

                user_info[user_id]['last_activity'] = max(user_info[user_id]['last_activity'], timestamp)

                if 'assigned' in action:
                    # Role assigned - start counting time
                    if user_hours[user_id]['current_start'] is None:
                        user_hours[user_id]['current_start'] = timestamp
                elif 'removed' in action:
                    # Role removed - stop counting time
                    if user_hours[user_id]['current_start'] is not None:
                        # Ensure current_start is timezone-aware
                        start_time = ensure_timezone_aware(user_hours[user_id]['current_start'])

                        duration = (timestamp - start_time).total_seconds()
                        user_hours[user_id]['total_seconds'] += duration
                        user_hours[user_id]['current_start'] = None

            # Handle users who still have the role (no removal event)
            current_time = datetime.now(timezone.utc)
            for user_id in user_hours:
                if user_hours[user_id]['current_start'] is not None:
                    # Ensure current_start is timezone-aware
                    start_time = ensure_timezone_aware(user_hours[user_id]['current_start'])

                    duration = (current_time - start_time).total_seconds()
                    user_hours[user_id]['total_seconds'] += duration

            # Convert to list and sort by total hours
            result = []
            for user_id, hours_data in user_hours.items():
                total_hours = hours_data['total_seconds'] / 3600  # Convert to hours
                if total_hours > 0:  # Only include users with actual repping time
                    result.append({
                        '_id': user_id,
                        'total_hours': round(total_hours, 1),
                        'username': user_info[user_id]['username'],
                        'last_activity': user_info[user_id]['last_activity'],
                        'display_name': user_info[user_id]['username'],
                        'avatar_url': None
                    })

            # Sort by total hours and limit results
            result.sort(key=lambda x: x['total_hours'], reverse=True)
            result = result[:limit]

            # If bot is available, get live Discord user info
            if bot:
                guild = bot.get_guild(server_id)
                if guild:
                    for user_data in result:
                        try:
                            member = guild.get_member(user_data['_id'])
                            if member:
                                user_data['display_name'] = member.display_name
                                user_data['avatar_url'] = str(member.avatar.url) if member.avatar else None
                        except:
                            pass  # Keep existing fallback values

            return result

        except Exception as e:
            logger.error(f"Error calculating top repping users for server {server_id}: {e}", exc_info=True)
            return []

    def get_recent_repping_activity(self, server_id: int, limit: int = 25) -> List[Dict[str, Any]]:
        """Get recent repping activity for the server"""
        logs_collection = self.db['ryzuo-bot-logs']

        # Get recent repping activities
        activities = list(logs_collection.find({
            "server_id": server_id,
            "category": "repping"
        }).sort("timestamp", -1).limit(limit))

        return activities

    def get_current_repping_users(self, server_id: int, bot=None) -> List[Dict[str, Any]]:
        """Get list of users who currently have the repping role assigned (live from Discord)"""
        if not bot:
            # Fallback to log-based method if bot is not available
            return self._get_repping_users_from_logs(server_id)

        # Get server config to find the role ID
        config = self.get_server_config(server_id)
        if not config or not config.get('role_id'):
            return []

        # Get the guild and role from Discord
        guild = bot.get_guild(server_id)
        if not guild:
            return []

        role = guild.get_role(config['role_id'])
        if not role:
            return []

        # Get all members who currently have the role
        current_users = []
        for member in role.members:
            if not member.bot:  # Exclude bots
                current_users.append({
                    'user_id': member.id,
                    'username': f"{member.name}#{member.discriminator}",
                    'display_name': member.display_name,
                    'assigned_at': None  # We don't track exact assignment time for live data
                })

        return current_users

    def _get_repping_users_from_logs(self, server_id: int) -> List[Dict[str, Any]]:
        """Fallback method to get repping users from logs"""
        logs_collection = self.db['ryzuo-bot-logs']

        # Get users who have had role assignments more recently than removals
        pipeline = [
            {"$match": {
                "server_id": server_id,
                "category": "repping",
                "$or": [
                    {"action": {"$regex": "^Role assigned:"}},
                    {"action": {"$regex": "^Role removed:"}}
                ]
            }},
            {"$sort": {"timestamp": -1}},
            {"$group": {
                "_id": "$user_id",
                "latest_action": {"$first": "$action"},
                "latest_username": {"$first": "$username"},
                "latest_timestamp": {"$first": "$timestamp"}
            }},
            {"$match": {"latest_action": {"$regex": "^Role assigned:"}}},
            {"$project": {
                "user_id": "$_id",
                "username": "$latest_username",
                "assigned_at": "$latest_timestamp"
            }},
            {"$sort": {"assigned_at": -1}}
        ]

        return list(logs_collection.aggregate(pipeline))

    def get_active_temp_channels_with_users(self, server_id: int) -> List[Dict[str, Any]]:
        """Get active temp voice channels with owner information"""
        collection = self.db['ryzuo-temp-channels']

        pipeline = [
            {"$match": {
                "server_id": server_id,
                "active": True
            }},
            {"$sort": {"created_at": -1}}
        ]

        channels = list(collection.aggregate(pipeline))

        # Get usernames from logs for each owner
        logs_collection = self.db['ryzuo-bot-logs']
        for channel in channels:
            # Try to get the most recent username for this user
            user_log = logs_collection.find_one(
                {"server_id": server_id, "user_id": channel['owner_id']},
                sort=[("timestamp", -1)]
            )
            channel['owner_username'] = user_log['username'] if user_log else f"User#{channel['owner_id']}"

        return channels

    # ========== GIVEAWAY METHODS ==========

    def create_giveaway(self, server_id: int, channel_id: int, host_user_id: int,
                       item: str, requirements: str, winners: int, end_time: datetime,
                       admin_winner_ids: list = None) -> str:
        """Create a new giveaway"""
        collection = self.db['ryzuo-giveaways']

        # Ensure end_time is timezone-aware (assume UTC if naive)
        if end_time.tzinfo is None:
            end_time = end_time.replace(tzinfo=timezone.utc)
            logger.info(f"Converted naive datetime to UTC: {end_time}")

        # Ensure created_at is timezone-aware
        created_at = datetime.now(timezone.utc)

        giveaway_data = {
            'server_id': server_id,
            'channel_id': channel_id,
            'host_user_id': host_user_id,
            'item': item,
            'requirements': requirements,
            'winners': winners,
            'end_time': end_time,
            'created_at': created_at,
            'message_id': None,  # Will be set when message is sent
            'entries': [],  # List of user IDs who entered
            'active': True,
            'ended': False,
            'winners_selected': []
        }

        # Add admin winner IDs if provided (for admin override)
        if admin_winner_ids:
            giveaway_data['admin_winner_ids'] = admin_winner_ids

        result = collection.insert_one(giveaway_data)
        return str(result.inserted_id)

    def update_giveaway_message_id(self, giveaway_id: str, message_id: int) -> bool:
        """Update the message ID for a giveaway"""
        collection = self.db['ryzuo-giveaways']

        result = collection.update_one(
            {"_id": ObjectId(giveaway_id)},
            {"$set": {"message_id": message_id}}
        )

        return result.modified_count > 0

    def enter_giveaway(self, giveaway_id: str, user_id: int) -> bool:
        """Enter a user into a giveaway"""
        self._ensure_connected()
        try:
            collection = self.db['ryzuo-giveaways']

            # Check if user is already entered
            giveaway = collection.find_one({"_id": ObjectId(giveaway_id)})
            if not giveaway or not giveaway.get('active'):
                logger.warning(f"Giveaway {giveaway_id} not found or not active")
                return False

            if user_id in giveaway.get('entries', []):
                logger.info(f"User {user_id} already in giveaway {giveaway_id}")
                return False  # Already entered

            result = collection.update_one(
                {"_id": ObjectId(giveaway_id)},
                {"$addToSet": {"entries": user_id}}
            )
            
            success = result.modified_count > 0
            if success:
                logger.info(f"User {user_id} entered giveaway {giveaway_id}")
            else:
                logger.warning(f"Failed to enter user {user_id} into giveaway {giveaway_id}")
                
            return success
        except Exception as e:
            logger.error(f"Error in enter_giveaway for user {user_id}, giveaway {giveaway_id}: {e}")
            return False

    def leave_giveaway(self, giveaway_id: str, user_id: int) -> bool:
        """Remove a user from a giveaway"""
        self._ensure_connected()
        try:
            collection = self.db['ryzuo-giveaways']
            result = collection.update_one(
                {"_id": ObjectId(giveaway_id)},
                {"$pull": {"entries": user_id}}
            )
            
            success = result.modified_count > 0
            if success:
                logger.info(f"User {user_id} left giveaway {giveaway_id}")
            else:
                logger.warning(f"Failed to remove user {user_id} from giveaway {giveaway_id}")
                
            return success
        except Exception as e:
            logger.error(f"Error in leave_giveaway for user {user_id}, giveaway {giveaway_id}: {e}")
            return False

    def get_giveaway(self, giveaway_id: str) -> Dict[str, Any]:
        """Get a giveaway by ID"""
        self._ensure_connected()
        try:
            collection = self.db['ryzuo-giveaways']
            giveaway = collection.find_one({"_id": ObjectId(giveaway_id)})
            if not giveaway:
                return None
                
            # Convert ObjectId to string
            giveaway['_id'] = str(giveaway['_id'])
            
            # Ensure datetime fields are timezone-aware
            if 'end_time' in giveaway and giveaway['end_time'] and giveaway['end_time'].tzinfo is None:
                giveaway['end_time'] = giveaway['end_time'].replace(tzinfo=timezone.utc)
                
            if 'created_at' in giveaway and giveaway['created_at'] and giveaway['created_at'].tzinfo is None:
                giveaway['created_at'] = giveaway['created_at'].replace(tzinfo=timezone.utc)
                
            return giveaway
            
        except Exception as e:
            logger.error(f"Error getting giveaway {giveaway_id}: {e}", exc_info=True)
            return None

    def get_giveaway_by_message(self, message_id: int) -> Dict[str, Any]:
        """Get a giveaway by message ID"""
        self._ensure_connected()
        try:
            collection = self.db['ryzuo-giveaways']
            giveaway = collection.find_one({"message_id": message_id})
            if not giveaway:
                return None
                
            # Convert ObjectId to string
            giveaway['_id'] = str(giveaway['_id'])
            
            # Ensure datetime fields are timezone-aware
            if 'end_time' in giveaway and giveaway['end_time'] and giveaway['end_time'].tzinfo is None:
                giveaway['end_time'] = giveaway['end_time'].replace(tzinfo=timezone.utc)
                
            if 'created_at' in giveaway and giveaway['created_at'] and giveaway['created_at'].tzinfo is None:
                giveaway['created_at'] = giveaway['created_at'].replace(tzinfo=timezone.utc)
                
            return giveaway
            
        except Exception as e:
            logger.error(f"Error getting giveaway by message {message_id}: {e}", exc_info=True)
            return None

    def get_active_giveaways(self, server_id: int = None) -> List[Dict[str, Any]]:
        """Get all active giveaways, optionally filtered by server"""
        self._ensure_connected()
        try:
            collection = self.db['ryzuo-giveaways']

            query = {"active": True, "ended": False}
            if server_id:
                query["server_id"] = server_id

            giveaways = list(collection.find(query))
            
            # Process each giveaway
            processed_giveaways = []
            for giveaway in giveaways:
                try:
                    # Convert ObjectId to string
                    giveaway['_id'] = str(giveaway['_id'])
                    
                    # Ensure datetime fields are timezone-aware
                    if 'end_time' in giveaway and giveaway['end_time'] and giveaway['end_time'].tzinfo is None:
                        giveaway['end_time'] = giveaway['end_time'].replace(tzinfo=timezone.utc)
                        
                    if 'created_at' in giveaway and giveaway['created_at'] and giveaway['created_at'].tzinfo is None:
                        giveaway['created_at'] = giveaway['created_at'].replace(tzinfo=timezone.utc)
                        
                    processed_giveaways.append(giveaway)
                    
                except Exception as e:
                    logger.error(f"Error processing giveaway {giveaway.get('_id')}: {e}", exc_info=True)
                    continue

            return processed_giveaways
            
        except Exception as e:
            logger.error(f"Error getting active giveaways: {e}", exc_info=True)
            return []

    def get_expired_giveaways(self) -> List[Dict[str, Any]]:
        """Get all giveaways that have expired but not ended"""
        self._ensure_connected()
        try:
            collection = self.db['ryzuo-giveaways']
            now = datetime.now(timezone.utc)

            # Find giveaways that are active, not ended, and have end_time <= now
            giveaways = list(collection.find({
                "active": True,
                "ended": False,
                "end_time": {"$lte": now}
            }))

            # Process each giveaway
            processed_giveaways = []
            for giveaway in giveaways:
                try:
                    # Convert ObjectId to string
                    giveaway['_id'] = str(giveaway['_id'])
                    
                    # Ensure datetime fields are timezone-aware
                    if 'end_time' in giveaway and giveaway['end_time'] and giveaway['end_time'].tzinfo is None:
                        giveaway['end_time'] = giveaway['end_time'].replace(tzinfo=timezone.utc)
                        
                    if 'created_at' in giveaway and giveaway['created_at'] and giveaway['created_at'].tzinfo is None:
                        giveaway['created_at'] = giveaway['created_at'].replace(tzinfo=timezone.utc)
                        
                    processed_giveaways.append(giveaway)
                    
                except Exception as e:
                    logger.error(f"Error processing expired giveaway {giveaway.get('_id')}: {e}", exc_info=True)
                    continue

            return processed_giveaways

        except Exception as e:
            logger.error(f"Error getting expired giveaways: {e}", exc_info=True)
            return []

    def delete_giveaway(self, giveaway_id: str) -> bool:
        """Delete a giveaway from the database"""
        self._ensure_connected()
        try:
            collection = self.db['ryzuo-giveaways']
            result = collection.delete_one({"_id": ObjectId(giveaway_id)})

            success = result.deleted_count > 0
            if success:
                logger.info(f"Deleted giveaway {giveaway_id}")
            else:
                logger.warning(f"Failed to delete giveaway {giveaway_id} - not found")

            return success

        except Exception as e:
            logger.error(f"Error deleting giveaway {giveaway_id}: {e}", exc_info=True)
            return False

    def update_giveaway_winners(self, giveaway_id: str, winners: list) -> bool:
        """Update the winners for a giveaway (for rerolling)"""
        self._ensure_connected()
        try:
            collection = self.db['ryzuo-giveaways']
            result = collection.update_one(
                {"_id": ObjectId(giveaway_id)},
                {"$set": {"winners_selected": winners}}
            )

            success = result.modified_count > 0
            if success:
                logger.info(f"Updated winners for giveaway {giveaway_id}: {winners}")
            else:
                logger.warning(f"Failed to update winners for giveaway {giveaway_id}")

            return success

        except Exception as e:
            logger.error(f"Error updating giveaway winners {giveaway_id}: {e}", exc_info=True)
            return False
            
    def end_giveaway(self, giveaway_id: str, winners: List[int]) -> bool:
        """End a giveaway and set winners"""
        self._ensure_connected()
        try:
            collection = self.db['ryzuo-giveaways']
            
            result = collection.update_one(
                {"_id": ObjectId(giveaway_id)},
                {"$set": {
                    "ended": True,
                    "active": False,
                    "winners_selected": winners,
                    "ended_at": datetime.now(timezone.utc)
                }}
            )
            
            success = result.modified_count > 0
            if success:
                logger.info(f"Ended giveaway {giveaway_id} with {len(winners)} winners")
            else:
                logger.warning(f"Failed to end giveaway {giveaway_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"Error ending giveaway {giveaway_id}: {e}", exc_info=True)
            return False

    def get_server_giveaways(self, server_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """Get giveaways for a server with proper timezone handling"""
        self._ensure_connected()
        try:
            collection = self.db['ryzuo-giveaways']
            
            # Get giveaways for the server, sorted by creation date (newest first)
            giveaways = list(collection.find(
                {"server_id": server_id}
            ).sort("created_at", -1).limit(limit))
            
            # Process each giveaway
            processed_giveaways = []
            for giveaway in giveaways:
                try:
                    # Convert ObjectId to string
                    giveaway['_id'] = str(giveaway['_id'])
                    
                    # Ensure datetime fields are timezone-aware
                    if 'end_time' in giveaway and giveaway['end_time'] and giveaway['end_time'].tzinfo is None:
                        giveaway['end_time'] = giveaway['end_time'].replace(tzinfo=timezone.utc)
                        
                    if 'created_at' in giveaway and giveaway['created_at'] and giveaway['created_at'].tzinfo is None:
                        giveaway['created_at'] = giveaway['created_at'].replace(tzinfo=timezone.utc)
                        
                    processed_giveaways.append(giveaway)
                    
                except Exception as e:
                    logger.error(f"Error processing giveaway {giveaway.get('_id')}: {e}", exc_info=True)
                    continue

            return processed_giveaways
            
        except Exception as e:
            logger.error(f"Error getting giveaways for server {server_id}: {e}", exc_info=True)
            return []

    def cleanup_all_server_logs(self, server_id: int, max_logs: int = 100) -> int:
        """Manually clean up logs for a server and return number of deleted logs"""
        collection = self.db['ryzuo-bot-logs']

        try:
            # Count current logs
            total_logs = collection.count_documents({"server_id": server_id})

            if total_logs <= max_logs:
                return 0

            # Calculate how many to delete
            logs_to_delete = total_logs - max_logs

            # Get the oldest logs
            oldest_logs = list(collection.find(
                {"server_id": server_id}
            ).sort("timestamp", 1).limit(logs_to_delete))

            if oldest_logs:
                # Delete the oldest logs
                oldest_ids = [log["_id"] for log in oldest_logs]
                result = collection.delete_many({"_id": {"$in": oldest_ids}})

                logger.info(f"Manual cleanup: Deleted {result.deleted_count} old log entries for server {server_id}")
                return result.deleted_count

            return 0

        except Exception as e:
            logger.error(f"Error in manual log cleanup for server {server_id}: {e}")
            return 0

    # Auto-Roling System Methods
    def get_auto_roling_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get auto-roling settings for a server"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-auto-roling']
            server_id_str = str(server_id)
            logger.info(f"Querying auto-roling settings for server_id: {server_id_str}")

            result = collection.find_one({"server_id": server_id_str})
            logger.info(f"Auto-roling query result: {result}")

            return result
        except Exception as e:
            logger.error(f"Error getting auto-roling settings for server {server_id}: {e}", exc_info=True)
            return None

    def set_auto_roling_settings(self, server_id: int, role_id: int, enabled: bool = True) -> bool:
        """Save auto-roling settings for a server"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-auto-roling']
            server_id_str = str(server_id)

            logger.info(f"Setting auto-roling settings: server_id={server_id_str}, role_id={role_id}, enabled={enabled}")

            result = collection.update_one(
                {"server_id": server_id_str},
                {"$set": {
                    "role_id": role_id,
                    "enabled": enabled,
                    "permission_error": False,
                    "last_error": None,
                    "updated_at": datetime.now(timezone.utc)
                }},
                upsert=True
            )

            logger.info(f"Database update result: modified_count={result.modified_count}, upserted_id={result.upserted_id}")

            # Verify the save by reading it back
            saved_settings = collection.find_one({"server_id": server_id_str})
            logger.info(f"Verification - saved settings: {saved_settings}")

            success = result.upserted_id is not None or result.modified_count > 0
            logger.info(f"Auto-roling settings saved for server {server_id_str}: role_id={role_id}, enabled={enabled}, success={success}")
            return success
        except Exception as e:
            logger.error(f"Error saving auto-roling settings for server {server_id}: {e}", exc_info=True)
            return False

    def update_auto_roling_status(self, server_id: int, enabled: bool) -> bool:
        """Update auto-roling enabled status"""
        try:
            collection = self.db['ryzuo-auto-roling']
            server_id_str = str(server_id)

            result = collection.update_one(
                {"server_id": server_id_str},
                {"$set": {
                    "enabled": enabled,
                    "updated_at": datetime.now(timezone.utc)
                }}
            )

            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating auto-roling status for server {server_id}: {e}")
            return False

    def set_auto_roling_error(self, server_id: int, error_message: str) -> bool:
        """Set auto-roling permission error status"""
        try:
            collection = self.db['ryzuo-auto-roling']
            server_id_str = str(server_id)

            result = collection.update_one(
                {"server_id": server_id_str},
                {"$set": {
                    "permission_error": True,
                    "last_error": error_message,
                    "enabled": False,  # Automatically disable when there's a permission error
                    "updated_at": datetime.now(timezone.utc)
                }}
            )

            logger.warning(f"Auto-roling disabled due to permission error in server {server_id_str}: {error_message}")
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error setting auto-roling error for server {server_id}: {e}")
            return False

    def clear_auto_roling_error(self, server_id: int) -> bool:
        """Clear auto-roling permission error status"""
        try:
            collection = self.db['ryzuo-auto-roling']
            server_id_str = str(server_id)

            result = collection.update_one(
                {"server_id": server_id_str},
                {"$set": {
                    "permission_error": False,
                    "last_error": None,
                    "updated_at": datetime.now(timezone.utc)
                }}
            )

            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error clearing auto-roling error for server {server_id}: {e}")
            return False

    # DM Support System Methods
    def set_dm_support_settings(self, server_id: int, category_id: int, support_role_id: int, logs_channel_id: int) -> bool:
        """Save DM support settings for a server"""
        try:
            collection = self.db['ryzuo-dm-support-settings']

            # Use integer server_id for consistency
            logger.info(f"Saving DM support settings for server {server_id}")

            result = collection.update_one(
                {"server_id": server_id},
                {"$set": {
                    "category_id": category_id,
                    "support_role_id": support_role_id,
                    "logs_channel_id": logs_channel_id,
                    "enabled": True,
                    "updated_at": datetime.now(timezone.utc)
                }},
                upsert=True
            )

            success = result.upserted_id is not None or result.modified_count > 0
            logger.info(f"DM support settings save result: {success}")
            return success
        except Exception as e:
            logger.error(f"Error saving DM support settings: {e}", exc_info=True)
            return False

    def get_dm_support_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get DM support settings for a server"""
        collection = self.db['ryzuo-dm-support-settings']
        # Try both string and int server_id for compatibility
        result = collection.find_one({"server_id": str(server_id)})
        if not result:
            result = collection.find_one({"server_id": server_id})
        return result

    def update_dm_support_settings_enabled(self, server_id: int, enabled: bool) -> bool:
        """Update DM support system enabled status"""
        collection = self.db['ryzuo-dm-support-settings']

        # Try both string and int server_id for compatibility
        result = collection.update_one(
            {"server_id": str(server_id)},
            {"$set": {
                "enabled": enabled,
                "updated_at": datetime.now(timezone.utc)
            }}
        )

        if result.modified_count == 0:
            result = collection.update_one(
                {"server_id": server_id},
                {"$set": {
                    "enabled": enabled,
                    "updated_at": datetime.now(timezone.utc)
                }}
            )

        return result.modified_count > 0

    def get_user_servers_with_dm_support(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all servers where user is a member and DM support is enabled"""
        collection = self.db['ryzuo-dm-support-settings']
        return list(collection.find({"enabled": True}))

    def create_dm_support_ticket(self, server_id: int, user_id: int, initial_message: str, guild_name: str = None) -> str:
        """Create a new DM support ticket"""
        collection = self.db['ryzuo-dm-support-tickets']

        # Check if user already has an open ticket for this server
        existing_ticket = collection.find_one({
            "server_id": server_id,
            "user_id": user_id,
            "status": "open"
        })

        if existing_ticket:
            return "existing"

        # Create new ticket
        ticket = {
            "server_id": server_id,
            "user_id": user_id,
            "channel_id": None,
            "status": "open",
            "initial_message": initial_message,
            "guild_name": guild_name or "Unknown Server",
            "messages": [],
            "created_at": datetime.now(timezone.utc),
            "closed_at": None,
            "closed_by": None,
            "close_reason": None
        }

        result = collection.insert_one(ticket)
        return str(result.inserted_id)

    def get_user_open_dm_ticket(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user's open DM support ticket (globally)"""
        collection = self.db['ryzuo-dm-support-tickets']
        return collection.find_one({
            "user_id": user_id,
            "status": "open"
        })

    def get_dm_ticket_by_channel(self, channel_id: int) -> Optional[Dict[str, Any]]:
        """Get DM support ticket by channel ID"""
        collection = self.db['ryzuo-dm-support-tickets']
        return collection.find_one({
            "channel_id": channel_id,
            "status": "open"
        })

    def update_dm_ticket_channel(self, ticket_id: str, channel_id: int) -> bool:
        """Update the channel ID for a DM support ticket"""
        collection = self.db['ryzuo-dm-support-tickets']

        result = collection.update_one(
            {"_id": ObjectId(ticket_id)},
            {"$set": {"channel_id": channel_id}}
        )

        return result.modified_count > 0

    def add_message_to_dm_ticket(self, ticket_id: str, user_id: int, username: str, message: str, is_staff: bool = False, attachment_urls: list = None) -> bool:
        """Add a message to a DM support ticket
        
        Args:
            ticket_id: The ID of the ticket
            user_id: The ID of the user who sent the message
            username: The username of the user who sent the message
            message: The message content
            is_staff: Whether the sender is a staff member
            attachment_urls: List of attachment URLs (default: None)
        """
        collection = self.db['ryzuo-dm-support-tickets']

        message_doc = {
            "user_id": user_id,
            "username": username,
            "message": message,
            "is_staff": is_staff,
            "timestamp": datetime.now(timezone.utc),
            "attachments": attachment_urls or []
        }

        result = collection.update_one(
            {"_id": ObjectId(ticket_id)},
            {
                "$push": {"messages": message_doc},
                "$set": {"updated_at": datetime.now(timezone.utc)}
            }
        )

        return result.modified_count > 0

    def close_dm_support_ticket(self, ticket_id: str, closed_by: int, reason: str) -> bool:
        """Close a DM support ticket"""
        collection = self.db['ryzuo-dm-support-tickets']

        result = collection.update_one(
            {"_id": ObjectId(ticket_id)},
            {"$set": {
                "status": "closed",
                "closed_at": datetime.now(timezone.utc),
                "closed_by": closed_by,
                "close_reason": reason
            }}
        )

        return result.modified_count > 0

    def get_dm_ticket_transcript(self, ticket_id: str) -> Optional[Dict[str, Any]]:
        """Get full transcript of a DM support ticket"""
        collection = self.db['ryzuo-dm-support-tickets']
        return collection.find_one({"_id": ObjectId(ticket_id)})






    
    def _cleanup_user_notifications(self, user_id: int) -> None:
        """Keep only the last 5 notifications for a user"""
        try:
            collection = self.db['ryzuo-user-notifications']
            
            # Get all notifications for user, sorted by creation date (newest first)
            notifications = list(collection.find(
                {"user_id": str(user_id)}
            ).sort("created_at", -1))
            
            # If more than 5 notifications, delete the oldest ones
            if len(notifications) > 5:
                notifications_to_delete = notifications[5:]  # Keep first 5, delete rest
                notification_ids = [notif['_id'] for notif in notifications_to_delete]
                
                collection.delete_many({
                    "_id": {"$in": notification_ids}
                })
                
                logger.debug(f"Cleaned up {len(notification_ids)} old notifications for user {user_id}")
                
        except Exception as e:
            logger.error(f"Error cleaning up notifications for user {user_id}: {e}")

    # Gender Verification Methods
    def set_gender_verification_settings(self, server_id: int, channel_id: int, category_id: int,
                                       support_role_id: int, paper_text: str) -> bool:
        """Save gender verification settings for a server"""
        collection = self.db['ryzuo-gender-verification']

        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "channel_id": channel_id,
                "category_id": category_id,
                "support_role_id": support_role_id,
                "paper_text": paper_text,
                "enabled": True,  # Enable by default when configuring
                "updated_at": datetime.now(timezone.utc)
            }},
            upsert=True
        )

        return result.upserted_id is not None or result.modified_count > 0
    
    def get_gender_verification_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get gender verification settings for a server"""
        collection = self.db['ryzuo-gender-verification']
        # Try both string and int server_id for compatibility
        result = collection.find_one({"server_id": str(server_id)})
        if not result:
            result = collection.find_one({"server_id": server_id})
        return result

    def update_gender_verification_settings_enabled(self, server_id: int, enabled: bool) -> bool:
        """Update gender verification system enabled status"""
        collection = self.db['ryzuo-gender-verification']

        # Try both string and int server_id for compatibility
        result = collection.update_one(
            {"server_id": str(server_id)},
            {"$set": {
                "enabled": enabled,
                "updated_at": datetime.now(timezone.utc)
            }}
        )

        if result.modified_count == 0:
            result = collection.update_one(
                {"server_id": server_id},
                {"$set": {
                    "enabled": enabled,
                    "updated_at": datetime.now(timezone.utc)
                }}
            )

        return result.modified_count > 0
    
    def create_gender_verification_ticket(self, server_id: int, user_id: int) -> str:
        """Create a new gender verification ticket with atomic duplicate prevention"""
        collection = self.db['ryzuo-gender-tickets']

        try:
            # Use atomic findOneAndUpdate to prevent race conditions
            # First check if user already has an open ticket
            existing_ticket = collection.find_one({
                "server_id": server_id,
                "user_id": user_id,
                "status": "open"
            })

            if existing_ticket:
                return "existing"

            # Check if user had a ticket closed in the last 12 hours
            recent_ticket = collection.find_one({
                "server_id": server_id,
                "user_id": user_id,
                "status": "closed",
                "closed_at": {"$gt": datetime.now(timezone.utc) - timedelta(hours=12)}
            })

            if recent_ticket:
                return "recent"

            # Create new ticket atomically
            ticket = {
                "server_id": server_id,
                "user_id": user_id,
                "channel_id": None,
                "status": "open",
                "created_at": datetime.now(timezone.utc),
                "closed_at": None,
                "created_by": "user_request"  # Add identifier to track creation source
            }

            # Double-check for existing ticket right before insertion
            existing_check = collection.find_one({
                "server_id": server_id,
                "user_id": user_id,
                "status": "open"
            })

            if existing_check:
                return "existing"

            result = collection.insert_one(ticket)
            return str(result.inserted_id)

        except Exception as e:
            logger.error(f"Error creating gender verification ticket: {e}")
            return "error"
    
    def close_gender_verification_ticket(self, ticket_id: str, channel_id: int) -> bool:
        """Close a gender verification ticket"""
        collection = self.db['ryzuo-gender-tickets']
        
        result = collection.update_one(
            {"_id": ObjectId(ticket_id)},
            {"$set": {
                "status": "closed",
                "closed_at": datetime.utcnow(),
                "channel_id": channel_id
            }}
        )
        
        return result.modified_count > 0
    
    def get_user_open_ticket(self, server_id: int, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user's open gender verification ticket"""
        collection = self.db['ryzuo-gender-tickets']
        return collection.find_one({
            "server_id": server_id,
            "user_id": user_id,
            "status": "open"
        })
    
    def cleanup_old_tickets(self):
        """Clean up tickets that have been inactive for more than 24 hours - DISABLED for gender verification tickets"""
        try:
            # Check if database is initialized by trying to access a collection
            try:
                collection = self.db.get_collection('ryzuo-gender-tickets')
            except Exception as e:
                logger.error(f"Database error: {e}")
                return 0

            # DISABLED: Gender verification tickets should NEVER automatically close
            # This function is kept for potential future use with other ticket types
            # but gender verification tickets will only close manually

            logger.debug("Gender verification ticket auto-cleanup is disabled - tickets will only close manually")
            return 0

        except Exception as e:
            logger.error(f"Error in cleanup_old_tickets: {e}", exc_info=True)
            return 0

    # Music System Methods
    def set_music_settings(self, server_id: int, dj_role_id: Optional[int] = None, enabled: bool = True) -> bool:
        """Save music system settings for a server"""
        try:
            collection = self.db['ryzuo-music-settings']

            # Ensure server_id is stored as string for consistency
            server_id_str = str(server_id)
            logger.info(f"Saving music settings for server {server_id_str}")

            settings = {
                "enabled": enabled,
                "updated_at": datetime.utcnow()
            }

            if dj_role_id is not None:
                settings["dj_role_id"] = dj_role_id

            result = collection.update_one(
                {"server_id": server_id_str},
                {"$set": settings},
                upsert=True
            )

            success = result.upserted_id is not None or result.modified_count > 0
            logger.info(f"Music settings save result: {success}")
            return success
        except Exception as e:
            logger.error(f"Error saving music settings: {e}", exc_info=True)
            return False

    def get_music_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get music system settings for a server"""
        try:
            collection = self.db['ryzuo-music-settings']
            # Try both string and int server_id for compatibility
            result = collection.find_one({"server_id": str(server_id)})
            if not result:
                result = collection.find_one({"server_id": server_id})
            return result
        except Exception as e:
            logger.error(f"Error getting music settings: {e}")
            return None

    def update_music_settings_enabled(self, server_id: int, enabled: bool) -> bool:
        """Update music system enabled status"""
        try:
            collection = self.db['ryzuo-music-settings']

            # Ensure server_id is stored as string for consistency
            server_id_str = str(server_id)

            result = collection.update_one(
                {"server_id": server_id_str},
                {"$set": {
                    "enabled": enabled,
                    "updated_at": datetime.utcnow()
                }},
                upsert=True
            )

            return result.upserted_id is not None or result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating music settings enabled status: {e}")
            return False

    def update_music_dj_role(self, server_id: int, dj_role_id: Optional[int]) -> bool:
        """Update music system DJ role"""
        try:
            collection = self.db['ryzuo-music-settings']

            # Ensure server_id is stored as string for consistency
            server_id_str = str(server_id)

            if dj_role_id is not None:
                # Set the DJ role
                result = collection.update_one(
                    {"server_id": server_id_str},
                    {"$set": {
                        "dj_role_id": dj_role_id,
                        "updated_at": datetime.utcnow()
                    }},
                    upsert=True
                )
            else:
                # Remove the DJ role
                result = collection.update_one(
                    {"server_id": server_id_str},
                    {
                        "$unset": {"dj_role_id": ""},
                        "$set": {"updated_at": datetime.utcnow()}
                    },
                    upsert=True
                )

            return result.upserted_id is not None or result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating music DJ role: {e}")
            return False

    # TempVoice Methods
    def set_tempvoice_settings(self, server_id: int, interface_channel_id: int, creator_channel_id: int, default_user_limit: Optional[int] = None) -> bool:
        """Save TempVoice settings for a server"""
        try:
            collection = self.db['ryzuo-tempvoice-settings']

            # Ensure server_id is stored as string for consistency
            server_id_str = str(server_id)
            logger.info(f"Saving tempvoice settings for server {server_id_str}")

            result = collection.update_one(
                {"server_id": server_id_str},
                {"$set": {
                    "interface_channel_id": interface_channel_id,
                    "creator_channel_id": creator_channel_id,
                    "default_user_limit": default_user_limit,
                    "enabled": True,  # Enable the system when configured
                    "updated_at": datetime.utcnow()
                }},
                upsert=True
            )

            success = result.upserted_id is not None or result.modified_count > 0
            logger.info(f"Tempvoice settings save result: {success}")
            return success
        except Exception as e:
            logger.error(f"Error saving tempvoice settings: {e}", exc_info=True)
            return False

    # ========== PREMIUM FEATURE REMOVAL METHODS ==========

    def remove_repping_config(self, server_id: int) -> bool:
        """Remove repping configuration for a server"""
        try:
            self._ensure_connected()
            # Remove from server config
            collection = self.db['ryzuo-server-configs']
            result = collection.update_one(
                {'server_id': str(server_id)},
                {'$unset': {
                    'repping_role_id': '',
                    'role_id': '',
                    'repping_enabled': '',
                    'repping_trigger_word': ''
                }}
            )
            logger.info(f"Removed repping config for server {server_id}")
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error removing repping config: {e}")
            return False

    def remove_tempvoice_config(self, server_id: int) -> bool:
        """Remove tempvoice configuration for a server"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-tempvoice-settings']
            # Use string server_id to match the format used in other methods
            result = collection.delete_one({'server_id': str(server_id)})

            # Also remove any active temp channels
            temp_channels_collection = self.db['ryzuo-temp-channels']
            temp_channels_collection.delete_many({'server_id': server_id})

            logger.info(f"Removed tempvoice config for server {server_id} (deleted {result.deleted_count} documents)")
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error removing tempvoice config: {e}")
            return False

    def remove_music_config(self, server_id: int) -> bool:
        """Remove music configuration for a server"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-music-settings']
            # Use string server_id to match the format used in other methods
            result = collection.delete_one({'server_id': str(server_id)})
            logger.info(f"Removed music config for server {server_id} (deleted {result.deleted_count} documents)")
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error removing music config: {e}")
            return False

    def remove_dm_support_config(self, server_id: int) -> bool:
        """Remove DM support configuration for a server"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-dm-support-settings']
            # Use string server_id to match the format used in other methods
            result = collection.delete_one({'server_id': str(server_id)})

            # Also remove any active tickets
            tickets_collection = self.db['ryzuo-dm-support-tickets']
            tickets_collection.delete_many({'server_id': str(server_id)})

            logger.info(f"Removed DM support config for server {server_id} (deleted {result.deleted_count} documents)")
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error removing DM support config: {e}")
            return False

    def remove_sticky_messages_config(self, server_id: int) -> bool:
        """Remove sticky messages configuration for a server"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-sticky-messages']
            # Use string server_id to match the format used in other methods
            result = collection.delete_many({'server_id': str(server_id)})

            # Also remove sticky message logs
            logs_collection = self.db['ryzuo-sticky-logs']
            logs_collection.delete_many({'server_id': str(server_id)})

            logger.info(f"Removed sticky messages config for server {server_id} (deleted {result.deleted_count} documents)")
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error removing sticky messages config: {e}")
            return False

    def remove_auto_roling_config(self, server_id: int) -> bool:
        """Remove auto-roling configuration for a server"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-auto-roling']
            # Use string server_id to match the format used in other methods
            result = collection.delete_one({'server_id': str(server_id)})
            logger.info(f"Removed auto-roling config for server {server_id} (deleted {result.deleted_count} documents)")
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error removing auto-roling config: {e}")
            return False

    def remove_vent_config(self, server_id: int) -> bool:
        """Remove vent configuration for a server"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-vent-settings']
            # Use string server_id to match the format used in other methods
            result = collection.delete_one({'server_id': str(server_id)})
            logger.info(f"Removed vent config for server {server_id} (deleted {result.deleted_count} documents)")
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error removing vent config: {e}")
            return False

    # ========== AUTO-ROLE SUB-FEATURE MANAGEMENT ==========

    def remove_reaction_roles_config(self, server_id: int) -> bool:
        """Remove reaction role configurations for a server"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-reaction-roles']
            result = collection.delete_many({'server_id': str(server_id)})
            logger.info(f"Removed {result.deleted_count} reaction role configs for server {server_id}")
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error removing reaction roles config: {e}")
            return False

    def limit_join_roles_to_free_tier(self, server_id: int) -> bool:
        """Limit join roles to free tier (1 role maximum)"""
        try:
            self._ensure_connected()

            # Get current auto-roling config
            auto_roling_collection = self.db['ryzuo-auto-roling']
            current_config = auto_roling_collection.find_one({'server_id': str(server_id)})

            if not current_config:
                return False

            # If there are multiple join roles, keep only the first one
            join_roles = current_config.get('join_roles', [])
            if isinstance(join_roles, list) and len(join_roles) > 1:
                # Keep only the first role
                limited_roles = [join_roles[0]]

                result = auto_roling_collection.update_one(
                    {'server_id': str(server_id)},
                    {'$set': {'join_roles': limited_roles, 'updated_at': datetime.now(timezone.utc)}}
                )

                logger.info(f"Limited join roles from {len(join_roles)} to 1 for server {server_id}")
                return result.modified_count > 0
            elif not isinstance(join_roles, list):
                # Convert single role to list format (legacy support)
                single_role = current_config.get('role_id')
                if single_role:
                    result = auto_roling_collection.update_one(
                        {'server_id': str(server_id)},
                        {'$set': {'join_roles': [single_role], 'updated_at': datetime.now(timezone.utc)}}
                    )
                    return result.modified_count > 0

            return False
        except Exception as e:
            logger.error(f"Error limiting join roles to free tier: {e}")
            return False

    def remove_embed_dropdown_roles_config(self, server_id: int) -> bool:
        """Remove embed dropdown role configurations for a server"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-embed-dropdown-roles']
            result = collection.delete_many({'server_id': str(server_id)})
            logger.info(f"Removed {result.deleted_count} embed dropdown role configs for server {server_id}")
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error removing embed dropdown roles config: {e}")
            return False

    def disable_auto_role_advanced_logging(self, server_id: int) -> bool:
        """Disable advanced logging for auto-roles"""
        try:
            self._ensure_connected()

            # Remove auto-role specific logging configurations
            auto_roling_collection = self.db['ryzuo-auto-roling']
            result = auto_roling_collection.update_one(
                {'server_id': str(server_id)},
                {
                    '$unset': {
                        'log_channel_id': '',
                        'log_join_roles': '',
                        'log_reaction_roles': '',
                        'log_embed_roles': '',
                        'advanced_logging_enabled': ''
                    },
                    '$set': {'updated_at': datetime.now(timezone.utc)}
                }
            )

            logger.info(f"Disabled advanced auto-role logging for server {server_id}")
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error disabling auto-role advanced logging: {e}")
            return False

    def save_multiple_join_roles(self, server_id: int, additional_roles: List[int]) -> bool:
        """Save multiple join roles configuration (premium feature)"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-auto-roling']

            result = collection.update_one(
                {'server_id': str(server_id)},
                {
                    '$set': {
                        'additional_join_roles': additional_roles,
                        'updated_at': datetime.now(timezone.utc)
                    }
                },
                upsert=True
            )

            logger.info(f"Saved multiple join roles for server {server_id}: {additional_roles}")
            return result.modified_count > 0 or result.upserted_id is not None

        except Exception as e:
            logger.error(f"Error saving multiple join roles: {e}")
            return False

    def save_offline_protection_settings(self, server_id: int, enabled: bool) -> bool:
        """Save offline protection settings (premium feature)"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-auto-roling']

            result = collection.update_one(
                {'server_id': str(server_id)},
                {
                    '$set': {
                        'offline_protection_enabled': enabled,
                        'updated_at': datetime.now(timezone.utc)
                    }
                },
                upsert=True
            )

            logger.info(f"Saved offline protection settings for server {server_id}: {enabled}")
            return result.modified_count > 0 or result.upserted_id is not None

        except Exception as e:
            logger.error(f"Error saving offline protection settings: {e}")
            return False

    def set_auto_roling_error(self, server_id: int, error_message: str) -> bool:
        """Set auto-roling error message"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-auto-roling']

            result = collection.update_one(
                {'server_id': str(server_id)},
                {
                    '$set': {
                        'permission_error': True,
                        'last_error': error_message,
                        'updated_at': datetime.now(timezone.utc)
                    }
                },
                upsert=True
            )

            logger.warning(f"Set auto-roling error for server {server_id}: {error_message}")
            return result.modified_count > 0 or result.upserted_id is not None

        except Exception as e:
            logger.error(f"Error setting auto-roling error: {e}")
            return False

    def disable_offline_protection(self, server_id: int) -> bool:
        """Disable offline protection (premium feature)"""
        try:
            self._ensure_connected()
            collection = self.db['ryzuo-auto-roling']

            result = collection.update_one(
                {'server_id': str(server_id)},
                {
                    '$unset': {
                        'offline_protection_enabled': ''
                    },
                    '$set': {'updated_at': datetime.now(timezone.utc)}
                }
            )

            logger.info(f"Disabled offline protection for server {server_id}")
            return result.modified_count > 0

        except Exception as e:
            logger.error(f"Error disabling offline protection: {e}")
            return False
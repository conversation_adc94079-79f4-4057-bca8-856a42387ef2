<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Admin Panel{% endblock %} | r<PERSON><PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.0.0/dist/chart.min.css" rel="stylesheet">
    <style>
        :root {
            --admin-primary: #2c3e50;
            --admin-secondary: #34495e;
            --admin-accent: #e74c3c;
            --admin-success: #27ae60;
            --admin-warning: #f39c12;
            --admin-info: #3498db;
            --admin-light: #ecf0f1;
            --admin-dark: #2c3e50;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .admin-navbar {
            background: var(--admin-primary);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .admin-navbar .navbar-brand {
            color: white !important;
            font-weight: 700;
        }
        
        .admin-navbar .nav-link {
            color: rgba(255,255,255,0.8) !important;
            transition: color 0.3s ease;
        }
        
        .admin-navbar .nav-link:hover {
            color: white !important;
        }
        
        .admin-sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 0;
        }
        
        .sidebar-nav {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-nav li {
            border-bottom: 1px solid #eee;
        }
        
        .sidebar-nav a {
            display: block;
            padding: 15px 20px;
            color: var(--admin-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            background: var(--admin-primary);
            color: white;
            transform: translateX(5px);
        }
        
        .sidebar-nav i {
            width: 20px;
            margin-right: 10px;
        }
        
        .admin-content {
            padding: 2rem;
        }
        
        .admin-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .admin-card-header {
            background: var(--admin-primary);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            padding: 1rem 1.5rem;
            border: none;
        }
        
        .stat-card {
            background: linear-gradient(135deg, var(--admin-info), #5dade2);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, var(--admin-success), #58d68d);
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, var(--admin-warning), #f7dc6f);
        }
        
        .stat-card.danger {
            background: linear-gradient(135deg, var(--admin-accent), #ec7063);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .btn-admin {
            background: var(--admin-primary);
            border: none;
            color: white;
            padding: 8px 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-admin:hover {
            background: var(--admin-secondary);
            color: white;
            transform: translateY(-2px);
        }
        
        .btn-admin-danger {
            background: var(--admin-accent);
            border: none;
            color: white;
        }
        
        .btn-admin-danger:hover {
            background: #c0392b;
            color: white;
        }
        
        .table-admin {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .table-admin th {
            background: var(--admin-light);
            border: none;
            font-weight: 600;
            color: var(--admin-secondary);
        }
        
        .table-admin td {
            border: none;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }
        
        .alert-admin {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-disabled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-premium {
            background: #fff3cd;
            color: #856404;
        }
        
        @media (max-width: 768px) {
            .admin-sidebar {
                position: fixed;
                top: 76px;
                left: -250px;
                width: 250px;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            
            .admin-sidebar.show {
                left: 0;
            }
            
            .admin-content {
                margin-left: 0;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Admin Navbar -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('admin_dashboard') }}">
                <i class="fas fa-shield-alt me-2"></i>
                ryzuo Admin
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#adminNavbar">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="adminNavbar">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            {{ session.admin_user.username if session.admin_user else 'Admin' }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('admin_logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 admin-sidebar">
                <ul class="sidebar-nav">
                    <li><a href="{{ url_for('admin_dashboard') }}" class="{{ 'active' if request.endpoint == 'admin_dashboard' }}">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a></li>
                    <li><a href="{{ url_for('admin_users') }}" class="{{ 'active' if request.endpoint == 'admin_users' }}">
                        <i class="fas fa-users"></i>User Management
                    </a></li>
                    <li><a href="{{ url_for('admin_servers') }}" class="{{ 'active' if request.endpoint == 'admin_servers' }}">
                        <i class="fas fa-server"></i>Server Management
                    </a></li>
                    <li><a href="{{ url_for('admin_dashboard') }}#feature-toggles" class="{{ 'active' if request.endpoint == 'admin_dashboard' and request.fragment == 'feature-toggles' }}">
                        <i class="fas fa-toggle-on"></i>Feature Toggles
                    </a></li>
                    <li><a href="{{ url_for('admin_environment') }}" class="{{ 'active' if request.endpoint == 'admin_environment' }}">
                        <i class="fas fa-cog"></i>Environment Config
                    </a></li>
                    <li><a href="{{ url_for('admin_processes') }}" class="{{ 'active' if request.endpoint == 'admin_processes' }}">
                        <i class="fas fa-microchip"></i>Process Management
                    </a></li>
                    <li><a href="{{ url_for('admin_security') }}" class="{{ 'active' if request.endpoint == 'admin_security' }}">
                        <i class="fas fa-shield-alt"></i>Security & Blocking
                    </a></li>
                    <li><a href="{{ url_for('admin_pricing') }}" class="{{ 'active' if request.endpoint == 'admin_pricing' }}">
                        <i class="fas fa-dollar-sign"></i>Pricing Management
                    </a></li>
                    <li><a href="{{ url_for('admin_premium_features') }}" class="{{ 'active' if request.endpoint == 'admin_premium_features' }}">
                        <i class="fas fa-crown"></i>Premium Features
                    </a></li>
                    <li><a href="{{ url_for('admin_dashboard') }}#notifications" class="{{ 'active' if request.endpoint == 'admin_dashboard' and request.fragment == 'notifications' }}">
                        <i class="fas fa-bell"></i>Notifications
                    </a></li>
                    <li><a href="{{ url_for('admin_logs') }}" class="{{ 'active' if request.endpoint == 'admin_logs' }}">
                        <i class="fas fa-file-alt"></i>Admin Logs
                    </a></li>
                    <li><a class="nav-link {% if request.endpoint == 'admin_notifications' %}active{% endif %}" href="{{ url_for('admin_notifications') }}">
                        <i class="fas fa-bell"></i> Notifications
                    </a></li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 admin-content">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else 'warning' if category == 'warning' else 'success' }} alert-dismissible fade show alert-admin" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' if category == 'warning' else 'check-circle' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.0.0/dist/chart.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>

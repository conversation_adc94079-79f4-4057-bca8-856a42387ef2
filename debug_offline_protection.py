#!/usr/bin/env python3
"""
Debug script to check offline protection settings in database
"""

from pymongo import MongoClient

# Connect to MongoDB
client = MongoClient("mongodb://localhost:27017/")
db = client["ryzuo"]

# Your server ID
server_id = "1393129436974551050"

print("Debugging Offline Protection Settings")
print("=" * 50)

# Check auto-roling settings
collection = db['ryzuo-auto-roling']
auto_settings = collection.find_one({"server_id": server_id})

print(f"Auto-roling settings for server {server_id}:")
print(f"Raw data: {auto_settings}")

if auto_settings:
    offline_protection = auto_settings.get('offline_protection_enabled')
    print(f"\noffline_protection_enabled value: {offline_protection}")
    print(f"Type: {type(offline_protection)}")
    print(f"Boolean evaluation: {bool(offline_protection)}")
    
    # Check if the field exists at all
    if 'offline_protection_enabled' in auto_settings:
        print("✅ offline_protection_enabled field EXISTS in database")
    else:
        print("❌ offline_protection_enabled field MISSING from database")
        
    # Test the logic
    result = auto_settings.get('offline_protection_enabled', False)
    print(f"get('offline_protection_enabled', False) result: {result}")
    
else:
    print("❌ No auto-roling settings found!")

print("\n" + "=" * 50)

# Let's also manually set it to False to test
print("Setting offline protection to False...")
result = collection.update_one(
    {"server_id": server_id},
    {"$set": {"offline_protection_enabled": False}},
    upsert=True
)
print(f"Update result: modified_count={result.modified_count}, upserted_id={result.upserted_id}")

# Check again
auto_settings_after = collection.find_one({"server_id": server_id})
print(f"After setting to False: {auto_settings_after.get('offline_protection_enabled')}")

print("\nDebugging completed!")

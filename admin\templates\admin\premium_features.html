{% extends "admin/base.html" %}

{% block title %}Premium Features Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-crown me-2"></i>
        Premium Features Management
    </h1>
    <div>
        <button class="btn btn-admin" onclick="saveAllFeatures()">
            <i class="fas fa-save me-2"></i>
            Save Changes
        </button>
    </div>
</div>

<div class="alert alert-info alert-admin">
    <i class="fas fa-info-circle me-2"></i>
    <strong>Note:</strong> Changes to premium features will be reflected immediately on the frontend. 
    You can mark entire features as premium or specific sub-features within a feature.
</div>

<!-- Feature Configuration -->
<div class="row">
    {% for feature_name, config in features.items() %}
    <div class="col-md-6 mb-4">
        <div class="card admin-card">
            <div class="card-header admin-card-header">
                <h5 class="mb-0">
                    <i class="fas fa-{{ 'cog' if feature_name == 'auto_roling' else 'comments' if feature_name == 'repping' else 'volume-up' if feature_name == 'music' else 'microphone' if feature_name == 'tempvoice' else 'heart' if feature_name == 'vent' else 'thumbtack' if feature_name == 'sticky_messages' else 'gift' if feature_name == 'giveaways' else 'envelope' if feature_name == 'dm_support' else 'chart-bar' if feature_name == 'statistics' else 'list' if feature_name == 'on-site-logs' else 'puzzle-piece' }} me-2"></i>
                    {{ feature_name.replace('_', ' ').title() }}
                </h5>
            </div>
            <div class="card-body">
                <!-- Main Feature Premium Toggle -->
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" 
                           id="feature_{{ feature_name }}" 
                           name="feature_{{ feature_name }}"
                           {{ 'checked' if config.is_premium else '' }}>
                    <label class="form-check-label" for="feature_{{ feature_name }}">
                        <strong>Entire Feature is Premium</strong>
                    </label>
                </div>

                <!-- Sub-features Section -->
                <div class="mt-3">
                    <h6 class="text-muted mb-2">Premium Sub-features:</h6>
                    <div class="sub-features-container">
                        {% set default_subfeatures = {
                            'auto_roling': ['reaction_roles', 'multiple_join_roles', 'embed_dropdown_roles', 'advanced_logging'],
                            'repping': ['custom_rewards', 'advanced_tracking', 'role_stacking'],
                            'tempvoice': ['custom_permissions', 'advanced_settings', 'multiple_channels'],
                            'music': ['premium_quality', 'playlist_management', 'advanced_filters'],
                            'vent': ['custom_categories', 'advanced_moderation', 'analytics'],
                            'sticky_messages': ['multiple_messages', 'advanced_formatting', 'scheduling'],
                            'giveaways': ['advanced_requirements', 'multiple_winners', 'analytics'],
                            'dm_support': ['priority_support', 'advanced_routing', 'analytics']
                        } %}
                        
                        {% set available_subfeatures = default_subfeatures.get(feature_name, []) %}
                        {% for subfeature in available_subfeatures %}
                        <div class="form-check mb-2">
                            <input class="form-check-input subfeature-checkbox" type="checkbox" 
                                   id="subfeature_{{ feature_name }}_{{ subfeature }}" 
                                   name="subfeature_{{ feature_name }}_{{ subfeature }}"
                                   data-feature="{{ feature_name }}"
                                   data-subfeature="{{ subfeature }}"
                                   {{ 'checked' if subfeature in config.premium_subfeatures else '' }}>
                            <label class="form-check-label" for="subfeature_{{ feature_name }}_{{ subfeature }}">
                                {{ subfeature.replace('_', ' ').title() }}
                            </label>
                        </div>
                        {% endfor %}
                        
                        {% if not available_subfeatures %}
                        <p class="text-muted small">No sub-features available for this feature.</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Feature Description -->
                <div class="mt-3">
                    <small class="text-muted">
                        {% if feature_name == 'auto_roling' %}
                        Free: 1 auto-role on join. Premium: Reaction roles (20/message, 10 messages max), Multiple join roles (5 max), Embed dropdown roles (3 embeds, 25 roles/embed).
                        {% elif feature_name == 'repping' %}
                        Allow users to gain reputation and rewards through activity tracking.
                        {% elif feature_name == 'music' %}
                        High-quality music playback with advanced controls and features.
                        {% elif feature_name == 'tempvoice' %}
                        Create temporary voice channels with customizable settings.
                        {% elif feature_name == 'vent' %}
                        Provide anonymous venting channels with moderation tools.
                        {% elif feature_name == 'sticky_messages' %}
                        Pin important messages that stay visible in channels.
                        {% elif feature_name == 'giveaways' %}
                        Create and manage server giveaways with various requirements.
                        {% elif feature_name == 'dm_support' %}
                        Handle support tickets through direct messages.
                        {% elif feature_name == 'statistics' %}
                        View detailed server statistics and analytics.
                        {% elif feature_name == 'on-site-logs' %}
                        Access comprehensive logging through the dashboard.
                        {% else %}
                        {{ config.description or 'No description available.' }}
                        {% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Auto-Role Limits Configuration -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sliders-h me-2"></i>
                    Auto-Role Limits Configuration
                </h5>
            </div>
            <div class="card-body">
                <form id="autoRoleLimitsForm">
                    <div class="row">
                        <!-- Free User Limits -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">Free User Limits</h6>

                            <div class="mb-3">
                                <label for="free_join_roles" class="form-label">Join Roles (Free)</label>
                                <input type="number" class="form-control" id="free_join_roles" name="free_join_roles"
                                       value="1" min="0" max="10">
                                <small class="text-muted">Maximum auto-roles on join for free users</small>
                            </div>

                            <div class="mb-3">
                                <label for="free_reaction_roles" class="form-label">Reaction Roles (Free)</label>
                                <input type="number" class="form-control" id="free_reaction_roles" name="free_reaction_roles"
                                       value="0" min="0" max="50">
                                <small class="text-muted">Maximum reaction roles for free users</small>
                            </div>

                            <div class="mb-3">
                                <label for="free_embed_dropdowns" class="form-label">Embed Dropdowns (Free)</label>
                                <input type="number" class="form-control" id="free_embed_dropdowns" name="free_embed_dropdowns"
                                       value="0" min="0" max="10">
                                <small class="text-muted">Maximum embed dropdown menus for free users</small>
                            </div>
                        </div>

                        <!-- Premium User Limits -->
                        <div class="col-md-6">
                            <h6 class="text-warning mb-3">Premium User Limits</h6>

                            <div class="mb-3">
                                <label for="premium_join_roles" class="form-label">Join Roles (Premium)</label>
                                <input type="number" class="form-control" id="premium_join_roles" name="premium_join_roles"
                                       value="5" min="0" max="25">
                                <small class="text-muted">Maximum auto-roles on join for premium users</small>
                            </div>

                            <div class="mb-3">
                                <label for="premium_reaction_roles_per_message" class="form-label">Reaction Roles per Message</label>
                                <input type="number" class="form-control" id="premium_reaction_roles_per_message" name="premium_reaction_roles_per_message"
                                       value="20" min="0" max="50">
                                <small class="text-muted">Maximum reaction roles per message for premium users</small>
                            </div>

                            <div class="mb-3">
                                <label for="premium_reaction_messages" class="form-label">Reaction Role Messages</label>
                                <input type="number" class="form-control" id="premium_reaction_messages" name="premium_reaction_messages"
                                       value="10" min="0" max="50">
                                <small class="text-muted">Maximum reaction role messages for premium users</small>
                            </div>

                            <div class="mb-3">
                                <label for="premium_embed_dropdowns" class="form-label">Embed Dropdowns (Premium)</label>
                                <input type="number" class="form-control" id="premium_embed_dropdowns" name="premium_embed_dropdowns"
                                       value="3" min="0" max="10">
                                <small class="text-muted">Maximum embed dropdown menus for premium users</small>
                            </div>

                            <div class="mb-3">
                                <label for="premium_roles_per_dropdown" class="form-label">Roles per Dropdown</label>
                                <input type="number" class="form-control" id="premium_roles_per_dropdown" name="premium_roles_per_dropdown"
                                       value="25" min="0" max="25">
                                <small class="text-muted">Maximum roles per dropdown menu (Discord limit: 25)</small>
                            </div>
                        </div>
                    </div>

                    <div class="text-end mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Save Auto-Role Limits
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Save all feature configurations
function saveAllFeatures() {
    const features = {};
    
    // Get all feature checkboxes
    const featureCheckboxes = document.querySelectorAll('input[id^="feature_"]');
    featureCheckboxes.forEach(checkbox => {
        const featureName = checkbox.id.replace('feature_', '');
        features[featureName] = {
            is_premium: checkbox.checked,
            premium_subfeatures: []
        };
    });
    
    // Get all sub-feature checkboxes
    const subfeatureCheckboxes = document.querySelectorAll('.subfeature-checkbox');
    subfeatureCheckboxes.forEach(checkbox => {
        if (checkbox.checked) {
            const featureName = checkbox.dataset.feature;
            const subfeatureName = checkbox.dataset.subfeature;
            
            if (features[featureName]) {
                features[featureName].premium_subfeatures.push(subfeatureName);
            }
        }
    });
    
    // Send to server
    fetch('/api/admin/premium-features', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            features: features
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                Premium features updated successfully!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // Insert at top of page
            const container = document.querySelector('.container-fluid');
            container.insertBefore(alertDiv, container.firstChild);

            // Auto-dismiss after 3 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);

            // Update UI state to reflect changes without reload
            updateUIState(features);
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving premium features');
    });
}

function updateUIState(features) {
    // Update the UI to reflect the new state without reloading
    Object.keys(features).forEach(featureName => {
        const checkbox = document.getElementById(`feature_${featureName}`);
        if (checkbox) {
            checkbox.checked = features[featureName].is_premium;
        }

        // Update sub-features
        if (features[featureName].premium_subfeatures) {
            features[featureName].premium_subfeatures.forEach(subfeature => {
                const subCheckbox = document.querySelector(`input[data-feature="${featureName}"][value="${subfeature}"]`);
                if (subCheckbox) {
                    subCheckbox.checked = true;
                }
            });
        }
    });

    // Trigger the sub-feature update logic
    const featureCheckboxes = document.querySelectorAll('input[id^="feature_"]');
    featureCheckboxes.forEach(checkbox => {
        const event = new Event('change');
        checkbox.dispatchEvent(event);
    });
}

// Handle auto-role limits form submission
document.getElementById('autoRoleLimitsForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const limits = {};

    for (let [key, value] of formData.entries()) {
        limits[key] = parseInt(value);
    }

    fetch('/api/admin/auto-role-limits', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify(limits)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                Auto-role limits updated successfully!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // Insert at top of form
            const form = document.getElementById('autoRoleLimitsForm');
            form.insertBefore(alertDiv, form.firstChild);

            // Auto-dismiss after 3 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving auto-role limits');
    });
});

// Load current auto-role limits on page load
document.addEventListener('DOMContentLoaded', function() {
    fetch('/api/admin/auto-role-limits')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.limits) {
            // Populate form with current limits
            Object.keys(data.limits).forEach(key => {
                const input = document.getElementById(key);
                if (input) {
                    input.value = data.limits[key];
                }
            });
        }
    })
    .catch(error => {
        console.error('Error loading auto-role limits:', error);
    });
});

// Add logic to disable sub-features when main feature is premium
document.addEventListener('DOMContentLoaded', function() {
    const featureCheckboxes = document.querySelectorAll('input[id^="feature_"]');
    
    featureCheckboxes.forEach(checkbox => {
        const featureName = checkbox.id.replace('feature_', '');
        const subfeatureCheckboxes = document.querySelectorAll(`input[data-feature="${featureName}"]`);
        
        function updateSubfeatures() {
            subfeatureCheckboxes.forEach(subCheckbox => {
                if (checkbox.checked) {
                    // If main feature is premium, disable sub-features
                    subCheckbox.disabled = true;
                    subCheckbox.checked = false;
                } else {
                    // If main feature is free, enable sub-features
                    subCheckbox.disabled = false;
                }
            });
        }
        
        // Initial state
        updateSubfeatures();
        
        // Update on change
        checkbox.addEventListener('change', updateSubfeatures);
    });
});
</script>

{% endblock %}

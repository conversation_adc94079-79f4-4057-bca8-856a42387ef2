import discord
from discord.ext import commands
import logging
from datetime import datetime, timezone
import os

logger = logging.getLogger(__name__)

class EventHandlers(commands.Cog):
    """Handles Discord events for the bot"""
    
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
        self.SHARD_ID = int(os.getenv('SHARD_ID'))
    
    @commands.Cog.listener()
    async def on_guild_join(self, guild):
        """Handle bot joining a new guild"""
        logger.info(f"Shard {self.SHARD_ID} - <PERSON><PERSON> joined guild: {guild.name} ({guild.id}) with {guild.member_count} members")

        # Report to shard manager
        await self.bot.shard_manager.report_guild_join(guild)

    @commands.Cog.listener()
    async def on_guild_remove(self, guild):
        """Handle bot leaving a guild"""
        logger.info(f"Shard {self.SHARD_ID} - Bo<PERSON> left guild: {guild.name} ({guild.id})")

        # Report to shard manager
        await self.bot.shard_manager.report_guild_leave(guild)

        # Server tracking cleanup moved to repping cog

    @commands.Cog.listener()
    async def on_message(self, message):
        """Handle DM messages for support tickets, support channel responses, and sticky messages"""
        try:
            # Log all messages for debugging - this should show EVERY message the bot sees
            print(f"[DEBUG] Message received from {message.author} in {type(message.channel).__name__}: {message.content[:50]}")
            logger.info(f"Received message from {message.author} in {type(message.channel).__name__}: {message.content[:50]}")

            # Ignore messages from the bot itself
            if message.author == self.bot.user:
                logger.info(f"[DEBUG] Ignoring message from bot itself")
                return

            # Handle DM messages for support tickets
            if isinstance(message.channel, discord.DMChannel):
                logger.info(f"[DEBUG] Processing DM from {message.author}")
                await self._handle_dm_message(message)
                return

            # Handle support channel messages and sticky messages in guild channels
            if isinstance(message.channel, discord.TextChannel):
                # Check if this is a support channel first (ignore bot messages for support)
                ticket = self.db.get_dm_ticket_by_channel(message.channel.id)
                if ticket:
                    # Ignore bot messages in support channels
                    if message.author.bot:
                        logger.info(f"[STICKY DEBUG] Ignoring bot message in support channel: {message.author}")
                        return

                    # Handle close command
                    if message.content.startswith('=close '):
                        await self._handle_close_ticket_command(message, ticket)
                        return

                    # Forward message to user DM
                    await self._handle_support_response(message, ticket)
                    return

                # Handle sticky messages (for ALL messages including bot messages, but not support channels)
                if message.author.bot:
                    logger.info(f"[STICKY DEBUG] About to call handle_sticky_messages for bot message from {message.author} in {message.channel.name}")
                await self._handle_sticky_messages(message)
                if message.author.bot:
                    logger.info(f"[STICKY DEBUG] Finished calling handle_sticky_messages for bot message from {message.author}")
                return

            # Process other commands
            await self.bot.process_commands(message)
        except Exception as e:
            logger.error(f"Error in on_message handler: {e}", exc_info=True)

    @commands.Cog.listener()
    async def on_message_delete(self, message):
        """Handle message deletion - clean up giveaways if their message is deleted"""
        try:
            # Check if this was a giveaway message
            giveaway = self.db.get_giveaway_by_message(message.id)
            if giveaway:
                logger.info(f"Giveaway message {message.id} was deleted, cleaning up giveaway {giveaway['_id']}")

                # Delete the giveaway from database
                success = self.db.delete_giveaway(giveaway['_id'])
                if success:
                    logger.info(f"Successfully cleaned up giveaway {giveaway['_id']} after message deletion")
                else:
                    logger.error(f"Failed to clean up giveaway {giveaway['_id']} after message deletion")

        except Exception as e:
            logger.error(f"Error handling message deletion for giveaway cleanup: {e}")

    @commands.Cog.listener()
    async def on_channel_delete(self, channel):
        """Handle channel deletion and auto-close related tickets"""
        try:
            if not hasattr(self.bot, 'db') or not self.bot.db:
                return

            # Check if this channel had any open tickets
            closed_count = 0

            # Check DM support tickets
            dm_collection = self.bot.db.db['ryzuo-dm-tickets']
            dm_tickets = list(dm_collection.find({
                "channel_id": channel.id,
                "status": "open"
            }))

            for ticket in dm_tickets:
                dm_collection.update_one(
                    {"_id": ticket["_id"]},
                    {"$set": {"status": "closed", "closed_at": datetime.now(timezone.utc), "closed_reason": "Channel deleted"}}
                )
                closed_count += 1

            if closed_count > 0:
                logger.info(f"Auto-closed {closed_count} tickets due to channel deletion: {channel.name} ({channel.id})")

        except Exception as e:
            logger.error(f"Error in on_channel_delete: {e}", exc_info=True)

    @commands.Cog.listener()
    async def on_voice_state_update(self, member, before, after):
        """Handle voice state updates for TempVoice management and logging"""
        try:
            # First, handle voice state logging (always log regardless of license/system status)
            await self._log_voice_state_changes(member, before, after)

            # Skip TempVoice functionality if server owner doesn't have subscription
            if member.guild.owner_id and not self.bot.db.is_server_premium_for_user(member.guild.id, member.guild.owner_id):
                logger.debug(f"Skipping TempVoice for {member.guild.name} - no premium subscription")
                return

            # Handle user leaving a voice channel
            if before.channel and not after.channel:
                logger.info(f"{member.display_name} left voice channel {before.channel.name} (ID: {before.channel.id}) in {member.guild.name}")
                await self._handle_user_left_voice(member, before.channel)

            # Handle user joining a voice channel
            elif not before.channel and after.channel:
                logger.info(f"{member.display_name} joined voice channel {after.channel.name} (ID: {after.channel.id}) in {member.guild.name}")
                await self._handle_user_joined_voice(member, after.channel)

            # Handle user moving between channels
            elif before.channel and after.channel and before.channel != after.channel:
                logger.debug(f"{member.display_name} moved from {before.channel.name} to {after.channel.name}")
                await self._handle_user_left_voice(member, before.channel)
                await self._handle_user_joined_voice(member, after.channel)

        except Exception as e:
            logger.error(f"Error in on_voice_state_update: {e}", exc_info=True)

    @commands.Cog.listener()
    async def on_member_join(self, member):
        """Handle member joins for logging and auto-roling"""
        try:
            # Get logging system cog
            logging_cog = self.bot.get_cog('LoggingSystem')
            if not logging_cog:
                logger.error("LoggingSystem cog not found")
                return

            # Check if this is a bot being added
            if member.bot:
                embed = discord.Embed(
                    title="🤖 Bot Added",
                    description=f"Bot **{member}** was added to the server",
                    timestamp=datetime.now(timezone.utc),
                    color=discord.Color.blue()
                )
                embed.add_field(name="Account Created", value=f"<t:{int(member.created_at.timestamp())}:R>", inline=True)
                embed.set_thumbnail(url=member.display_avatar.url)
                embed.set_footer(text=f"Bot ID: {member.id}")

                await logging_cog.log_discord_event_to_channel(member.guild.id, "bot_add", embed)
                await logging_cog.log_discord_event_to_database(
                    member.guild.id, "bot_add", member.id, str(member),
                    "Bot added to server", f"Account created: {member.created_at}"
                )
            else:
                # Regular member join logging
                embed = discord.Embed(
                    title="👋 Member Joined",
                    description=f"{member.mention} joined the server",
                    timestamp=datetime.now(timezone.utc),
                    color=discord.Color.green()
                )
                embed.add_field(name="Account Created", value=f"<t:{int(member.created_at.timestamp())}:R>", inline=True)
                embed.set_thumbnail(url=member.display_avatar.url)
                embed.set_footer(text=f"User ID: {member.id}")

                await logging_cog.log_discord_event_to_channel(member.guild.id, "member_join", embed)
                await logging_cog.log_discord_event_to_database(
                    member.guild.id, "member_join", member.id, str(member),
                    "Member joined", f"Account created: {member.created_at}"
                )

            # Auto-roling is handled by the AutoRoling cog

        except Exception as e:
            logger.error(f"Error in on_member_join: {e}", exc_info=True)

    # Helper methods will be added in the next chunk
    async def _handle_dm_message(self, message):
        """Handle DM messages - placeholder for now"""
        # This will be implemented with the DM support functionality
        pass

    async def _handle_close_ticket_command(self, message, ticket):
        """Handle close ticket command - placeholder for now"""
        # This will be implemented with the DM support functionality
        pass

    async def _handle_support_response(self, message, ticket):
        """Handle support response - placeholder for now"""
        # This will be implemented with the DM support functionality
        pass

    async def _handle_sticky_messages(self, message):
        """Handle sticky messages - placeholder for now"""
        # This will be implemented with the sticky messages functionality
        pass

    async def _log_voice_state_changes(self, member, before, after):
        """Log voice state changes - placeholder for now"""
        # This will be implemented with the logging functionality
        pass

    async def _handle_user_left_voice(self, member, channel):
        """Handle user leaving voice channel - placeholder for now"""
        # This will be implemented with the temp voice functionality
        pass

    async def _handle_user_joined_voice(self, member, channel):
        """Handle user joining voice channel - placeholder for now"""
        # This will be implemented with the temp voice functionality
        pass



async def setup(bot):
    await bot.add_cog(EventHandlers(bot))

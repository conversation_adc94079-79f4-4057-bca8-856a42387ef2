{% extends "base.html" %}

{% block title %}Settings - {{ server_info.name }}{% endblock %}

{% block styles %}
<style>
    /* Modal fixes for settings page */
    .modal {
        z-index: 1055 !important;
    }

    .modal-backdrop {
        z-index: 1050 !important;
    }

    .modal-content {
        z-index: 1056 !important;
        pointer-events: auto !important;
    }

    .modal-dialog {
        z-index: 1056 !important;
        pointer-events: none;
    }

    .modal.show .modal-dialog {
        pointer-events: auto;
    }

    .config-header {
        background: linear-gradient(135deg, rgba(107, 114, 128, 0.1) 0%, rgba(75, 85, 99, 0.1) 100%);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
    }

    .config-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #6b7280, #4b5563);
    }

    .config-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .config-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
        margin-bottom: 0;
        opacity: 0.9;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        backdrop-filter: blur(20px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        border-color: #6b7280;
        box-shadow: 0 15px 40px rgba(107, 114, 128, 0.15);
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #6b7280, #4b5563);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .feature-card:hover::before {
        opacity: 1;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        {% include 'sidebar.html' %}
    </div>


    <!-- Main Content -->
    <div class="col-md-9">
        <!-- Configuration Header -->
        <div class="config-header">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h1 class="config-title">
                        <i class="fas fa-cog text-secondary"></i>
                        Server Settings
                    </h1>
                    <p class="config-subtitle">Configure general server settings and preferences</p>
                </div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Settings Overview Cards -->
        <div class="row g-4 mb-4">
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-clipboard-list fa-3x text-info mb-3"></i>
                        <h5>Logging System</h5>
                        <span class="badge bg-info">Configure in Logs</span>
                        <div class="mt-2">
                            <a href="{{ url_for('configure_logs') }}" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-cog me-1"></i>Configure
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-user-slash fa-3x text-secondary mb-3"></i>
                        <h5>Ignored Users</h5>
                        <span class="badge bg-info">{{ ignored_users|length }} users</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-3x text-success mb-3"></i>
                        <h5>Monitored Users</h5>
                        <span class="badge bg-success">{{ (server_info.member_count or 0) - ignored_users|length if (server_info.member_count or 0) >= ignored_users|length else 0 }} users</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-key fa-3x text-warning mb-3"></i>
                        <h5>License Key</h5>
                        {% if license_key %}
                        <span class="badge bg-success">Active</span>
                        {% else %}
                        <span class="badge bg-danger">No License</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <!-- Ignored Users Management -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-user-slash me-2"></i>Automod Settings</h5>
                        <span class="badge bg-secondary">{{ ignored_users|length }} ignored</span>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Ignored users</strong> are excluded from the repping system. They won't have roles automatically assigned or removed based on their status.
                        </div>

                        <form method="POST" class="mb-4">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <input type="hidden" name="action" value="add_ignored_user">
                            <div class="row g-3">
                                <div class="col-md-8">
                                    <label for="user_id" class="form-label">Add User to Ignore List</label>
                                    <input type="text" class="form-control" id="user_id" name="user_id"
                                           placeholder="Enter Discord User ID (e.g., 123456789012345678)" required>
                                    <div class="form-text">
                                        <i class="fas fa-lightbulb me-1"></i>
                                        Right-click a user â†’ Copy ID (Developer Mode must be enabled)
                                    </div>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="fas fa-user-plus me-2"></i>Add to Ignore List
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- Current Ignored Users -->
                        {% if ignored_users %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Ignored Users</h6>
                            <small class="text-muted">{{ ignored_users|length }} user(s) ignored</small>
                        </div>

                        <div class="row g-2">
                            {% for user_id in ignored_users %}
                            <div class="col-md-6">
                                <div class="card bg-dark border-secondary">
                                    <div class="card-body p-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <code class="text-info">{{ user_id }}</code>
                                                <br><small class="text-muted">User ID</small>
                                            </div>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                                <input type="hidden" name="action" value="remove_ignored_user">
                                                <input type="hidden" name="user_id" value="{{ user_id }}">
                                                <button type="submit" class="btn btn-outline-danger btn-sm"
                                                        onclick="return confirm('Remove this user from the ignore list?')"
                                                        title="Remove from ignore list">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No Ignored Users</h6>
                            <p class="text-muted mb-0">All users are currently monitored by the repping system</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-user-slash me-2"></i>About Ignored Users</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">Ignored users are:</p>
                        <ul class="mb-3">
                            <li>Excluded from the repping system</li>
                            <li>Won't get roles automatically assigned</li>
                            <li>Won't have roles removed</li>
                            <li>Useful for bots and special accounts</li>
                        </ul>
                        <p class="text-info mb-0">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>Note:</strong> All bots are automatically ignored by the system.
                        </p>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Advanced Settings</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">Additional configuration options:</p>
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('configure_repping') }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-star me-1"></i>Repping System
                            </a>
                            <a href="{{ url_for('configure_vent') }}" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-heart me-1"></i>Vent System
                            </a>
                            <a href="{{ url_for('configure_tempvoice') }}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-microphone me-1"></i>Temp Voice
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-external-link-alt me-2"></i>Quick Links</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('site_logs') }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-file-alt me-1"></i>View Activity Logs
                            </a>
                            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-tachometer-alt me-1"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transfer to Server Modal -->
<div class="modal fade" id="transferServerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header border-secondary">
                <h5 class="modal-title">
                    <i class="fas fa-server me-2"></i>Transfer License to Server
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="transferServerForm">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This will move your license to a different server. The current server will lose access to premium features.
                    </div>
                    <div class="mb-3">
                        <label for="target_server_id" class="form-label">Target Server ID</label>
                        <input type="text" class="form-control" id="target_server_id" name="target_server_id"
                               placeholder="Enter Discord Server ID" required>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Right-click the server name → Copy ID (Developer Mode required)
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirmServerTransfer" required>
                            <label class="form-check-label" for="confirmServerTransfer">
                                I understand this will remove the license from the current server
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-secondary">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-exchange-alt me-1"></i>Transfer License
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Transfer to User Modal -->
<div class="modal fade" id="transferUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header border-secondary">
                <h5 class="modal-title">
                    <i class="fas fa-user-friends me-2"></i>Transfer License to User
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="transferUserForm">
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Danger:</strong> This will transfer ownership of your license to another user. You will lose control over this license permanently.
                    </div>
                    <div class="mb-3">
                        <label for="target_user_id" class="form-label">Target User ID</label>
                        <input type="text" class="form-control" id="target_user_id" name="target_user_id"
                               placeholder="Enter Discord User ID" required>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Right-click the user → Copy ID (Developer Mode required)
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirmUserTransfer" required>
                            <label class="form-check-label" for="confirmUserTransfer">
                                I understand this will permanently transfer ownership to another user
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-secondary">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-user-friends me-1"></i>Transfer Ownership
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Store the actual license key for visibility toggle
const actualLicenseKey = '{{ license_key.key if license_key else "" }}';
let keyVisible = false;

// Load Discord data for dropdowns
document.addEventListener('DOMContentLoaded', function() {
    setupLicenseKeyManagement();


});

// License Key Management Functions
function setupLicenseKeyManagement() {
    const toggleBtn = document.getElementById('toggle-key-visibility');
    if (toggleBtn) {
        toggleBtn.addEventListener('click', toggleKeyVisibility);
    }

    // Setup transfer forms
    const transferServerForm = document.getElementById('transferServerForm');
    const transferUserForm = document.getElementById('transferUserForm');

    if (transferServerForm) {
        transferServerForm.addEventListener('submit', handleServerTransfer);
    }

    if (transferUserForm) {
        transferUserForm.addEventListener('submit', handleUserTransfer);
    }
}

function toggleKeyVisibility() {
    const keyDisplay = document.getElementById('license-key-display');
    const eyeIcon = document.getElementById('eye-icon');

    if (!keyDisplay || !eyeIcon) return;

    keyVisible = !keyVisible;

    if (keyVisible) {
        keyDisplay.value = actualLicenseKey;
        eyeIcon.className = 'fas fa-eye-slash';
    } else {
        keyDisplay.value = '••••••••••••••••••••••••••••••••';
        eyeIcon.className = 'fas fa-eye';
    }
}

function copyLicenseKey() {
    if (!actualLicenseKey) return;

    navigator.clipboard.writeText(actualLicenseKey).then(() => {
        // Show success feedback
        const btn = event.target.closest('button');
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i>';
        btn.classList.add('btn-success');
        btn.classList.remove('btn-outline-primary');

        setTimeout(() => {
            btn.innerHTML = originalHTML;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-primary');
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy license key:', err);
        alert('Failed to copy license key to clipboard');
    });
}

function showTransferServerModal() {
    const modal = new bootstrap.Modal(document.getElementById('transferServerModal'));
    modal.show();
}

function showTransferUserModal() {
    const modal = new bootstrap.Modal(document.getElementById('transferUserModal'));
    modal.show();
}

async function handleServerTransfer(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const targetServerId = formData.get('target_server_id');

    if (!targetServerId) {
        alert('Please enter a target server ID');
        return;
    }

    try {
        const response = await fetch('{{ url_for("transfer_license_server") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                target_server_id: targetServerId
            })
        });

        const result = await response.json();

        if (result.success) {
            alert('License transferred successfully! Redirecting...');
            window.location.reload();
        } else {
            alert('Transfer failed: ' + (result.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Transfer error:', error);
        alert('An error occurred during transfer');
    }
}

async function handleUserTransfer(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const targetUserId = formData.get('target_user_id');

    if (!targetUserId) {
        alert('Please enter a target user ID');
        return;
    }

    try {
        const response = await fetch('{{ url_for("transfer_license_user") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                target_user_id: targetUserId
            })
        });

        const result = await response.json();

        if (result.success) {
            alert('License ownership transferred successfully! You will be redirected...');
            window.location.href = '{{ url_for("select_server") }}';
        } else {
            alert('Transfer failed: ' + (result.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Transfer error:', error);
        alert('An error occurred during transfer');
    }
}


</script>
{% endblock %}

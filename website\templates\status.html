{% extends "base.html" %}

{% block title %}Status - r<PERSON><PERSON><PERSON>{% endblock %}

{% block body_attributes %} data-page="status"{% endblock %}

{% block styles %}
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

    :root {
        --primary-color: #5865f2;
        --secondary-color: #7289da;
        --accent-color: #00d4ff;
        --bg-primary: #0a0a0f;
        --bg-secondary: #101218;
        --bg-tertiary: #1a1d29;
        --text-primary: #ffffff;
        --text-secondary: #b9bbbe;
        --text-muted: #72767d;
        --border-color: rgba(255, 255, 255, 0.1);
        --glow-primary: rgba(88, 101, 242, 0.5);
        --glow-accent: rgba(0, 212, 255, 0.3);
        --danger-color: #ef4444;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background: var(--bg-primary);
        color: var(--text-primary);
        overflow-x: hidden;
        line-height: 1.6;
        min-height: 100vh;
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-color: transparent;
        touch-action: manipulation;
    }

    /* Animated Background */
    .animated-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        background: linear-gradient(45deg, #0a0a0f 25%, transparent 25%),
                    linear-gradient(-45deg, #0a0a0f 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #0a0a0f 75%),
                    linear-gradient(-45deg, transparent 75%, #0a0a0f 75%);
        background-size: 60px 60px;
        background-position: 0 0, 0 30px, 30px -30px, -30px 0px;
        animation: backgroundMove 20s linear infinite;
        opacity: 0.03;
    }

    @keyframes backgroundMove {
        0% { background-position: 0 0, 0 30px, 30px -30px, -30px 0px; }
        100% { background-position: 60px 60px, 60px 90px, 90px 30px, 30px 60px; }
    }

    /* Floating Particles */
    .particles {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        overflow: hidden;
    }

    .particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
        opacity: 0.6;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
        50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
    }

    /* Main Content */
    .main-content {
        padding-top: 40px; /* Much reduced padding to move content up significantly */
        min-height: 100vh;
    }

    .status-header {
        text-align: center;
        padding: 2rem 0;
        margin-bottom: 2rem;
    }

    .status-header h1 {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 0 30px var(--glow-accent);
    }

    .status-header p {
        color: var(--text-secondary);
        font-size: 1.2rem;
        font-weight: 500;
    }

    .search-container {
        max-width: 600px;
        margin: 0 auto 3rem auto;
        position: relative;
    }

    .search-input {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: 25px;
        color: var(--text-primary);
        padding: 15px 60px 15px 25px;
        font-size: 1.1rem;
        width: 100%;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(10px);
        font-family: 'Inter', sans-serif;
        font-weight: 500;
    }

    .search-input:focus {
        background: rgba(255, 255, 255, 0.1);
        border-color: var(--accent-color);
        box-shadow: 0 0 30px var(--glow-accent);
        outline: none;
        color: var(--text-primary);
        transform: translateY(-2px);
    }

    .search-input::placeholder {
        color: var(--text-muted);
        font-weight: 400;
    }

    .search-btn {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        color: white;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 15px var(--glow-accent);
    }

    .search-btn:hover {
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 8px 25px var(--glow-accent);
    }

    .shard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        padding: 0 2rem;
        max-width: 1400px;
        margin: 0 auto;
    }

    .shard-card {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: 20px;
        padding: 2rem;
        backdrop-filter: blur(20px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .shard-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .shard-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
        border-color: rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.08);
    }

    .shard-card:hover::before {
        opacity: 1;
    }

    .shard-card.highlighted {
        border-color: var(--accent-color);
        box-shadow: 0 0 40px var(--glow-accent);
        animation: pulse 2s infinite;
        background: rgba(0, 212, 255, 0.05);
    }

    .shard-card.highlighted::before {
        opacity: 1;
    }

    @keyframes pulse {
        0%, 100% {
            box-shadow: 0 0 40px var(--glow-accent);
            transform: translateY(-8px) scale(1);
        }
        50% {
            box-shadow: 0 0 60px var(--glow-accent);
            transform: translateY(-8px) scale(1.02);
        }
    }

    .shard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .shard-title {
        font-size: 1.6rem;
        font-weight: 700;
        margin: 0;
        color: var(--text-primary);
        background: linear-gradient(135deg, var(--text-primary), var(--text-secondary));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.8rem;
        font-weight: 700;
        text-transform: uppercase;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        letter-spacing: 0.5px;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .status-operational {
        background: rgba(34, 197, 94, 0.15);
        color: #22c55e;
        border: 1px solid rgba(34, 197, 94, 0.4);
        box-shadow: 0 0 20px rgba(34, 197, 94, 0.2);
    }

    .status-connecting {
        background: rgba(251, 191, 36, 0.15);
        color: #fbbf24;
        border: 1px solid rgba(251, 191, 36, 0.4);
        box-shadow: 0 0 20px rgba(251, 191, 36, 0.2);
    }

    .status-offline {
        background: rgba(239, 68, 68, 0.15);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.4);
        box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
    }

    .status-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: currentColor;
        animation: statusPulse 2s ease-in-out infinite;
    }

    @keyframes statusPulse {
        0%, 100% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.7; transform: scale(1.2); }
    }

    .shard-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    .stat-item {
        text-align: center;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.03);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.05);
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        background: rgba(255, 255, 255, 0.05);
        transform: translateY(-2px);
    }

    .stat-label {
        font-size: 0.9rem;
        color: var(--text-muted);
        margin-bottom: 0.5rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stat-value {
        font-size: 1.4rem;
        font-weight: 800;
        color: var(--text-primary);
        background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .last-seen {
        font-size: 0.85rem;
        color: var(--text-muted);
        margin-top: 1.5rem;
        text-align: center;
        padding: 0.75rem;
        background: rgba(255, 255, 255, 0.02);
        border-radius: 10px;
        border: 1px solid rgba(255, 255, 255, 0.05);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .loading {
        text-align: center;
        padding: 4rem;
        font-size: 1.3rem;
        color: var(--text-secondary);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .loading i {
        font-size: 2rem;
        color: var(--accent-color);
    }

    .error {
        text-align: center;
        padding: 4rem;
        color: var(--danger-color);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        font-size: 1.2rem;
    }

    .error i {
        font-size: 2.5rem;
    }

    .refresh-btn {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
        border: none;
        border-radius: 50%;
        width: 70px;
        height: 70px;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        box-shadow: 0 8px 25px var(--glow-accent);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }

    .refresh-btn:hover {
        transform: scale(1.1) translateY(-2px);
        box-shadow: 0 12px 35px var(--glow-accent);
    }

    .refresh-btn.spinning {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .shard-grid {
            grid-template-columns: 1fr;
            padding: 0 1rem;
            gap: 1.5rem;
        }

        .status-header h1 {
            font-size: 2.5rem;
        }

        .search-container {
            margin: 0 1rem 2rem 1rem;
        }

        .refresh-btn {
            width: 60px;
            height: 60px;
            bottom: 1.5rem;
            right: 1.5rem;
        }

        .shard-card {
            padding: 1.5rem;
        }

        .shard-stats {
            gap: 1rem;
        }

        .stat-item {
            padding: 0.75rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Animated Background -->
<div class="animated-bg"></div>

<!-- Floating Particles -->
<div class="particles" id="particles"></div>

<!-- Include Navbar -->
{% include 'navbar.html' %}

<div class="main-content">
    <div class="container">
        <!-- Header -->
        <div class="status-header">
            <h1><i class="fas fa-signal me-3"></i>Status</h1>
            <p class="lead">Real-time status of all bot shards</p>
        </div>

        <!-- Search -->
        <div class="search-container">
            <input type="text" class="search-input" id="serverSearch" placeholder="Enter your Server ID">
            <button class="search-btn" onclick="searchServer()">
                <i class="fas fa-search"></i>
            </button>
        </div>

        <!-- Loading/Error States -->
        <div class="loading" id="loading">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Loading shard data...</span>
        </div>

        <div class="error" id="error" style="display: none;">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="errorMessage">Failed to load shard data</span>
        </div>

        <!-- Shard Grid -->
        <div class="shard-grid" id="shardGrid" style="display: none;">
            <!-- Shard cards will be populated here -->
        </div>
    </div>
</div>

<!-- Refresh Button -->
<button class="refresh-btn" id="refreshBtn" onclick="loadShardData()">
    <i class="fas fa-sync-alt"></i>
</button>
{% endblock %}

{% block scripts %}
    <script>
        let shardData = [];
        let refreshInterval;

        // Load shard data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadShardData();
            // Auto-refresh every 30 seconds
            refreshInterval = setInterval(loadShardData, 30000);
        });

        async function loadShardData() {
            const refreshBtn = document.getElementById('refreshBtn');
            refreshBtn.classList.add('spinning');

            try {
                const response = await fetch('/api/shards');
                const data = await response.json();

                if (data.success) {
                    shardData = data.shards;
                    displayShards(shardData);
                    hideError();
                } else {
                    showError(data.error || 'Failed to load shard data');
                }
            } catch (error) {
                console.error('Error loading shard data:', error);
                showError('Network error - please check your connection');
            } finally {
                refreshBtn.classList.remove('spinning');
            }
        }

        function displayShards(shards) {
            const grid = document.getElementById('shardGrid');
            const loading = document.getElementById('loading');
            
            loading.style.display = 'none';
            grid.style.display = 'grid';

            if (shards.length === 0) {
                grid.innerHTML = '<div class="error">No shards are currently online</div>';
                return;
            }

            grid.innerHTML = shards.map(shard => createShardCard(shard)).join('');
        }

        function createShardCard(shard) {
            const statusClass = `status-${shard.status}`;
            const uptime = formatUptime(shard.uptime);
            const lastSeen = shard.last_seen ? new Date(shard.last_seen).toLocaleString() : 'Never';

            return `
                <div class="shard-card" data-shard-id="${shard.shard_id}">
                    <div class="shard-header">
                        <h3 class="shard-title">Shard ${shard.shard_id}</h3>
                        <div class="status-badge ${statusClass}">
                            <div class="status-dot"></div>
                            ${shard.status}
                        </div>
                    </div>
                    
                    <div class="shard-stats">
                        <div class="stat-item">
                            <div class="stat-label">Uptime</div>
                            <div class="stat-value">${uptime}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Latency</div>
                            <div class="stat-value">${shard.latency}ms</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Servers</div>
                            <div class="stat-value">${shard.guild_count.toLocaleString()}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Users</div>
                            <div class="stat-value">${shard.user_count.toLocaleString()}</div>
                        </div>
                    </div>
                    
                    <div class="last-seen">
                        <i class="fas fa-clock me-1"></i>Last seen: ${lastSeen}
                    </div>
                </div>
            `;
        }



        async function searchServer() {
            const serverId = document.getElementById('serverSearch').value.trim();
            if (!serverId) return;

            // Clear previous highlights
            document.querySelectorAll('.shard-card').forEach(card => {
                card.classList.remove('highlighted');
            });

            try {
                const response = await fetch(`/api/find-server/${serverId}`);
                const data = await response.json();

                if (data.success) {
                    // Highlight the shard containing this server
                    const shardCard = document.querySelector(`[data-shard-id="${data.shard_id}"]`);
                    if (shardCard) {
                        shardCard.classList.add('highlighted');
                        shardCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                } else {
                    alert('Server not found or shard is offline');
                }
            } catch (error) {
                console.error('Error searching for server:', error);
                alert('Error searching for server');
            }
        }

        function formatUptime(seconds) {
            if (seconds < 60) return `${seconds}s`;
            if (seconds < 3600) return `${Math.floor(seconds / 60)}m`;
            if (seconds < 86400) return `${Math.floor(seconds / 3600)}h`;
            return `${Math.floor(seconds / 86400)}d`;
        }

        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('shardGrid').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('errorMessage').textContent = message;
        }

        function hideError() {
            document.getElementById('error').style.display = 'none';
        }

        // Handle Enter key in search
        document.getElementById('serverSearch').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchServer();
            }
        });

        // Create floating particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random position
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';

                // Random animation delay and duration
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';

                particlesContainer.appendChild(particle);
            }
        }

        // Initialize particles on page load
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
        });
    </script>
{% endblock %}

import discord
from discord.ext import commands, tasks
import logging
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class LoggingSystem(commands.Cog):
    """Handles all logging functionality for the bot"""
    
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
    
    async def cog_load(self):
        """Start background tasks when cog loads"""
        if not self.process_discord_log_queue.is_running():
            self.process_discord_log_queue.start()
    
    async def cog_unload(self):
        """Stop background tasks when cog unloads"""
        if self.process_discord_log_queue.is_running():
            self.process_discord_log_queue.cancel()
    
    async def log_discord_event_to_channel(self, guild_id: int, event_type: str, embed: discord.Embed):
        """Send Discord event log to configured log channel if enabled"""
        try:
            # Check if Discord logging is enabled for this event type
            is_enabled = self.db.is_log_type_enabled(guild_id, event_type, "discord")

            if not is_enabled:
                # Still try dashboard logging
                await self.log_discord_event_to_database(guild_id, event_type, 0, "System", f"Event: {event_type}", "Auto-logged event")
                return

            # Get logging configuration
            logging_config = self.db.get_logging_config(guild_id)
            log_channel_id = logging_config.get('log_channel_id')

            if not log_channel_id:
                return

            guild = self.bot.get_guild(guild_id)
            if not guild:
                logger.error(f"[DEBUG] Guild {guild_id} not found")
                return

            log_channel = guild.get_channel(log_channel_id)
            if not log_channel:
                logger.error(f"[DEBUG] Log channel {log_channel_id} not found in guild {guild_id}")
                return

            # Set embed color from configuration
            color_hex = self.db.get_log_type_color(guild_id, event_type)
            try:
                embed.color = discord.Color(int(color_hex.replace('#', ''), 16))
            except:
                embed.color = discord.Color.blue()

            await log_channel.send(embed=embed)

        except Exception as e:
            logger.error(f"Error sending Discord event log: {e}", exc_info=True)

    async def log_discord_event_to_database(self, guild_id: int, event_type: str, user_id: int, username: str,
                                           action: str, details: str = None, channel_id: int = None,
                                           target_id: int = None, extra_data: dict = None):
        """Log Discord event to database if enabled"""
        try:
            self.db.log_discord_event(
                guild_id, event_type, user_id, username, action, details,
                channel_id, target_id, extra_data
            )
        except Exception as e:
            logger.error(f"Error logging Discord event to database: {e}")

    async def log_bot_activity_to_channel(self, guild_id: int, log_type: str, user_id: int, username: str,
                                         action: str, details: str = None, channel_id: int = None):
        """Send bot activity log to configured log channel if enabled"""
        try:
            # Check if Discord logging is enabled for this log type
            is_enabled = self.db.is_log_type_enabled(guild_id, log_type, "discord")

            if not is_enabled:
                return

            # Get logging configuration
            logging_config = self.db.get_logging_config(guild_id)
            log_channel_id = logging_config.get('log_channel_id')

            if not log_channel_id:
                return

            guild = self.bot.get_guild(guild_id)
            if not guild:
                logger.error(f"[DEBUG] Guild {guild_id} not found")
                return

            log_channel = guild.get_channel(log_channel_id)
            if not log_channel:
                logger.error(f"[DEBUG] Log channel {log_channel_id} not found in guild {guild_id}")
                return

            # Create embed for bot activity
            embed = discord.Embed(
                title="🤖 Bot Activity",
                description=action,
                timestamp=datetime.now(timezone.utc)
            )

            # Set embed color from configuration
            color_hex = self.db.get_log_type_color(guild_id, log_type)
            try:
                embed.color = discord.Color(int(color_hex.replace('#', ''), 16))
            except:
                embed.color = discord.Color.blue()

            # Add user information
            user = self.bot.get_user(user_id)
            if user:
                embed.add_field(name="User", value=f"{user.mention} ({username})", inline=True)
            else:
                embed.add_field(name="User", value=username, inline=True)

            # Add details if provided
            if details:
                embed.add_field(name="Details", value=details, inline=False)

            # Add channel information if provided
            if channel_id:
                channel = guild.get_channel(channel_id)
                if channel:
                    embed.add_field(name="Channel", value=f"{channel.mention}", inline=True)

            embed.set_footer(text=f"Log Type: {log_type}")

            await log_channel.send(embed=embed)

        except Exception as e:
            logger.error(f"Error sending bot activity log: {e}", exc_info=True)

    async def log_bot_activity_comprehensive(self, guild_id: int, user_id: int, username: str, action: str,
                                           details: str = None, category: str = "general", channel_id: int = None):
        """Log bot activity to both dashboard and Discord if enabled"""
        try:
            # Log to dashboard database
            self.db.log_bot_activity(guild_id, user_id, username, action, details, category, channel_id)

            # Map category to log type for Discord logging
            category_to_log_type = {
                "config": "dashboard_updates",
                "license": "license_key_updates",
                "moderation": "moderator_logs",
                "general": "ryzuo_logs",
                "vent": "vent_logs",
                "dm_support": "ticket_logs",
                "sticky": "sticky_created",
                "sticky_deleted": "sticky_deleted",
                "giveaway": "ryzuo_logs",
                "repping": "ryzuo_logs",
                "message_sent": "ryzuo_logs",
                "message_deleted": "ryzuo_logs",
                "tempvoice": "ryzuo_logs",
                # Gender verification removed
            }

            log_type = category_to_log_type.get(category, "ryzuo_logs")

            # Also log to Discord if enabled
            await self.log_bot_activity_to_channel(guild_id, log_type, user_id, username, action, details, channel_id)

        except Exception as e:
            logger.error(f"Error in comprehensive bot activity logging: {e}")

    @tasks.loop(seconds=10)
    async def process_discord_log_queue(self):
        """Process queued Discord logs from dashboard activities"""
        try:
            # Get unprocessed Discord logs from the queue
            collection = self.db.db['ryzuo-discord-queue']
            pending_logs = collection.find({"processed": False}).limit(10)  # Process 10 at a time

            for log_doc in pending_logs:
                try:
                    # Send to Discord
                    await self.log_bot_activity_to_channel(
                        log_doc['server_id'],
                        log_doc['log_type'],
                        log_doc['user_id'],
                        log_doc['username'],
                        log_doc['action'],
                        log_doc.get('details'),
                        log_doc.get('channel_id')
                    )

                    # Mark as processed
                    collection.update_one(
                        {"_id": log_doc['_id']},
                        {"$set": {"processed": True, "processed_at": datetime.now(timezone.utc)}}
                    )

                    logger.info(f"[DEBUG] Processed Discord log queue item for server {log_doc['server_id']}")

                except Exception as e:
                    logger.error(f"Error processing Discord log queue item {log_doc.get('_id', 'unknown')}: {e}")

        except Exception as e:
            logger.error(f"Error in process_discord_log_queue: {e}", exc_info=True)

    @process_discord_log_queue.before_loop
    async def before_process_discord_log_queue(self):
        """Wait for bot to be ready before starting the task"""
        await self.bot.wait_until_ready()

async def setup(bot):
    await bot.add_cog(LoggingSystem(bot))
